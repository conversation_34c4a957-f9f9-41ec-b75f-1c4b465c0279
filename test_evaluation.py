#!/usr/bin/env python3
"""
Test script for the self-contained mesh evaluation functions.
This script validates the evaluation functions using simple synthetic meshes.
"""

import numpy as np
import sys
import os

# Test if dependencies are available
try:
    import trimesh
    import sklearn
    print("✓ All dependencies available")
except ImportError as e:
    print(f"✗ Missing dependency: {e}")
    print("Please install: pip install trimesh scikit-learn numpy")
    sys.exit(1)

# Import our evaluation functions
sys.path.append('.')
from evaluate_meshes import (
    chamfer_distance,
    sample_and_calc_chamfer,
    compute_mesh_iou_chunked,
    compute_mesh_iou_surface_sampling,
    compute_simplified_lfd,
    create_test_mesh
)

def test_chamfer_distance():
    """Test Chamfer Distance computation with known point sets."""
    print("\n=== Testing Chamfer Distance ===")
    
    # Create two simple point sets
    points1 = np.array([[0, 0, 0], [1, 0, 0], [0, 1, 0], [0, 0, 1]])
    points2 = np.array([[0, 0, 0], [1, 0, 0], [0, 1, 0], [0, 0, 1]])  # identical
    
    # Test identical point sets (should be 0)
    cd_identical = chamfer_distance(points1, points2)
    print(f"Chamfer Distance (identical): {cd_identical:.6f}")
    assert cd_identical < 1e-10, "Identical point sets should have CD ≈ 0"
    
    # Test with offset
    points2_offset = points2 + 0.1
    cd_offset = chamfer_distance(points1, points2_offset)
    print(f"Chamfer Distance (offset 0.1): {cd_offset:.6f}")
    assert cd_offset > 0, "Offset point sets should have CD > 0"
    
    print("✓ Chamfer Distance tests passed")

def test_mesh_evaluation():
    """Test mesh evaluation functions with synthetic meshes."""
    print("\n=== Testing Mesh Evaluation Functions ===")
    
    # Create test meshes
    mesh1 = create_test_mesh()
    mesh2 = create_test_mesh()
    mesh2.vertices *= 1.1  # Scale slightly
    
    print(f"Mesh 1: {mesh1.vertices.shape[0]} vertices, {mesh1.faces.shape[0]} faces")
    print(f"Mesh 2: {mesh2.vertices.shape[0]} vertices, {mesh2.faces.shape[0]} faces")
    
    # Test Chamfer Distance
    print("\n--- Chamfer Distance ---")
    cd = sample_and_calc_chamfer(mesh1, mesh2)
    print(f"Chamfer Distance: {cd:.6f}")
    assert cd >= 0, "Chamfer Distance should be non-negative"
    
    # Test IoU - both methods
    print("\n--- IoU (Chunked Method) ---")
    iou_chunked = compute_mesh_iou_chunked(mesh1, mesh2, resolution=32, chunk_size=16)
    print(f"IoU (chunked): {iou_chunked:.6f}")
    assert 0 <= iou_chunked <= 1, "IoU should be between 0 and 1"

    print("\n--- IoU (Surface Sampling Method) ---")
    iou_surface = compute_mesh_iou_surface_sampling(mesh1, mesh2, n_samples=1000)
    print(f"IoU (surface): {iou_surface:.6f}")
    assert 0 <= iou_surface <= 1, "IoU should be between 0 and 1"
    
    # Test simplified LFD
    print("\n--- Light Field Distance (simplified) ---")
    lfd = compute_simplified_lfd(mesh1, mesh2)
    print(f"LFD: {lfd:.6f}")
    assert 0 <= lfd <= 1, "Simplified LFD should be between 0 and 1"
    
    # Test with identical meshes
    print("\n--- Testing with identical meshes ---")
    cd_identical = sample_and_calc_chamfer(mesh1, mesh1)
    iou_identical_chunked = compute_mesh_iou_chunked(mesh1, mesh1, resolution=32, chunk_size=16)
    iou_identical_surface = compute_mesh_iou_surface_sampling(mesh1, mesh1, n_samples=1000)
    lfd_identical = compute_simplified_lfd(mesh1, mesh1)

    print(f"Identical meshes - CD: {cd_identical:.6f}")
    print(f"Identical meshes - IoU (chunked): {iou_identical_chunked:.6f}")
    print(f"Identical meshes - IoU (surface): {iou_identical_surface:.6f}")
    print(f"Identical meshes - LFD: {lfd_identical:.6f}")

    assert cd_identical < 0.01, "Identical meshes should have very low CD"
    assert iou_identical_chunked > 0.8, "Identical meshes should have high IoU (chunked)"
    assert iou_identical_surface > 0.5, "Identical meshes should have reasonable IoU (surface)"
    assert lfd_identical < 0.1, "Identical meshes should have low LFD"
    
    print("✓ All mesh evaluation tests passed")

def test_memory_efficiency():
    """Test memory efficiency of IoU implementations."""
    print("\n=== Testing Memory Efficiency ===")

    # Create larger test meshes to stress test memory usage
    print("Creating larger test meshes...")

    # Create a more complex mesh (sphere with higher resolution)
    mesh_large1 = trimesh.creation.icosphere(subdivisions=3)  # ~2562 vertices
    mesh_large2 = trimesh.creation.icosphere(subdivisions=3)
    mesh_large2.vertices *= 1.2  # Scale slightly

    print(f"Large mesh 1: {mesh_large1.vertices.shape[0]} vertices")
    print(f"Large mesh 2: {mesh_large2.vertices.shape[0]} vertices")

    # Test chunked method with higher resolution
    try:
        print("\n--- Testing Chunked IoU (higher resolution) ---")
        iou_chunked = compute_mesh_iou_chunked(mesh_large1, mesh_large2, resolution=64, chunk_size=16)
        print(f"Chunked IoU (res=64): {iou_chunked:.6f}")
        print("✓ Chunked method handles higher resolution")
    except Exception as e:
        print(f"✗ Chunked method failed: {e}")

    # Test surface sampling method
    try:
        print("\n--- Testing Surface Sampling IoU ---")
        iou_surface = compute_mesh_iou_surface_sampling(mesh_large1, mesh_large2, n_samples=5000)
        print(f"Surface IoU (5k samples): {iou_surface:.6f}")
        print("✓ Surface sampling method works")
    except Exception as e:
        print(f"✗ Surface sampling failed: {e}")

    print("✓ Memory efficiency tests completed")

def test_edge_cases():
    """Test edge cases and error handling."""
    print("\n=== Testing Edge Cases ===")

    # Test with very small meshes
    vertices_small = np.array([[0, 0, 0], [1, 0, 0], [0, 1, 0]])
    faces_small = np.array([[0, 1, 2]])
    mesh_small = trimesh.Trimesh(vertices=vertices_small, faces=faces_small)

    mesh_normal = create_test_mesh()

    try:
        cd = sample_and_calc_chamfer(mesh_small, mesh_normal)
        print(f"Small mesh CD: {cd:.6f}")
        print("✓ Small mesh handling works")
    except Exception as e:
        print(f"✗ Small mesh test failed: {e}")

    # Test with degenerate cases
    try:
        # Points vs mesh
        points = np.random.rand(100, 3)
        cd = chamfer_distance(points, points)
        print(f"Point cloud CD: {cd:.6f}")
        print("✓ Point cloud handling works")
    except Exception as e:
        print(f"✗ Point cloud test failed: {e}")

def main():
    """Run all tests."""
    print("Self-Contained Mesh Evaluation - Test Suite")
    print("=" * 50)
    
    try:
        test_chamfer_distance()
        test_mesh_evaluation()
        test_memory_efficiency()
        test_edge_cases()
        
        print("\n" + "=" * 50)
        print("✓ ALL TESTS PASSED")
        print("The self-contained evaluation script is working correctly!")
        print("\nYou can now run the full evaluation with:")
        print("  python evaluate_meshes.py                    # Real meshes")
        print("  python evaluate_meshes.py --test            # Test meshes")
        
    except Exception as e:
        print(f"\n✗ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
