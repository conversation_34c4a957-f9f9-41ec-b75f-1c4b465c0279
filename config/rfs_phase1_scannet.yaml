GENERAL:
  task: train  # train, test
  manual_seed: 123
  model_dir: model/rfs.py
  dataset_dir: data/scannetv2_inst.py

DATA:
  data_root: ../dataset
  dataset: scannetv2
  filename_suffix: _inst_nostuff.pth

  classes: 25
  ignore_label: -100

  scale: 50   # voxel_size = 1 / scale, scale 50(2cm)
  batch_size: 8
  full_scale: [128, 512]
  max_npoint: 250000
  mode: 4 # 4=mean

STRUCTURE:
  model_name: rfs
  m: 16 # 16 or 32
  block_residual: True
  block_reps: 2

  use_rgb: False
  use_coords: True

TRAIN:
  epochs: 256
  train_workers: 4 # data loader workers
  optim: Adam # Adam or SGD
  lr: 0.001
  step_epoch: 256
  multiplier: 0.1
  momentum: 0.9
  weight_decay: 0.0001
  save_freq: 16  # also eval_freq
  loss_weight: [1.0, 1.0, 1.0, 1.0, 1.0, 1.0] # semantic_loss, offset_norm_loss, offset_dir_loss, score_loss, z_loss, bbox_loss

  fg_thresh: 0.75
  bg_thresh: 0.25

  score_scale: 50 # the minimal voxel size is 2cm
  score_fullscale: 14
  score_mode: 4 # mean

  pretrain_path: 
  pretrain_module: []
  fix_module: []
  

GROUP:
  ### point grouping
  cluster_radius: 0.03
  cluster_meanActive: 50
  cluster_shift_meanActive: 300
  cluster_npoint_thre: 50
  
  # do not perform instance level training.
  prepare_epochs: 256
  prepare_epochs_2: 256

TEST:
  split: val
  test_epoch: 256
  test_workers: 4
  test_seed: 567

  TEST_NMS_THRESH: 0.3
  TEST_SCORE_THRESH: 0.09
  TEST_NPOINT_THRESH: 100
  sample: False

  eval: False
  eval_voxel_size: 0.047
  mesh_iou_thresh: [0.25, 0.5]
  save_semantic: True
  save_pt_offsets: False
  save_instance: False
  save_pt_angles: True
  save_mesh: False