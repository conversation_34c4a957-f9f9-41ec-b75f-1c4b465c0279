{"version": "0.2.0", "configurations": [{"name": "IoU Evaluation", "type": "debugpy-old", "request": "launch", "program": "${workspaceFolder}/evaluation/iou/eval.py", "args": ["/media/lty/data_mechine/data/test/testIoU/gt", "/media/lty/data_mechine/data/test/testIoU/pred", "--voxel_size", "0.047"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "python": "/home/<USER>/anaconda3/envs/gaussian_splatting/bin/python", "env": {"PYTHONPATH": "${workspaceFolder}"}, "justMyCode": false}, {"name": "IoU Evaluation (Custom Voxel Size)", "type": "debugpy-old", "request": "launch", "program": "${workspaceFolder}/evaluation/iou/eval.py", "args": ["/media/lty/data_mechine/data/test/testIoU/gt", "/media/lty/data_mechine/data/test/testIoU/pred", "--voxel_size", "1"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "python": "/home/<USER>/anaconda3/envs/gaussian_splatting/bin/python", "env": {"PYTHONPATH": "${workspaceFolder}"}, "justMyCode": false}, {"name": "Chamfer Distance Evaluation", "type": "debugpy-old", "request": "launch", "program": "${workspaceFolder}/evaluation/cd/eval.py", "args": ["/media/lty/data_mechine/data/test/testIoU/gt", "/media/lty/data_mechine/data/test/testIoU/pred"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "python": "/home/<USER>/anaconda3/envs/gaussian_splatting/bin/python", "env": {"PYTHONPATH": "${workspaceFolder}"}, "justMyCode": false}, {"name": "LFD Evaluation", "type": "debugpy-old", "request": "launch", "program": "${workspaceFolder}/evaluation/lfd/eval.py", "args": ["/media/lty/data_mechine/data/test/testIoU/gt", "/media/lty/data_mechine/data/test/testIoU/pred"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "python": "/home/<USER>/anaconda3/envs/gaussian_splatting/bin/python", "env": {"PYTHONPATH": "${workspaceFolder}"}, "justMyCode": false}, {"name": "PCR Evaluation", "type": "debugpy-old", "request": "launch", "program": "${workspaceFolder}/evaluation/pcr/eval.py", "args": ["/media/lty/data_mechine/data/test/testIoU/pred"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "python": "/home/<USER>/anaconda3/envs/gaussian_splatting/bin/python", "env": {"PYTHONPATH": "${workspaceFolder}"}, "justMyCode": false}, {"name": "Self-Contained Mesh Evaluation", "type": "debugpy-old", "request": "launch", "program": "${workspaceFolder}/evaluate_meshes.py", "args": [], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "python": "/home/<USER>/anaconda3/envs/gaussian_splatting/bin/python", "env": {"PYTHONPATH": "${workspaceFolder}"}, "justMyCode": false}, {"name": "Self-Contained Mesh Evaluation (Test Mode)", "type": "debugpy-old", "request": "launch", "program": "${workspaceFolder}/evaluate_meshes.py", "args": ["--test"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "python": "/home/<USER>/anaconda3/envs/gaussian_splatting/bin/python", "env": {"PYTHONPATH": "${workspaceFolder}"}, "justMyCode": false}, {"name": "Test IoU Memory Fix", "type": "debugpy-old", "request": "launch", "program": "${workspaceFolder}/test_iou_memory.py", "args": [], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "python": "/home/<USER>/anaconda3/envs/gaussian_splatting/bin/python", "env": {"PYTHONPATH": "${workspaceFolder}"}, "justMyCode": false}]}