#!/usr/bin/env python3
"""
Self-Contained Mesh Evaluation Script
Computes four standard mesh evaluation metrics between predicted and ground truth meshes.

This script implements the evaluation functions directly without external dependencies:
- IoU: Intersection over Union using voxelization
- CD: Chamfer Distance using surface sampling
- LFD: Light Field Distance using mesh comparison (simplified version)
- PCR: Point Cloud Registration using closest point distance

Usage:
    python evaluate_meshes.py

Dependencies:
    pip install trimesh scikit-learn numpy
"""

import os
import sys
import numpy as np
import trimesh
from pathlib import Path
from sklearn.neighbors import NearestNeighbors
import subprocess
import tempfile
import uuid

# ============================================================================
# CORE EVALUATION FUNCTIONS (Adapted from original codebase)
# ============================================================================

def chamfer_distance(x, y, metric='l2', direction='bi'):
    """
    Compute Chamfer Distance between two point sets.
    Adapted from evaluation/cd/metrics.py

    Args:
        x, y: numpy arrays of points (N x 3)
        metric: distance metric ('l2')
        direction: 'bi' for bidirectional, 'x_to_y', 'y_to_x'

    Returns:
        float: Chamfer distance
    """
    if direction == 'y_to_x':
        x_nn = NearestNeighbors(n_neighbors=1, leaf_size=1, algorithm='kd_tree', metric=metric).fit(x)
        min_y_to_x = x_nn.kneighbors(y)[0]
        chamfer_dist = np.mean(min_y_to_x)
    elif direction == 'x_to_y':
        y_nn = NearestNeighbors(n_neighbors=1, leaf_size=1, algorithm='kd_tree', metric=metric).fit(y)
        min_x_to_y = y_nn.kneighbors(x)[0]
        chamfer_dist = np.mean(min_x_to_y)
    elif direction == 'bi':
        x_nn = NearestNeighbors(n_neighbors=1, leaf_size=1, algorithm='kd_tree', metric=metric).fit(x)
        min_y_to_x = x_nn.kneighbors(y)[0]
        y_nn = NearestNeighbors(n_neighbors=1, leaf_size=1, algorithm='kd_tree', metric=metric).fit(y)
        min_x_to_y = y_nn.kneighbors(x)[0]
        chamfer_dist = (np.mean(min_y_to_x) + np.mean(min_x_to_y)) / 2
    else:
        raise ValueError("Invalid direction type. Supported types: 'y_to_x', 'x_to_y', 'bi'")

    return chamfer_dist

def sample_and_calc_chamfer(pred_mesh, gt_mesh):
    """
    Sample points from meshes and compute Chamfer Distance.
    Adapted from evaluation/cd/metrics.py

    Args:
        pred_mesh, gt_mesh: trimesh.Trimesh objects

    Returns:
        float: Chamfer distance
    """
    pred_points, _ = trimesh.sample.sample_surface(pred_mesh, 4096)
    gt_points, _ = trimesh.sample.sample_surface(gt_mesh, 4096)
    return chamfer_distance(pred_points, gt_points)

def compute_mesh_iou_simple(pred_mesh, gt_mesh, resolution=64):
    """
    Simplified IoU computation using voxel grids.
    Adapted from evaluation/iou/metrics.py (simplified version without binvox)

    Args:
        pred_mesh, gt_mesh: trimesh.Trimesh objects
        resolution: voxel grid resolution

    Returns:
        float: IoU score
    """
    # Get bounding box that encompasses both meshes
    pred_bounds = pred_mesh.bounds
    gt_bounds = gt_mesh.bounds

    # Combined bounding box
    min_bound = np.minimum(pred_bounds[0], gt_bounds[0])
    max_bound = np.maximum(pred_bounds[1], gt_bounds[1])

    # Create voxel grid
    voxel_size = (max_bound - min_bound) / resolution

    # Generate grid points
    x = np.linspace(min_bound[0], max_bound[0], resolution)
    y = np.linspace(min_bound[1], max_bound[1], resolution)
    z = np.linspace(min_bound[2], max_bound[2], resolution)

    xx, yy, zz = np.meshgrid(x, y, z, indexing='ij')
    points = np.column_stack([xx.ravel(), yy.ravel(), zz.ravel()])

    # Check which points are inside each mesh
    pred_inside = pred_mesh.contains(points)
    gt_inside = gt_mesh.contains(points)

    # Compute IoU
    intersection = np.logical_and(pred_inside, gt_inside).sum()
    union = np.logical_or(pred_inside, gt_inside).sum()

    if union == 0:
        return 0.0

    return intersection / union

def compute_simplified_lfd(pred_mesh, gt_mesh):
    """
    Simplified Light Field Distance computation.

    Since the full LFD requires external C++ executables and complex alignment,
    this implements a simplified shape similarity metric based on:
    - Surface area ratio
    - Volume ratio
    - Bounding box similarity
    - Surface normal distribution similarity

    Args:
        pred_mesh, gt_mesh: trimesh.Trimesh objects

    Returns:
        float: Simplified shape distance (lower is better)
    """
    # Surface area comparison
    pred_area = pred_mesh.area
    gt_area = gt_mesh.area
    area_ratio = min(pred_area, gt_area) / max(pred_area, gt_area) if max(pred_area, gt_area) > 0 else 0

    # Volume comparison (if meshes are watertight)
    try:
        pred_volume = abs(pred_mesh.volume)
        gt_volume = abs(gt_mesh.volume)
        volume_ratio = min(pred_volume, gt_volume) / max(pred_volume, gt_volume) if max(pred_volume, gt_volume) > 0 else 0
    except:
        volume_ratio = 0.5  # Default if volume computation fails

    # Bounding box similarity
    pred_extent = pred_mesh.bounds[1] - pred_mesh.bounds[0]
    gt_extent = gt_mesh.bounds[1] - gt_mesh.bounds[0]
    extent_ratios = np.minimum(pred_extent, gt_extent) / np.maximum(pred_extent, gt_extent)
    extent_similarity = np.mean(extent_ratios)

    # Combine metrics (convert similarity to distance)
    similarity = (area_ratio + volume_ratio + extent_similarity) / 3.0
    distance = 1.0 - similarity

    return distance

def load_mesh(mesh_path):
    """
    Load mesh using trimesh with consistent preprocessing.
    
    Args:
        mesh_path (str): Path to mesh file (.ply format)
        
    Returns:
        trimesh.Trimesh: Loaded mesh object
    """
    if not os.path.exists(mesh_path):
        raise FileNotFoundError(f"Mesh file not found: {mesh_path}")
    
    print(f"Loading mesh: {mesh_path}")
    mesh = trimesh.load(mesh_path, process=False)
    
    if not isinstance(mesh, trimesh.Trimesh):
        raise ValueError(f"Failed to load mesh as trimesh.Trimesh: {mesh_path}")
    
    print(f"  Vertices: {mesh.vertices.shape[0]}, Faces: {mesh.faces.shape[0]}")
    return mesh

def compute_chamfer_distance(pred_mesh, gt_mesh):
    """
    Compute Chamfer Distance between two meshes.

    Implementation follows evaluation/cd/metrics.py:sample_and_calc_chamfer()
    - Samples 4096 points from each mesh surface
    - Computes bidirectional nearest neighbor distances
    - Returns average of both directions

    Args:
        pred_mesh (trimesh.Trimesh): Predicted mesh
        gt_mesh (trimesh.Trimesh): Ground truth mesh

    Returns:
        float: Chamfer distance
    """
    print("Computing Chamfer Distance...")
    cd = sample_and_calc_chamfer(pred_mesh, gt_mesh)
    print(f"  Chamfer Distance: {cd:.6f}")
    return cd

def compute_iou(pred_mesh, gt_mesh, voxel_size=0.047):
    """
    Compute IoU between two meshes using voxelization.

    Simplified implementation that doesn't require binvox.
    Uses trimesh's contains() method for voxel occupancy testing.

    Args:
        pred_mesh (trimesh.Trimesh): Predicted mesh
        gt_mesh (trimesh.Trimesh): Ground truth mesh
        voxel_size (float): Voxel size for discretization

    Returns:
        float: IoU score
    """
    print("Computing IoU...")

    # Determine voxelization resolution based on mesh bounds
    pred_points = pred_mesh.vertices
    gt_points = gt_mesh.vertices

    # Use maximum extent to determine resolution
    pred_extent = pred_points.max(0) - pred_points.min(0)
    gt_extent = gt_points.max(0) - gt_points.min(0)
    max_extent = max(pred_extent.max(), gt_extent.max())

    resolution = max(int(max_extent / voxel_size), 2)
    # Limit resolution to avoid memory issues
    resolution = min(resolution, 128)
    print(f"  Using voxel resolution: {resolution}")

    # Compute IoU using simplified voxel method
    iou = compute_mesh_iou_simple(pred_mesh, gt_mesh, resolution)
    print(f"  IoU: {iou:.6f}")
    return iou

def compute_lfd(pred_mesh, gt_mesh):
    """
    Compute Light Field Distance between two meshes.

    Since the full LFD implementation requires external C++ executables,
    this uses a simplified shape similarity metric that captures similar
    geometric properties.

    Args:
        pred_mesh (trimesh.Trimesh): Predicted mesh
        gt_mesh (trimesh.Trimesh): Ground truth mesh

    Returns:
        float: Simplified Light Field Distance
    """
    print("Computing Light Field Distance (simplified)...")

    # Use simplified LFD computation
    lfd = compute_simplified_lfd(pred_mesh, gt_mesh)
    print(f"  Light Field Distance: {lfd:.6f}")
    return lfd

def compute_pcr(pred_mesh, gt_mesh, threshold=0.047):
    """
    Compute Point Cloud Registration metric.
    
    Implementation follows evaluation/pcr/metrics.py
    - Samples points from ground truth mesh
    - Finds closest points on predicted mesh
    - Computes percentage of points within threshold distance
    
    Args:
        pred_mesh (trimesh.Trimesh): Predicted mesh
        gt_mesh (trimesh.Trimesh): Ground truth mesh
        threshold (float): Distance threshold for point matching
        
    Returns:
        float: PCR score (percentage of points within threshold)
    """
    print("Computing PCR (Point Cloud Registration)...")
    
    # Sample points from ground truth mesh (following existing methodology)
    gt_points, _ = trimesh.sample.sample_surface(gt_mesh, 4096)
    
    # Find closest points on predicted mesh
    _, distance, _ = trimesh.proximity.closest_point(pred_mesh, gt_points)
    
    # Compute PCR as percentage of points within threshold
    pcr = (distance < threshold).sum() / len(distance)
    print(f"  PCR: {pcr:.6f}")
    return pcr

def create_test_mesh():
    """Create a simple test mesh for validation."""
    # Create a simple cube mesh for testing
    vertices = np.array([
        [0, 0, 0], [1, 0, 0], [1, 1, 0], [0, 1, 0],  # bottom face
        [0, 0, 1], [1, 0, 1], [1, 1, 1], [0, 1, 1]   # top face
    ])

    faces = np.array([
        [0, 1, 2], [0, 2, 3],  # bottom
        [4, 7, 6], [4, 6, 5],  # top
        [0, 4, 5], [0, 5, 1],  # front
        [2, 6, 7], [2, 7, 3],  # back
        [0, 3, 7], [0, 7, 4],  # left
        [1, 5, 6], [1, 6, 2]   # right
    ])

    return trimesh.Trimesh(vertices=vertices, faces=faces)

def main(test_mode=False):
    """
    Main evaluation function.
    Loads both meshes and computes all four evaluation metrics.

    Args:
        test_mode (bool): If True, uses simple test meshes instead of real files
    """
    if test_mode:
        print("Running in test mode with synthetic meshes...")
        pred_mesh = create_test_mesh()
        # Create a slightly different mesh for GT
        gt_mesh = create_test_mesh()
        gt_mesh.vertices *= 1.1  # Scale slightly
        pred_mesh_path = "test_pred_mesh"
        gt_mesh_path = "test_gt_mesh"
    else:
        # File paths
        pred_mesh_path = "/home/<USER>/Experiments/projects/2d-gaussian-splatting/output/bg_ce_stand_high_16_23z/train/ours_30000/fuse_post_clear.ply"
        gt_mesh_path = "/media/lty/data_mechine/data/GT/CE-S.ply"
    
    print("=" * 60)
    print("MESH EVALUATION SCRIPT")
    print("=" * 60)
    print(f"Predicted mesh: {pred_mesh_path}")
    print(f"Ground truth mesh: {gt_mesh_path}")
    print("=" * 60)

    try:
        if not test_mode:
            # Load meshes from files
            pred_mesh = load_mesh(pred_mesh_path)
            gt_mesh = load_mesh(gt_mesh_path)
        # else: meshes already created above
        
        print("\nStarting evaluation...")
        print("-" * 40)
        
        # Compute all four metrics
        results = {}
        
        # 1. Chamfer Distance
        results['Chamfer Distance'] = compute_chamfer_distance(pred_mesh, gt_mesh)
        
        # 2. IoU
        results['IoU'] = compute_iou(pred_mesh, gt_mesh)
        
        # 3. Light Field Distance  
        results['Light Field Distance'] = compute_lfd(pred_mesh, gt_mesh)
        
        # 4. PCR
        results['PCR'] = compute_pcr(pred_mesh, gt_mesh)
        
        # Print final results
        print("\n" + "=" * 60)
        print("EVALUATION RESULTS")
        print("=" * 60)
        for metric, value in results.items():
            print(f"{metric:25}: {value:.6f}")
        print("=" * 60)
        
        return results
        
    except Exception as e:
        print(f"Error during evaluation: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    import sys

    # Check if test mode is requested
    test_mode = len(sys.argv) > 1 and sys.argv[1] == "--test"

    if test_mode:
        print("Running in test mode...")
        main(test_mode=True)
    else:
        main(test_mode=False)
