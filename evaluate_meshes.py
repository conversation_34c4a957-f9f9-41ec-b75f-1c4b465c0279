#!/usr/bin/env python3
"""
Comprehensive Mesh Evaluation Script
Computes four standard mesh evaluation metrics between predicted and ground truth meshes.

This script follows the existing codebase methodology from the evaluation modules:
- IoU: Intersection over Union using voxelization (evaluation/iou/metrics.py)
- CD: Chamfer Distance using surface sampling (evaluation/cd/metrics.py)  
- LFD: Light Field Distance using external alignment (evaluation/lfd/metrics.py)
- PCR: Point Cloud Registration using closest point distance (evaluation/pcr/metrics.py)

Usage:
    python evaluate_meshes.py
"""

import os
import sys
import numpy as np
import trimesh
from pathlib import Path

# Add evaluation modules to path
sys.path.append('evaluation/cd')
sys.path.append('evaluation/iou')
sys.path.append('evaluation/lfd')
sys.path.append('evaluation/pcr')
sys.path.append('lib/light-field-distance')

# Import evaluation functions from existing codebase
from evaluation.cd.metrics import sample_and_calc_chamfer
from evaluation.iou.metrics import compute_mesh_iou
from evaluation.pcr.metrics import *
from lfd import MeshEncoder, light_field_distance
from trimesh.exchange.binvox import voxelize_mesh

def load_mesh(mesh_path):
    """
    Load mesh using trimesh with consistent preprocessing.
    
    Args:
        mesh_path (str): Path to mesh file (.ply format)
        
    Returns:
        trimesh.Trimesh: Loaded mesh object
    """
    if not os.path.exists(mesh_path):
        raise FileNotFoundError(f"Mesh file not found: {mesh_path}")
    
    print(f"Loading mesh: {mesh_path}")
    mesh = trimesh.load(mesh_path, process=False)
    
    if not isinstance(mesh, trimesh.Trimesh):
        raise ValueError(f"Failed to load mesh as trimesh.Trimesh: {mesh_path}")
    
    print(f"  Vertices: {mesh.vertices.shape[0]}, Faces: {mesh.faces.shape[0]}")
    return mesh

def compute_chamfer_distance(pred_mesh, gt_mesh):
    """
    Compute Chamfer Distance between two meshes.
    
    Implementation follows evaluation/cd/metrics.py:sample_and_calc_chamfer()
    - Samples 4096 points from each mesh surface
    - Computes bidirectional nearest neighbor distances
    - Returns average of both directions
    
    Args:
        pred_mesh (trimesh.Trimesh): Predicted mesh
        gt_mesh (trimesh.Trimesh): Ground truth mesh
        
    Returns:
        float: Chamfer distance
    """
    print("Computing Chamfer Distance...")
    cd = sample_and_calc_chamfer(pred_mesh, gt_mesh)
    print(f"  Chamfer Distance: {cd:.6f}")
    return cd

def compute_iou(pred_mesh, gt_mesh, voxel_size=0.047):
    """
    Compute IoU between two meshes using voxelization.
    
    Implementation follows evaluation/iou/metrics.py:compute_mesh_iou()
    - Voxelizes both meshes using binvox
    - Computes intersection and union of voxel grids
    - Returns IoU ratio
    
    Args:
        pred_mesh (trimesh.Trimesh): Predicted mesh
        gt_mesh (trimesh.Trimesh): Ground truth mesh
        voxel_size (float): Voxel size for discretization
        
    Returns:
        float: IoU score
    """
    print("Computing IoU...")
    
    # Determine voxelization resolution based on mesh bounds
    pred_points = pred_mesh.vertices
    gt_points = gt_mesh.vertices
    
    # Use maximum extent to determine resolution
    pred_extent = pred_points.max(0) - pred_points.min(0)
    gt_extent = gt_points.max(0) - gt_points.min(0)
    max_extent = max(pred_extent.max(), gt_extent.max())
    
    resolution = max(int(max_extent / voxel_size), 2)
    print(f"  Using voxel resolution: {resolution}")
    
    # Voxelize predicted mesh
    pred_voxels_internal = voxelize_mesh(pred_mesh, dimension=resolution, 
                                       wireframe=True, dilated_carving=True, verbose=False)
    pred_voxels_surface = voxelize_mesh(pred_mesh, exact=True, 
                                      dimension=resolution, verbose=False)
    
    # Voxelize ground truth mesh  
    gt_voxels_internal = voxelize_mesh(gt_mesh, dimension=resolution,
                                     wireframe=True, dilated_carving=True, verbose=False)
    gt_voxels_surface = voxelize_mesh(gt_mesh, exact=True,
                                    dimension=resolution, verbose=False)
    
    # Compute IoU using existing function
    pred_voxels = (pred_voxels_internal, pred_voxels_surface)
    gt_voxels = (gt_voxels_internal, gt_voxels_surface)
    
    iou = compute_mesh_iou(pred_voxels, gt_voxels)
    print(f"  IoU: {iou:.6f}")
    return iou

def compute_lfd(pred_mesh, gt_mesh):
    """
    Compute Light Field Distance between two meshes.
    
    Implementation follows evaluation/lfd/metrics.py:calc_lfd()
    - Uses MeshEncoder for mesh alignment and preprocessing
    - Calls external Distance executable for LFD computation
    - Returns light field distance score
    
    Args:
        pred_mesh (trimesh.Trimesh): Predicted mesh
        gt_mesh (trimesh.Trimesh): Ground truth mesh
        
    Returns:
        float: Light Field Distance
    """
    print("Computing Light Field Distance...")
    
    # Create MeshEncoder objects with caching
    pred_encoder = MeshEncoder(pred_mesh, './cache_eval_pred', 'pred_mesh')
    gt_encoder = MeshEncoder(gt_mesh, './cache_eval_gt', 'gt_mesh')
    
    # Align meshes (preprocessing step)
    pred_encoder.align_mesh(verbose=True)
    gt_encoder.align_mesh(verbose=True)
    
    # Compute LFD using existing function
    lfd = light_field_distance(pred_encoder, gt_encoder)
    print(f"  Light Field Distance: {lfd:.6f}")
    return lfd

def compute_pcr(pred_mesh, gt_mesh, threshold=0.047):
    """
    Compute Point Cloud Registration metric.
    
    Implementation follows evaluation/pcr/metrics.py
    - Samples points from ground truth mesh
    - Finds closest points on predicted mesh
    - Computes percentage of points within threshold distance
    
    Args:
        pred_mesh (trimesh.Trimesh): Predicted mesh
        gt_mesh (trimesh.Trimesh): Ground truth mesh
        threshold (float): Distance threshold for point matching
        
    Returns:
        float: PCR score (percentage of points within threshold)
    """
    print("Computing PCR (Point Cloud Registration)...")
    
    # Sample points from ground truth mesh (following existing methodology)
    gt_points, _ = trimesh.sample.sample_surface(gt_mesh, 4096)
    
    # Find closest points on predicted mesh
    closest, distance, _ = trimesh.proximity.closest_point(pred_mesh, gt_points)
    
    # Compute PCR as percentage of points within threshold
    pcr = (distance < threshold).sum() / len(distance)
    print(f"  PCR: {pcr:.6f}")
    return pcr

def main():
    """
    Main evaluation function.
    Loads both meshes and computes all four evaluation metrics.
    """
    # File paths
    pred_mesh_path = "/home/<USER>/Experiments/projects/2d-gaussian-splatting/output/bg_ce_stand_high_16_23z/train/ours_30000/fuse_post_clear.ply"
    gt_mesh_path = "/media/lty/data_mechine/data/GT/CE-S.ply"
    
    print("=" * 60)
    print("MESH EVALUATION SCRIPT")
    print("=" * 60)
    print(f"Predicted mesh: {pred_mesh_path}")
    print(f"Ground truth mesh: {gt_mesh_path}")
    print("=" * 60)
    
    try:
        # Load meshes
        pred_mesh = load_mesh(pred_mesh_path)
        gt_mesh = load_mesh(gt_mesh_path)
        
        print("\nStarting evaluation...")
        print("-" * 40)
        
        # Compute all four metrics
        results = {}
        
        # 1. Chamfer Distance
        results['Chamfer Distance'] = compute_chamfer_distance(pred_mesh, gt_mesh)
        
        # 2. IoU
        results['IoU'] = compute_iou(pred_mesh, gt_mesh)
        
        # 3. Light Field Distance  
        results['Light Field Distance'] = compute_lfd(pred_mesh, gt_mesh)
        
        # 4. PCR
        results['PCR'] = compute_pcr(pred_mesh, gt_mesh)
        
        # Print final results
        print("\n" + "=" * 60)
        print("EVALUATION RESULTS")
        print("=" * 60)
        for metric, value in results.items():
            print(f"{metric:25}: {value:.6f}")
        print("=" * 60)
        
        return results
        
    except Exception as e:
        print(f"Error during evaluation: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
