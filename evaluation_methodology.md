# Mesh Evaluation Methodology

This document explains the four mesh evaluation metrics implemented in `evaluate_meshes.py` and how they align with the existing codebase methodology.

## Overview

The evaluation script computes four standard 3D mesh comparison metrics:

1. **Chamfer Distance (CD)** - Point-to-point surface distance
2. **Intersection over Union (IoU)** - Volumetric overlap using voxelization  
3. **Light Field Distance (LFD)** - Shape similarity using rendered silhouettes
4. **Point Cloud Registration (PCR)** - Point matching within distance threshold

## Metric Implementations

### 1. Chamfer Distance (CD)

**Source**: `evaluation/cd/metrics.py:sample_and_calc_chamfer()`

**Methodology**:
- Samples 4096 points uniformly from each mesh surface using `trimesh.sample.sample_surface()`
- Computes bidirectional nearest neighbor distances using sklearn's KDTree
- Returns average of both directions: `(mean(pred→gt) + mean(gt→pred)) / 2`

**Key Function**:
```python
def sample_and_calc_chamfer(pred, gt):
    pred_points, _ = trimesh.sample.sample_surface(pred, 4096)
    gt_points, _ = trimesh.sample.sample_surface(gt, 4096)
    x = chamfer_distance(pred_points, gt_points)
    return x
```

**Interpretation**: Lower values indicate better similarity. Measures geometric accuracy.

### 2. Intersection over Union (IoU)

**Source**: `evaluation/iou/metrics.py:compute_mesh_iou()`

**Methodology**:
- Voxelizes both meshes using binvox through trimesh's API
- Creates internal (solid) and surface voxel representations
- Computes intersection and union of voxel grids
- Returns IoU = intersection / union

**Key Parameters**:
- `voxel_size = 0.047` (consistent with existing evaluation)
- `resolution = max(mesh_extent / voxel_size, 2)`
- Uses both `wireframe=True, dilated_carving=True` and `exact=True` modes

**Interpretation**: Higher values (0-1) indicate better volumetric overlap.

### 3. Light Field Distance (LFD)

**Source**: `evaluation/lfd/metrics.py:calc_lfd()` and `lib/light-field-distance/lfd/lfd.py`

**Methodology**:
- Uses `MeshEncoder` class to preprocess and align meshes
- Calls external C++ executable `Distance` for LFD computation
- Renders multiple silhouettes from different viewpoints
- Compares shape descriptors (Zernike moments, Fourier descriptors, etc.)

**Key Steps**:
1. Create `MeshEncoder` objects with caching directories
2. Call `align_mesh(verbose=True)` for 3D alignment preprocessing
3. Execute `light_field_distance()` function
4. Parse similarity score from executable output

**Interpretation**: Lower values indicate better shape similarity. Robust to pose variations.

### 4. Point Cloud Registration (PCR)

**Source**: `evaluation/pcr/metrics.py` (lines 136-139)

**Methodology**:
- Samples 4096 points from ground truth mesh surface
- Finds closest points on predicted mesh using `trimesh.proximity.closest_point()`
- Computes percentage of GT points within distance threshold
- Default threshold = 0.047 (consistent with existing evaluation)

**Key Function**:
```python
closest, distance, _ = trimesh.proximity.closest_point(mesh_pred, points_gt)
pcr_mesh = (distance < 0.047).sum() / len(distance)
```

**Interpretation**: Higher values (0-1) indicate better point-wise accuracy.

## Consistency with Existing Codebase

### Import Structure
The script follows the existing import pattern:
```python
sys.path.append('evaluation/cd')
sys.path.append('evaluation/iou') 
sys.path.append('evaluation/lfd')
sys.path.append('evaluation/pcr')
sys.path.append('lib/light-field-distance')
```

### Mesh Loading
Uses `trimesh.load(mesh_path, process=False)` consistent with existing evaluation scripts.

### Parameter Values
- Sampling count: 4096 points (matches existing code)
- Voxel size: 0.047 (matches existing evaluation)
- PCR threshold: 0.047 (matches existing evaluation)

### Caching Strategy
- LFD uses permanent caching with `MeshEncoder(mesh, cache_dir, name)`
- Cache directories: `./cache_eval_pred` and `./cache_eval_gt`
- Follows existing pattern from `evaluation/lfd/eval.py`

## Usage Instructions

### Prerequisites
1. Ensure binvox is installed and in system PATH (required for IoU)
2. Compile LFD executables in `lib/light-field-distance/lfd/Executable/`
3. Install required Python packages: `trimesh`, `sklearn`, `numpy`

### Running the Evaluation
```bash
python evaluate_meshes.py
```

### Expected Output
```
============================================================
MESH EVALUATION SCRIPT
============================================================
Predicted mesh: /path/to/predicted.ply
Ground truth mesh: /path/to/ground_truth.ply
============================================================

Loading mesh: /path/to/predicted.ply
  Vertices: 50000, Faces: 100000
Loading mesh: /path/to/ground_truth.ply  
  Vertices: 45000, Faces: 90000

Starting evaluation...
----------------------------------------
Computing Chamfer Distance...
  Chamfer Distance: 0.012345
Computing IoU...
  Using voxel resolution: 128
  IoU: 0.856789
Computing Light Field Distance...
[MeshEncoder.align_mesh] generating alignment for /tmp/cache_eval_pred/pred_mesh.obj
[MeshEncoder.align_mesh] generating alignment for /tmp/cache_eval_gt/gt_mesh.obj
  Light Field Distance: 0.234567
Computing PCR (Point Cloud Registration)...
  PCR: 0.789012

============================================================
EVALUATION RESULTS
============================================================
Chamfer Distance         : 0.012345
IoU                      : 0.856789
Light Field Distance     : 0.234567
PCR                      : 0.789012
============================================================
```

## Error Handling

The script includes comprehensive error handling for:
- Missing mesh files
- Invalid mesh formats
- Missing dependencies (binvox, LFD executables)
- Memory issues with large meshes
- Numerical errors in metric computation

## Customization

To modify the evaluation for different use cases:

1. **Change file paths**: Edit `pred_mesh_path` and `gt_mesh_path` in `main()`
2. **Adjust sampling**: Modify the 4096 point sampling count
3. **Change thresholds**: Adjust voxel_size (0.047) and PCR threshold
4. **Add metrics**: Extend with additional evaluation functions

This implementation ensures full compatibility with the existing codebase while providing a clean, standalone evaluation interface.
