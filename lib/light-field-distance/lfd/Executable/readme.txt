** To generate feature for each 3D model

1. Run 3DAlignment_v1.8
	(1) This will read "list.txt" to get each model. Note that, in "list.txt", each line is a filename (can include path) and the last line should be empty.
	(2) The following file will be used when calculating the feature: 12_0.OBJ, 12_1.OBJ, ..., 12_9.OBJ, list.txt and 3D models
	
2. press 'n' to start
	(1) please note that the OpenGL window should not be overlap by other window in Windows 2000. This is because the rendered image will be capture from video buffer.
	(2) Each model will create 5 feature file. filename_q4_v1.8.art, filename_q8_v1.8.art, filename_q8_v1.8.fd, filename_q8_v1.8.cir and filename_q8_v1.8.ecc in the same folder.


** To evaluate the performance

1. Run GroundTruth_v1.8
	(1) The number 3D models is recorded in source code. The "GroundTruth_v1.8_4.exe" is used for only 4 models. Please modify the number in source code.
	(2) This will read "list.txt", this is the same with above. In addition, "compare.txt" record the filename for each category, and the file record 3D models in this category. The last line of those file should be emply, as same with "list.txt". (All the files and folders can be generated by "FileName_v1.19.exe", which is write by me)
	(3) "align10.txt" and "q8_table" will be used.
	(4) The result will be written in folder "Results", and sub-folder of category name.
	(5) The result include precise-recall diagram of all category, each category (*.txt) and search result of each 3D model (*.html).
	(6) The "compare_time.txt" is only referred for myself, it's not a really time to create precise-recall.
	