/*
 * Copyright (c) 1997-1999 Massachusetts Institute of Technology
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 *
 */

/* This file was automatically generated --- DO NOT EDIT */
/* Generated on Sun Nov  7 20:44:29 EST 1999 */

#include "fftw-int.h"
#include "fftw.h"

/* Generated by: ./genfft -magic-alignment-check -magic-twiddle-load-all -magic-variables 4 -magic-loopi -hc2real 128 */

/*
 * This function contains 956 FP additions, 374 FP multiplications,
 * (or, 812 additions, 230 multiplications, 144 fused multiply/add),
 * 176 stack variables, and 256 memory accesses
 */
static const fftw_real K555570233 = FFTW_KONST(+0.555570233019602224742830813948532874374937191);
static const fftw_real K831469612 = FFTW_KONST(+0.831469612302545237078788377617905756738560812);
static const fftw_real K195090322 = FFTW_KONST(+0.195090322016128267848284868477022240927691618);
static const fftw_real K980785280 = FFTW_KONST(+0.980785280403230449126182236134239036973933731);
static const fftw_real K995184726 = FFTW_KONST(+0.995184726672196886244836953109479921575474869);
static const fftw_real K098017140 = FFTW_KONST(+0.098017140329560601994195563888641845861136673);
static const fftw_real K471396736 = FFTW_KONST(+0.471396736825997648556387625905254377657460319);
static const fftw_real K881921264 = FFTW_KONST(+0.881921264348355029712756863660388349508442621);
static const fftw_real K803207531 = FFTW_KONST(+0.803207531480644909806676512963141923879569427);
static const fftw_real K595699304 = FFTW_KONST(+0.595699304492433343467036528829969889511926338);
static const fftw_real K989176509 = FFTW_KONST(+0.989176509964780973451673738016243063983689533);
static const fftw_real K146730474 = FFTW_KONST(+0.146730474455361751658850129646717819706215317);
static const fftw_real K773010453 = FFTW_KONST(+0.773010453362736960810906609758469800971041293);
static const fftw_real K634393284 = FFTW_KONST(+0.634393284163645498215171613225493370675687095);
static const fftw_real K290284677 = FFTW_KONST(+0.290284677254462367636192375817395274691476278);
static const fftw_real K956940335 = FFTW_KONST(+0.956940335732208864935797886980269969482849206);
static const fftw_real K336889853 = FFTW_KONST(+0.336889853392220050689253212619147570477766780);
static const fftw_real K941544065 = FFTW_KONST(+0.941544065183020778412509402599502357185589796);
static const fftw_real K903989293 = FFTW_KONST(+0.903989293123443331586200297230537048710132025);
static const fftw_real K427555093 = FFTW_KONST(+0.427555093430282094320966856888798534304578629);
static const fftw_real K998795456 = FFTW_KONST(+0.998795456205172392714771604759100694443203615);
static const fftw_real K049067674 = FFTW_KONST(+0.049067674327418014254954976942682658314745363);
static const fftw_real K671558954 = FFTW_KONST(+0.671558954847018400625376850427421803228750632);
static const fftw_real K740951125 = FFTW_KONST(+0.740951125354959091175616897495162729728955309);
static const fftw_real K514102744 = FFTW_KONST(+0.514102744193221726593693838968815772608049120);
static const fftw_real K857728610 = FFTW_KONST(+0.857728610000272069902269984284770137042490799);
static const fftw_real K242980179 = FFTW_KONST(+0.242980179903263889948274162077471118320990783);
static const fftw_real K970031253 = FFTW_KONST(+0.970031253194543992603984207286100251456865962);
static const fftw_real K765366864 = FFTW_KONST(+0.765366864730179543456919968060797733522689125);
static const fftw_real K1_847759065 = FFTW_KONST(+1.847759065022573512256366378793576573644833252);
static const fftw_real K1_414213562 = FFTW_KONST(+1.414213562373095048801688724209698078569671875);
static const fftw_real K2_000000000 = FFTW_KONST(+2.000000000000000000000000000000000000000000000);

/*
 * Generator Id's : 
 * $Id: exprdag.ml,v 1.41 1999/05/26 15:44:14 fftw Exp $
 * $Id: fft.ml,v 1.43 1999/05/17 19:44:18 fftw Exp $
 * $Id: to_c.ml,v 1.25 1999/10/26 21:41:32 stevenj Exp $
 */

void fftw_hc2real_128(const fftw_real *real_input, const fftw_real *imag_input, fftw_real *output, int real_istride, int imag_istride, int ostride)
{
     fftw_real tmp10;
     fftw_real tmp454;
     fftw_real tmp134;
     fftw_real tmp326;
     fftw_real tmp529;
     fftw_real tmp705;
     fftw_real tmp775;
     fftw_real tmp891;
     fftw_real tmp17;
     fftw_real tmp143;
     fftw_real tmp327;
     fftw_real tmp455;
     fftw_real tmp536;
     fftw_real tmp706;
     fftw_real tmp778;
     fftw_real tmp892;
     fftw_real tmp112;
     fftw_real tmp478;
     fftw_real tmp507;
     fftw_real tmp411;
     fftw_real tmp646;
     fftw_real tmp731;
     fftw_real tmp662;
     fftw_real tmp728;
     fftw_real tmp856;
     fftw_real tmp917;
     fftw_real tmp832;
     fftw_real tmp914;
     fftw_real tmp825;
     fftw_real tmp916;
     fftw_real tmp853;
     fftw_real tmp913;
     fftw_real tmp127;
     fftw_real tmp481;
     fftw_real tmp506;
     fftw_real tmp408;
     fftw_real tmp262;
     fftw_real tmp288;
     fftw_real tmp350;
     fftw_real tmp360;
     fftw_real tmp659;
     fftw_real tmp730;
     fftw_real tmp631;
     fftw_real tmp727;
     fftw_real tmp281;
     fftw_real tmp289;
     fftw_real tmp353;
     fftw_real tmp361;
     fftw_real tmp33;
     fftw_real tmp457;
     fftw_real tmp153;
     fftw_real tmp329;
     fftw_real tmp431;
     fftw_real tmp458;
     fftw_real tmp162;
     fftw_real tmp330;
     fftw_real tmp544;
     fftw_real tmp551;
     fftw_real tmp708;
     fftw_real tmp680;
     fftw_real tmp681;
     fftw_real tmp709;
     fftw_real tmp782;
     fftw_real tmp785;
     fftw_real tmp894;
     fftw_real tmp866;
     fftw_real tmp867;
     fftw_real tmp895;
     fftw_real tmp49;
     fftw_real tmp461;
     fftw_real tmp668;
     fftw_real tmp715;
     fftw_real tmp838;
     fftw_real tmp901;
     fftw_real tmp794;
     fftw_real tmp898;
     fftw_real tmp568;
     fftw_real tmp712;
     fftw_real tmp335;
     fftw_real tmp365;
     fftw_real tmp183;
     fftw_real tmp293;
     fftw_real tmp417;
     fftw_real tmp465;
     fftw_real tmp64;
     fftw_real tmp464;
     fftw_real tmp671;
     fftw_real tmp713;
     fftw_real tmp841;
     fftw_real tmp899;
     fftw_real tmp801;
     fftw_real tmp902;
     fftw_real tmp583;
     fftw_real tmp716;
     fftw_real tmp338;
     fftw_real tmp366;
     fftw_real tmp202;
     fftw_real tmp294;
     fftw_real tmp420;
     fftw_real tmp462;
     fftw_real tmp81;
     fftw_real tmp471;
     fftw_real tmp503;
     fftw_real tmp404;
     fftw_real tmp615;
     fftw_real tmp724;
     fftw_real tmp655;
     fftw_real tmp721;
     fftw_real tmp849;
     fftw_real tmp910;
     fftw_real tmp817;
     fftw_real tmp907;
     fftw_real tmp810;
     fftw_real tmp909;
     fftw_real tmp846;
     fftw_real tmp906;
     fftw_real tmp96;
     fftw_real tmp474;
     fftw_real tmp504;
     fftw_real tmp401;
     fftw_real tmp223;
     fftw_real tmp285;
     fftw_real tmp343;
     fftw_real tmp357;
     fftw_real tmp652;
     fftw_real tmp723;
     fftw_real tmp600;
     fftw_real tmp720;
     fftw_real tmp242;
     fftw_real tmp286;
     fftw_real tmp346;
     fftw_real tmp358;
     ASSERT_ALIGNED_DOUBLE;
     {
	  fftw_real tmp5;
	  fftw_real tmp524;
	  fftw_real tmp3;
	  fftw_real tmp522;
	  fftw_real tmp9;
	  fftw_real tmp526;
	  fftw_real tmp133;
	  fftw_real tmp527;
	  fftw_real tmp6;
	  fftw_real tmp130;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp4;
	       fftw_real tmp523;
	       fftw_real tmp1;
	       fftw_real tmp2;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp4 = real_input[32 * real_istride];
	       tmp5 = K2_000000000 * tmp4;
	       tmp523 = imag_input[32 * imag_istride];
	       tmp524 = K2_000000000 * tmp523;
	       tmp1 = real_input[0];
	       tmp2 = real_input[64 * real_istride];
	       tmp3 = tmp1 + tmp2;
	       tmp522 = tmp1 - tmp2;
	       {
		    fftw_real tmp7;
		    fftw_real tmp8;
		    fftw_real tmp131;
		    fftw_real tmp132;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp7 = real_input[16 * real_istride];
		    tmp8 = real_input[48 * real_istride];
		    tmp9 = K2_000000000 * (tmp7 + tmp8);
		    tmp526 = tmp7 - tmp8;
		    tmp131 = imag_input[16 * imag_istride];
		    tmp132 = imag_input[48 * imag_istride];
		    tmp133 = K2_000000000 * (tmp131 - tmp132);
		    tmp527 = tmp132 + tmp131;
	       }
	  }
	  tmp6 = tmp3 + tmp5;
	  tmp10 = tmp6 + tmp9;
	  tmp454 = tmp6 - tmp9;
	  tmp130 = tmp3 - tmp5;
	  tmp134 = tmp130 - tmp133;
	  tmp326 = tmp130 + tmp133;
	  {
	       fftw_real tmp525;
	       fftw_real tmp528;
	       fftw_real tmp773;
	       fftw_real tmp774;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp525 = tmp522 - tmp524;
	       tmp528 = K1_414213562 * (tmp526 - tmp527);
	       tmp529 = tmp525 + tmp528;
	       tmp705 = tmp525 - tmp528;
	       tmp773 = tmp522 + tmp524;
	       tmp774 = K1_414213562 * (tmp526 + tmp527);
	       tmp775 = tmp773 - tmp774;
	       tmp891 = tmp773 + tmp774;
	  }
     }
     {
	  fftw_real tmp13;
	  fftw_real tmp530;
	  fftw_real tmp141;
	  fftw_real tmp534;
	  fftw_real tmp16;
	  fftw_real tmp533;
	  fftw_real tmp138;
	  fftw_real tmp531;
	  fftw_real tmp135;
	  fftw_real tmp142;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp11;
	       fftw_real tmp12;
	       fftw_real tmp139;
	       fftw_real tmp140;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp11 = real_input[8 * real_istride];
	       tmp12 = real_input[56 * real_istride];
	       tmp13 = tmp11 + tmp12;
	       tmp530 = tmp11 - tmp12;
	       tmp139 = imag_input[8 * imag_istride];
	       tmp140 = imag_input[56 * imag_istride];
	       tmp141 = tmp139 - tmp140;
	       tmp534 = tmp139 + tmp140;
	  }
	  {
	       fftw_real tmp14;
	       fftw_real tmp15;
	       fftw_real tmp136;
	       fftw_real tmp137;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp14 = real_input[24 * real_istride];
	       tmp15 = real_input[40 * real_istride];
	       tmp16 = tmp14 + tmp15;
	       tmp533 = tmp15 - tmp14;
	       tmp136 = imag_input[24 * imag_istride];
	       tmp137 = imag_input[40 * imag_istride];
	       tmp138 = tmp136 - tmp137;
	       tmp531 = tmp137 + tmp136;
	  }
	  tmp17 = K2_000000000 * (tmp13 + tmp16);
	  tmp135 = tmp13 - tmp16;
	  tmp142 = tmp138 + tmp141;
	  tmp143 = K1_414213562 * (tmp135 - tmp142);
	  tmp327 = K1_414213562 * (tmp135 + tmp142);
	  tmp455 = K2_000000000 * (tmp141 - tmp138);
	  {
	       fftw_real tmp532;
	       fftw_real tmp535;
	       fftw_real tmp776;
	       fftw_real tmp777;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp532 = tmp530 - tmp531;
	       tmp535 = tmp533 + tmp534;
	       tmp536 = (K1_847759065 * tmp532) - (K765366864 * tmp535);
	       tmp706 = (K765366864 * tmp532) + (K1_847759065 * tmp535);
	       tmp776 = tmp530 + tmp531;
	       tmp777 = tmp534 - tmp533;
	       tmp778 = (K765366864 * tmp776) - (K1_847759065 * tmp777);
	       tmp892 = (K1_847759065 * tmp776) + (K765366864 * tmp777);
	  }
     }
     {
	  fftw_real tmp104;
	  fftw_real tmp244;
	  fftw_real tmp260;
	  fftw_real tmp406;
	  fftw_real tmp619;
	  fftw_real tmp819;
	  fftw_real tmp622;
	  fftw_real tmp820;
	  fftw_real tmp126;
	  fftw_real tmp272;
	  fftw_real tmp270;
	  fftw_real tmp410;
	  fftw_real tmp641;
	  fftw_real tmp829;
	  fftw_real tmp644;
	  fftw_real tmp830;
	  fftw_real tmp111;
	  fftw_real tmp253;
	  fftw_real tmp251;
	  fftw_real tmp407;
	  fftw_real tmp626;
	  fftw_real tmp822;
	  fftw_real tmp629;
	  fftw_real tmp823;
	  fftw_real tmp119;
	  fftw_real tmp263;
	  fftw_real tmp279;
	  fftw_real tmp409;
	  fftw_real tmp634;
	  fftw_real tmp826;
	  fftw_real tmp637;
	  fftw_real tmp827;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp100;
	       fftw_real tmp617;
	       fftw_real tmp259;
	       fftw_real tmp621;
	       fftw_real tmp103;
	       fftw_real tmp620;
	       fftw_real tmp256;
	       fftw_real tmp618;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp98;
		    fftw_real tmp99;
		    fftw_real tmp257;
		    fftw_real tmp258;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp98 = real_input[3 * real_istride];
		    tmp99 = real_input[61 * real_istride];
		    tmp100 = tmp98 + tmp99;
		    tmp617 = tmp98 - tmp99;
		    tmp257 = imag_input[3 * imag_istride];
		    tmp258 = imag_input[61 * imag_istride];
		    tmp259 = tmp257 - tmp258;
		    tmp621 = tmp257 + tmp258;
	       }
	       {
		    fftw_real tmp101;
		    fftw_real tmp102;
		    fftw_real tmp254;
		    fftw_real tmp255;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp101 = real_input[29 * real_istride];
		    tmp102 = real_input[35 * real_istride];
		    tmp103 = tmp101 + tmp102;
		    tmp620 = tmp102 - tmp101;
		    tmp254 = imag_input[29 * imag_istride];
		    tmp255 = imag_input[35 * imag_istride];
		    tmp256 = tmp254 - tmp255;
		    tmp618 = tmp255 + tmp254;
	       }
	       tmp104 = tmp100 + tmp103;
	       tmp244 = tmp100 - tmp103;
	       tmp260 = tmp256 + tmp259;
	       tmp406 = tmp259 - tmp256;
	       tmp619 = tmp617 - tmp618;
	       tmp819 = tmp617 + tmp618;
	       tmp622 = tmp620 + tmp621;
	       tmp820 = tmp621 - tmp620;
	  }
	  {
	       fftw_real tmp122;
	       fftw_real tmp639;
	       fftw_real tmp269;
	       fftw_real tmp643;
	       fftw_real tmp125;
	       fftw_real tmp642;
	       fftw_real tmp266;
	       fftw_real tmp640;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp120;
		    fftw_real tmp121;
		    fftw_real tmp267;
		    fftw_real tmp268;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp120 = real_input[11 * real_istride];
		    tmp121 = real_input[53 * real_istride];
		    tmp122 = tmp120 + tmp121;
		    tmp639 = tmp120 - tmp121;
		    tmp267 = imag_input[11 * imag_istride];
		    tmp268 = imag_input[53 * imag_istride];
		    tmp269 = tmp267 - tmp268;
		    tmp643 = tmp267 + tmp268;
	       }
	       {
		    fftw_real tmp123;
		    fftw_real tmp124;
		    fftw_real tmp264;
		    fftw_real tmp265;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp123 = real_input[21 * real_istride];
		    tmp124 = real_input[43 * real_istride];
		    tmp125 = tmp123 + tmp124;
		    tmp642 = tmp124 - tmp123;
		    tmp264 = imag_input[21 * imag_istride];
		    tmp265 = imag_input[43 * imag_istride];
		    tmp266 = tmp264 - tmp265;
		    tmp640 = tmp265 + tmp264;
	       }
	       tmp126 = tmp122 + tmp125;
	       tmp272 = tmp125 - tmp122;
	       tmp270 = tmp266 + tmp269;
	       tmp410 = tmp269 - tmp266;
	       tmp641 = tmp639 - tmp640;
	       tmp829 = tmp639 + tmp640;
	       tmp644 = tmp642 + tmp643;
	       tmp830 = tmp643 - tmp642;
	  }
	  {
	       fftw_real tmp107;
	       fftw_real tmp624;
	       fftw_real tmp250;
	       fftw_real tmp628;
	       fftw_real tmp110;
	       fftw_real tmp627;
	       fftw_real tmp247;
	       fftw_real tmp625;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp105;
		    fftw_real tmp106;
		    fftw_real tmp248;
		    fftw_real tmp249;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp105 = real_input[13 * real_istride];
		    tmp106 = real_input[51 * real_istride];
		    tmp107 = tmp105 + tmp106;
		    tmp624 = tmp105 - tmp106;
		    tmp248 = imag_input[13 * imag_istride];
		    tmp249 = imag_input[51 * imag_istride];
		    tmp250 = tmp248 - tmp249;
		    tmp628 = tmp248 + tmp249;
	       }
	       {
		    fftw_real tmp108;
		    fftw_real tmp109;
		    fftw_real tmp245;
		    fftw_real tmp246;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp108 = real_input[19 * real_istride];
		    tmp109 = real_input[45 * real_istride];
		    tmp110 = tmp108 + tmp109;
		    tmp627 = tmp109 - tmp108;
		    tmp245 = imag_input[19 * imag_istride];
		    tmp246 = imag_input[45 * imag_istride];
		    tmp247 = tmp245 - tmp246;
		    tmp625 = tmp246 + tmp245;
	       }
	       tmp111 = tmp107 + tmp110;
	       tmp253 = tmp110 - tmp107;
	       tmp251 = tmp247 + tmp250;
	       tmp407 = tmp250 - tmp247;
	       tmp626 = tmp624 - tmp625;
	       tmp822 = tmp624 + tmp625;
	       tmp629 = tmp627 + tmp628;
	       tmp823 = tmp628 - tmp627;
	  }
	  {
	       fftw_real tmp115;
	       fftw_real tmp632;
	       fftw_real tmp278;
	       fftw_real tmp636;
	       fftw_real tmp118;
	       fftw_real tmp635;
	       fftw_real tmp275;
	       fftw_real tmp633;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp113;
		    fftw_real tmp114;
		    fftw_real tmp276;
		    fftw_real tmp277;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp113 = real_input[5 * real_istride];
		    tmp114 = real_input[59 * real_istride];
		    tmp115 = tmp113 + tmp114;
		    tmp632 = tmp113 - tmp114;
		    tmp276 = imag_input[5 * imag_istride];
		    tmp277 = imag_input[59 * imag_istride];
		    tmp278 = tmp276 - tmp277;
		    tmp636 = tmp276 + tmp277;
	       }
	       {
		    fftw_real tmp116;
		    fftw_real tmp117;
		    fftw_real tmp273;
		    fftw_real tmp274;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp116 = real_input[27 * real_istride];
		    tmp117 = real_input[37 * real_istride];
		    tmp118 = tmp116 + tmp117;
		    tmp635 = tmp117 - tmp116;
		    tmp273 = imag_input[27 * imag_istride];
		    tmp274 = imag_input[37 * imag_istride];
		    tmp275 = tmp273 - tmp274;
		    tmp633 = tmp274 + tmp273;
	       }
	       tmp119 = tmp115 + tmp118;
	       tmp263 = tmp115 - tmp118;
	       tmp279 = tmp275 + tmp278;
	       tmp409 = tmp278 - tmp275;
	       tmp634 = tmp632 - tmp633;
	       tmp826 = tmp632 + tmp633;
	       tmp637 = tmp635 + tmp636;
	       tmp827 = tmp636 - tmp635;
	  }
	  {
	       fftw_real tmp828;
	       fftw_real tmp831;
	       fftw_real tmp623;
	       fftw_real tmp630;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp476;
		    fftw_real tmp477;
		    fftw_real tmp638;
		    fftw_real tmp645;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp112 = tmp104 + tmp111;
		    tmp476 = tmp104 - tmp111;
		    tmp477 = tmp409 + tmp410;
		    tmp478 = tmp476 - tmp477;
		    tmp507 = tmp476 + tmp477;
		    tmp411 = tmp409 - tmp410;
		    tmp638 = (K970031253 * tmp634) - (K242980179 * tmp637);
		    tmp645 = (K857728610 * tmp641) - (K514102744 * tmp644);
		    tmp646 = tmp638 + tmp645;
		    tmp731 = tmp645 - tmp638;
	       }
	       {
		    fftw_real tmp660;
		    fftw_real tmp661;
		    fftw_real tmp854;
		    fftw_real tmp855;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp660 = (K242980179 * tmp634) + (K970031253 * tmp637);
		    tmp661 = (K514102744 * tmp641) + (K857728610 * tmp644);
		    tmp662 = tmp660 - tmp661;
		    tmp728 = tmp660 + tmp661;
		    tmp854 = (K740951125 * tmp826) - (K671558954 * tmp827);
		    tmp855 = (K049067674 * tmp829) + (K998795456 * tmp830);
		    tmp856 = tmp854 - tmp855;
		    tmp917 = tmp854 + tmp855;
	       }
	       tmp828 = (K671558954 * tmp826) + (K740951125 * tmp827);
	       tmp831 = (K998795456 * tmp829) - (K049067674 * tmp830);
	       tmp832 = tmp828 - tmp831;
	       tmp914 = tmp828 + tmp831;
	       {
		    fftw_real tmp821;
		    fftw_real tmp824;
		    fftw_real tmp851;
		    fftw_real tmp852;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp821 = (K427555093 * tmp819) + (K903989293 * tmp820);
		    tmp824 = (K941544065 * tmp822) - (K336889853 * tmp823);
		    tmp825 = tmp821 - tmp824;
		    tmp916 = tmp821 + tmp824;
		    tmp851 = (K903989293 * tmp819) - (K427555093 * tmp820);
		    tmp852 = (K336889853 * tmp822) + (K941544065 * tmp823);
		    tmp853 = tmp851 - tmp852;
		    tmp913 = tmp851 + tmp852;
	       }
	       {
		    fftw_real tmp479;
		    fftw_real tmp480;
		    fftw_real tmp252;
		    fftw_real tmp261;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp127 = tmp119 + tmp126;
		    tmp479 = tmp126 - tmp119;
		    tmp480 = tmp406 + tmp407;
		    tmp481 = tmp479 + tmp480;
		    tmp506 = tmp480 - tmp479;
		    tmp408 = tmp406 - tmp407;
		    tmp252 = tmp244 - tmp251;
		    tmp261 = tmp253 + tmp260;
		    tmp262 = (K956940335 * tmp252) - (K290284677 * tmp261);
		    tmp288 = (K956940335 * tmp261) + (K290284677 * tmp252);
	       }
	       {
		    fftw_real tmp348;
		    fftw_real tmp349;
		    fftw_real tmp657;
		    fftw_real tmp658;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp348 = tmp244 + tmp251;
		    tmp349 = tmp260 - tmp253;
		    tmp350 = (K634393284 * tmp348) - (K773010453 * tmp349);
		    tmp360 = (K634393284 * tmp349) + (K773010453 * tmp348);
		    tmp657 = (K146730474 * tmp619) + (K989176509 * tmp622);
		    tmp658 = (K595699304 * tmp626) + (K803207531 * tmp629);
		    tmp659 = tmp657 - tmp658;
		    tmp730 = tmp657 + tmp658;
	       }
	       tmp623 = (K989176509 * tmp619) - (K146730474 * tmp622);
	       tmp630 = (K803207531 * tmp626) - (K595699304 * tmp629);
	       tmp631 = tmp623 + tmp630;
	       tmp727 = tmp623 - tmp630;
	       {
		    fftw_real tmp271;
		    fftw_real tmp280;
		    fftw_real tmp351;
		    fftw_real tmp352;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp271 = tmp263 - tmp270;
		    tmp280 = tmp272 + tmp279;
		    tmp281 = (K881921264 * tmp271) - (K471396736 * tmp280);
		    tmp289 = (K881921264 * tmp280) + (K471396736 * tmp271);
		    tmp351 = tmp263 + tmp270;
		    tmp352 = tmp279 - tmp272;
		    tmp353 = (K098017140 * tmp351) - (K995184726 * tmp352);
		    tmp361 = (K098017140 * tmp352) + (K995184726 * tmp351);
	       }
	  }
     }
     {
	  fftw_real tmp21;
	  fftw_real tmp780;
	  fftw_real tmp540;
	  fftw_real tmp157;
	  fftw_real tmp24;
	  fftw_real tmp781;
	  fftw_real tmp543;
	  fftw_real tmp160;
	  fftw_real tmp28;
	  fftw_real tmp783;
	  fftw_real tmp547;
	  fftw_real tmp148;
	  fftw_real tmp31;
	  fftw_real tmp784;
	  fftw_real tmp550;
	  fftw_real tmp151;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp19;
	       fftw_real tmp20;
	       fftw_real tmp538;
	       fftw_real tmp155;
	       fftw_real tmp156;
	       fftw_real tmp539;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp19 = real_input[4 * real_istride];
	       tmp20 = real_input[60 * real_istride];
	       tmp538 = tmp19 - tmp20;
	       tmp155 = imag_input[28 * imag_istride];
	       tmp156 = imag_input[36 * imag_istride];
	       tmp539 = tmp156 + tmp155;
	       tmp21 = tmp19 + tmp20;
	       tmp780 = tmp538 + tmp539;
	       tmp540 = tmp538 - tmp539;
	       tmp157 = tmp155 - tmp156;
	  }
	  {
	       fftw_real tmp22;
	       fftw_real tmp23;
	       fftw_real tmp541;
	       fftw_real tmp158;
	       fftw_real tmp159;
	       fftw_real tmp542;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp22 = real_input[28 * real_istride];
	       tmp23 = real_input[36 * real_istride];
	       tmp541 = tmp23 - tmp22;
	       tmp158 = imag_input[4 * imag_istride];
	       tmp159 = imag_input[60 * imag_istride];
	       tmp542 = tmp158 + tmp159;
	       tmp24 = tmp22 + tmp23;
	       tmp781 = tmp542 - tmp541;
	       tmp543 = tmp541 + tmp542;
	       tmp160 = tmp158 - tmp159;
	  }
	  {
	       fftw_real tmp26;
	       fftw_real tmp27;
	       fftw_real tmp545;
	       fftw_real tmp146;
	       fftw_real tmp147;
	       fftw_real tmp546;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp26 = real_input[12 * real_istride];
	       tmp27 = real_input[52 * real_istride];
	       tmp545 = tmp26 - tmp27;
	       tmp146 = imag_input[20 * imag_istride];
	       tmp147 = imag_input[44 * imag_istride];
	       tmp546 = tmp147 + tmp146;
	       tmp28 = tmp26 + tmp27;
	       tmp783 = tmp545 + tmp546;
	       tmp547 = tmp545 - tmp546;
	       tmp148 = tmp146 - tmp147;
	  }
	  {
	       fftw_real tmp29;
	       fftw_real tmp30;
	       fftw_real tmp548;
	       fftw_real tmp149;
	       fftw_real tmp150;
	       fftw_real tmp549;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp29 = real_input[20 * real_istride];
	       tmp30 = real_input[44 * real_istride];
	       tmp548 = tmp30 - tmp29;
	       tmp149 = imag_input[12 * imag_istride];
	       tmp150 = imag_input[52 * imag_istride];
	       tmp549 = tmp149 + tmp150;
	       tmp31 = tmp29 + tmp30;
	       tmp784 = tmp549 - tmp548;
	       tmp550 = tmp548 + tmp549;
	       tmp151 = tmp149 - tmp150;
	  }
	  {
	       fftw_real tmp25;
	       fftw_real tmp32;
	       fftw_real tmp145;
	       fftw_real tmp152;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp25 = tmp21 + tmp24;
	       tmp32 = tmp28 + tmp31;
	       tmp33 = K2_000000000 * (tmp25 + tmp32);
	       tmp457 = tmp25 - tmp32;
	       tmp145 = tmp21 - tmp24;
	       tmp152 = tmp148 + tmp151;
	       tmp153 = tmp145 - tmp152;
	       tmp329 = tmp145 + tmp152;
	  }
	  {
	       fftw_real tmp429;
	       fftw_real tmp430;
	       fftw_real tmp154;
	       fftw_real tmp161;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp429 = tmp160 - tmp157;
	       tmp430 = tmp151 - tmp148;
	       tmp431 = K2_000000000 * (tmp429 - tmp430);
	       tmp458 = tmp429 + tmp430;
	       tmp154 = tmp31 - tmp28;
	       tmp161 = tmp157 + tmp160;
	       tmp162 = tmp154 + tmp161;
	       tmp330 = tmp161 - tmp154;
	  }
	  tmp544 = (K980785280 * tmp540) - (K195090322 * tmp543);
	  tmp551 = (K831469612 * tmp547) - (K555570233 * tmp550);
	  tmp708 = tmp544 - tmp551;
	  tmp680 = (K195090322 * tmp540) + (K980785280 * tmp543);
	  tmp681 = (K555570233 * tmp547) + (K831469612 * tmp550);
	  tmp709 = tmp680 + tmp681;
	  tmp782 = (K831469612 * tmp780) - (K555570233 * tmp781);
	  tmp785 = (K195090322 * tmp783) + (K980785280 * tmp784);
	  tmp894 = tmp782 + tmp785;
	  tmp866 = (K555570233 * tmp780) + (K831469612 * tmp781);
	  tmp867 = (K980785280 * tmp783) - (K195090322 * tmp784);
	  tmp895 = tmp866 + tmp867;
     }
     {
	  fftw_real tmp37;
	  fftw_real tmp180;
	  fftw_real tmp40;
	  fftw_real tmp177;
	  fftw_real tmp165;
	  fftw_real tmp559;
	  fftw_real tmp789;
	  fftw_real tmp788;
	  fftw_real tmp556;
	  fftw_real tmp181;
	  fftw_real tmp44;
	  fftw_real tmp171;
	  fftw_real tmp47;
	  fftw_real tmp168;
	  fftw_real tmp172;
	  fftw_real tmp566;
	  fftw_real tmp792;
	  fftw_real tmp791;
	  fftw_real tmp563;
	  fftw_real tmp174;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp554;
	       fftw_real tmp558;
	       fftw_real tmp557;
	       fftw_real tmp555;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp35;
		    fftw_real tmp36;
		    fftw_real tmp178;
		    fftw_real tmp179;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp35 = real_input[2 * real_istride];
		    tmp36 = real_input[62 * real_istride];
		    tmp37 = tmp35 + tmp36;
		    tmp554 = tmp35 - tmp36;
		    tmp178 = imag_input[2 * imag_istride];
		    tmp179 = imag_input[62 * imag_istride];
		    tmp180 = tmp178 - tmp179;
		    tmp558 = tmp178 + tmp179;
	       }
	       {
		    fftw_real tmp38;
		    fftw_real tmp39;
		    fftw_real tmp175;
		    fftw_real tmp176;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp38 = real_input[30 * real_istride];
		    tmp39 = real_input[34 * real_istride];
		    tmp40 = tmp38 + tmp39;
		    tmp557 = tmp39 - tmp38;
		    tmp175 = imag_input[30 * imag_istride];
		    tmp176 = imag_input[34 * imag_istride];
		    tmp177 = tmp175 - tmp176;
		    tmp555 = tmp176 + tmp175;
	       }
	       tmp165 = tmp37 - tmp40;
	       tmp559 = tmp557 + tmp558;
	       tmp789 = tmp558 - tmp557;
	       tmp788 = tmp554 + tmp555;
	       tmp556 = tmp554 - tmp555;
	       tmp181 = tmp177 + tmp180;
	  }
	  {
	       fftw_real tmp561;
	       fftw_real tmp565;
	       fftw_real tmp564;
	       fftw_real tmp562;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp42;
		    fftw_real tmp43;
		    fftw_real tmp169;
		    fftw_real tmp170;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp42 = real_input[14 * real_istride];
		    tmp43 = real_input[50 * real_istride];
		    tmp44 = tmp42 + tmp43;
		    tmp561 = tmp42 - tmp43;
		    tmp169 = imag_input[14 * imag_istride];
		    tmp170 = imag_input[50 * imag_istride];
		    tmp171 = tmp169 - tmp170;
		    tmp565 = tmp169 + tmp170;
	       }
	       {
		    fftw_real tmp45;
		    fftw_real tmp46;
		    fftw_real tmp166;
		    fftw_real tmp167;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp45 = real_input[18 * real_istride];
		    tmp46 = real_input[46 * real_istride];
		    tmp47 = tmp45 + tmp46;
		    tmp564 = tmp46 - tmp45;
		    tmp166 = imag_input[18 * imag_istride];
		    tmp167 = imag_input[46 * imag_istride];
		    tmp168 = tmp166 - tmp167;
		    tmp562 = tmp167 + tmp166;
	       }
	       tmp172 = tmp168 + tmp171;
	       tmp566 = tmp564 + tmp565;
	       tmp792 = tmp565 - tmp564;
	       tmp791 = tmp561 + tmp562;
	       tmp563 = tmp561 - tmp562;
	       tmp174 = tmp47 - tmp44;
	  }
	  {
	       fftw_real tmp41;
	       fftw_real tmp48;
	       fftw_real tmp666;
	       fftw_real tmp667;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp41 = tmp37 + tmp40;
	       tmp48 = tmp44 + tmp47;
	       tmp49 = tmp41 + tmp48;
	       tmp461 = tmp41 - tmp48;
	       tmp666 = (K098017140 * tmp556) + (K995184726 * tmp559);
	       tmp667 = (K634393284 * tmp563) + (K773010453 * tmp566);
	       tmp668 = tmp666 - tmp667;
	       tmp715 = tmp666 + tmp667;
	  }
	  {
	       fftw_real tmp836;
	       fftw_real tmp837;
	       fftw_real tmp790;
	       fftw_real tmp793;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp836 = (K290284677 * tmp788) + (K956940335 * tmp789);
	       tmp837 = (K881921264 * tmp791) - (K471396736 * tmp792);
	       tmp838 = tmp836 - tmp837;
	       tmp901 = tmp836 + tmp837;
	       tmp790 = (K956940335 * tmp788) - (K290284677 * tmp789);
	       tmp793 = (K471396736 * tmp791) + (K881921264 * tmp792);
	       tmp794 = tmp790 - tmp793;
	       tmp898 = tmp790 + tmp793;
	  }
	  {
	       fftw_real tmp560;
	       fftw_real tmp567;
	       fftw_real tmp333;
	       fftw_real tmp334;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp560 = (K995184726 * tmp556) - (K098017140 * tmp559);
	       tmp567 = (K773010453 * tmp563) - (K634393284 * tmp566);
	       tmp568 = tmp560 + tmp567;
	       tmp712 = tmp560 - tmp567;
	       tmp333 = tmp165 + tmp172;
	       tmp334 = tmp181 - tmp174;
	       tmp335 = (K831469612 * tmp333) - (K555570233 * tmp334);
	       tmp365 = (K831469612 * tmp334) + (K555570233 * tmp333);
	  }
	  {
	       fftw_real tmp173;
	       fftw_real tmp182;
	       fftw_real tmp415;
	       fftw_real tmp416;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp173 = tmp165 - tmp172;
	       tmp182 = tmp174 + tmp181;
	       tmp183 = (K980785280 * tmp173) - (K195090322 * tmp182);
	       tmp293 = (K980785280 * tmp182) + (K195090322 * tmp173);
	       tmp415 = tmp180 - tmp177;
	       tmp416 = tmp171 - tmp168;
	       tmp417 = tmp415 - tmp416;
	       tmp465 = tmp415 + tmp416;
	  }
     }
     {
	  fftw_real tmp52;
	  fftw_real tmp199;
	  fftw_real tmp55;
	  fftw_real tmp196;
	  fftw_real tmp184;
	  fftw_real tmp574;
	  fftw_real tmp796;
	  fftw_real tmp795;
	  fftw_real tmp571;
	  fftw_real tmp200;
	  fftw_real tmp59;
	  fftw_real tmp190;
	  fftw_real tmp62;
	  fftw_real tmp187;
	  fftw_real tmp191;
	  fftw_real tmp581;
	  fftw_real tmp799;
	  fftw_real tmp798;
	  fftw_real tmp578;
	  fftw_real tmp193;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp569;
	       fftw_real tmp573;
	       fftw_real tmp572;
	       fftw_real tmp570;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp50;
		    fftw_real tmp51;
		    fftw_real tmp197;
		    fftw_real tmp198;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp50 = real_input[6 * real_istride];
		    tmp51 = real_input[58 * real_istride];
		    tmp52 = tmp50 + tmp51;
		    tmp569 = tmp50 - tmp51;
		    tmp197 = imag_input[6 * imag_istride];
		    tmp198 = imag_input[58 * imag_istride];
		    tmp199 = tmp197 - tmp198;
		    tmp573 = tmp197 + tmp198;
	       }
	       {
		    fftw_real tmp53;
		    fftw_real tmp54;
		    fftw_real tmp194;
		    fftw_real tmp195;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp53 = real_input[26 * real_istride];
		    tmp54 = real_input[38 * real_istride];
		    tmp55 = tmp53 + tmp54;
		    tmp572 = tmp54 - tmp53;
		    tmp194 = imag_input[26 * imag_istride];
		    tmp195 = imag_input[38 * imag_istride];
		    tmp196 = tmp194 - tmp195;
		    tmp570 = tmp195 + tmp194;
	       }
	       tmp184 = tmp52 - tmp55;
	       tmp574 = tmp572 + tmp573;
	       tmp796 = tmp573 - tmp572;
	       tmp795 = tmp569 + tmp570;
	       tmp571 = tmp569 - tmp570;
	       tmp200 = tmp196 + tmp199;
	  }
	  {
	       fftw_real tmp576;
	       fftw_real tmp580;
	       fftw_real tmp579;
	       fftw_real tmp577;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp57;
		    fftw_real tmp58;
		    fftw_real tmp188;
		    fftw_real tmp189;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp57 = real_input[10 * real_istride];
		    tmp58 = real_input[54 * real_istride];
		    tmp59 = tmp57 + tmp58;
		    tmp576 = tmp57 - tmp58;
		    tmp188 = imag_input[10 * imag_istride];
		    tmp189 = imag_input[54 * imag_istride];
		    tmp190 = tmp188 - tmp189;
		    tmp580 = tmp188 + tmp189;
	       }
	       {
		    fftw_real tmp60;
		    fftw_real tmp61;
		    fftw_real tmp185;
		    fftw_real tmp186;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp60 = real_input[22 * real_istride];
		    tmp61 = real_input[42 * real_istride];
		    tmp62 = tmp60 + tmp61;
		    tmp579 = tmp61 - tmp60;
		    tmp185 = imag_input[22 * imag_istride];
		    tmp186 = imag_input[42 * imag_istride];
		    tmp187 = tmp185 - tmp186;
		    tmp577 = tmp186 + tmp185;
	       }
	       tmp191 = tmp187 + tmp190;
	       tmp581 = tmp579 + tmp580;
	       tmp799 = tmp580 - tmp579;
	       tmp798 = tmp576 + tmp577;
	       tmp578 = tmp576 - tmp577;
	       tmp193 = tmp62 - tmp59;
	  }
	  {
	       fftw_real tmp56;
	       fftw_real tmp63;
	       fftw_real tmp669;
	       fftw_real tmp670;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp56 = tmp52 + tmp55;
	       tmp63 = tmp59 + tmp62;
	       tmp64 = tmp56 + tmp63;
	       tmp464 = tmp63 - tmp56;
	       tmp669 = (K290284677 * tmp571) + (K956940335 * tmp574);
	       tmp670 = (K471396736 * tmp578) + (K881921264 * tmp581);
	       tmp671 = tmp669 - tmp670;
	       tmp713 = tmp669 + tmp670;
	  }
	  {
	       fftw_real tmp839;
	       fftw_real tmp840;
	       fftw_real tmp797;
	       fftw_real tmp800;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp839 = (K773010453 * tmp795) + (K634393284 * tmp796);
	       tmp840 = (K995184726 * tmp798) + (K098017140 * tmp799);
	       tmp841 = tmp839 - tmp840;
	       tmp899 = tmp839 + tmp840;
	       tmp797 = (K634393284 * tmp795) - (K773010453 * tmp796);
	       tmp800 = (K098017140 * tmp798) - (K995184726 * tmp799);
	       tmp801 = tmp797 + tmp800;
	       tmp902 = tmp800 - tmp797;
	  }
	  {
	       fftw_real tmp575;
	       fftw_real tmp582;
	       fftw_real tmp336;
	       fftw_real tmp337;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp575 = (K956940335 * tmp571) - (K290284677 * tmp574);
	       tmp582 = (K881921264 * tmp578) - (K471396736 * tmp581);
	       tmp583 = tmp575 + tmp582;
	       tmp716 = tmp582 - tmp575;
	       tmp336 = tmp200 - tmp193;
	       tmp337 = tmp184 + tmp191;
	       tmp338 = (K980785280 * tmp336) + (K195090322 * tmp337);
	       tmp366 = (K980785280 * tmp337) - (K195090322 * tmp336);
	  }
	  {
	       fftw_real tmp192;
	       fftw_real tmp201;
	       fftw_real tmp418;
	       fftw_real tmp419;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp192 = tmp184 - tmp191;
	       tmp201 = tmp193 + tmp200;
	       tmp202 = (K831469612 * tmp192) - (K555570233 * tmp201);
	       tmp294 = (K831469612 * tmp201) + (K555570233 * tmp192);
	       tmp418 = tmp199 - tmp196;
	       tmp419 = tmp190 - tmp187;
	       tmp420 = tmp418 - tmp419;
	       tmp462 = tmp418 + tmp419;
	  }
     }
     {
	  fftw_real tmp73;
	  fftw_real tmp205;
	  fftw_real tmp221;
	  fftw_real tmp399;
	  fftw_real tmp588;
	  fftw_real tmp804;
	  fftw_real tmp591;
	  fftw_real tmp805;
	  fftw_real tmp95;
	  fftw_real tmp233;
	  fftw_real tmp231;
	  fftw_real tmp403;
	  fftw_real tmp610;
	  fftw_real tmp814;
	  fftw_real tmp613;
	  fftw_real tmp815;
	  fftw_real tmp80;
	  fftw_real tmp214;
	  fftw_real tmp212;
	  fftw_real tmp400;
	  fftw_real tmp595;
	  fftw_real tmp807;
	  fftw_real tmp598;
	  fftw_real tmp808;
	  fftw_real tmp88;
	  fftw_real tmp224;
	  fftw_real tmp240;
	  fftw_real tmp402;
	  fftw_real tmp603;
	  fftw_real tmp811;
	  fftw_real tmp606;
	  fftw_real tmp812;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp69;
	       fftw_real tmp586;
	       fftw_real tmp220;
	       fftw_real tmp590;
	       fftw_real tmp72;
	       fftw_real tmp589;
	       fftw_real tmp217;
	       fftw_real tmp587;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp67;
		    fftw_real tmp68;
		    fftw_real tmp218;
		    fftw_real tmp219;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp67 = real_input[real_istride];
		    tmp68 = real_input[63 * real_istride];
		    tmp69 = tmp67 + tmp68;
		    tmp586 = tmp67 - tmp68;
		    tmp218 = imag_input[imag_istride];
		    tmp219 = imag_input[63 * imag_istride];
		    tmp220 = tmp218 - tmp219;
		    tmp590 = tmp218 + tmp219;
	       }
	       {
		    fftw_real tmp70;
		    fftw_real tmp71;
		    fftw_real tmp215;
		    fftw_real tmp216;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp70 = real_input[31 * real_istride];
		    tmp71 = real_input[33 * real_istride];
		    tmp72 = tmp70 + tmp71;
		    tmp589 = tmp71 - tmp70;
		    tmp215 = imag_input[31 * imag_istride];
		    tmp216 = imag_input[33 * imag_istride];
		    tmp217 = tmp215 - tmp216;
		    tmp587 = tmp216 + tmp215;
	       }
	       tmp73 = tmp69 + tmp72;
	       tmp205 = tmp69 - tmp72;
	       tmp221 = tmp217 + tmp220;
	       tmp399 = tmp220 - tmp217;
	       tmp588 = tmp586 - tmp587;
	       tmp804 = tmp586 + tmp587;
	       tmp591 = tmp589 + tmp590;
	       tmp805 = tmp590 - tmp589;
	  }
	  {
	       fftw_real tmp91;
	       fftw_real tmp608;
	       fftw_real tmp230;
	       fftw_real tmp612;
	       fftw_real tmp94;
	       fftw_real tmp611;
	       fftw_real tmp227;
	       fftw_real tmp609;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp89;
		    fftw_real tmp90;
		    fftw_real tmp228;
		    fftw_real tmp229;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp89 = real_input[9 * real_istride];
		    tmp90 = real_input[55 * real_istride];
		    tmp91 = tmp89 + tmp90;
		    tmp608 = tmp89 - tmp90;
		    tmp228 = imag_input[9 * imag_istride];
		    tmp229 = imag_input[55 * imag_istride];
		    tmp230 = tmp228 - tmp229;
		    tmp612 = tmp228 + tmp229;
	       }
	       {
		    fftw_real tmp92;
		    fftw_real tmp93;
		    fftw_real tmp225;
		    fftw_real tmp226;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp92 = real_input[23 * real_istride];
		    tmp93 = real_input[41 * real_istride];
		    tmp94 = tmp92 + tmp93;
		    tmp611 = tmp93 - tmp92;
		    tmp225 = imag_input[23 * imag_istride];
		    tmp226 = imag_input[41 * imag_istride];
		    tmp227 = tmp225 - tmp226;
		    tmp609 = tmp226 + tmp225;
	       }
	       tmp95 = tmp91 + tmp94;
	       tmp233 = tmp94 - tmp91;
	       tmp231 = tmp227 + tmp230;
	       tmp403 = tmp230 - tmp227;
	       tmp610 = tmp608 - tmp609;
	       tmp814 = tmp608 + tmp609;
	       tmp613 = tmp611 + tmp612;
	       tmp815 = tmp612 - tmp611;
	  }
	  {
	       fftw_real tmp76;
	       fftw_real tmp593;
	       fftw_real tmp211;
	       fftw_real tmp597;
	       fftw_real tmp79;
	       fftw_real tmp596;
	       fftw_real tmp208;
	       fftw_real tmp594;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp74;
		    fftw_real tmp75;
		    fftw_real tmp209;
		    fftw_real tmp210;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp74 = real_input[15 * real_istride];
		    tmp75 = real_input[49 * real_istride];
		    tmp76 = tmp74 + tmp75;
		    tmp593 = tmp74 - tmp75;
		    tmp209 = imag_input[15 * imag_istride];
		    tmp210 = imag_input[49 * imag_istride];
		    tmp211 = tmp209 - tmp210;
		    tmp597 = tmp209 + tmp210;
	       }
	       {
		    fftw_real tmp77;
		    fftw_real tmp78;
		    fftw_real tmp206;
		    fftw_real tmp207;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp77 = real_input[17 * real_istride];
		    tmp78 = real_input[47 * real_istride];
		    tmp79 = tmp77 + tmp78;
		    tmp596 = tmp78 - tmp77;
		    tmp206 = imag_input[17 * imag_istride];
		    tmp207 = imag_input[47 * imag_istride];
		    tmp208 = tmp206 - tmp207;
		    tmp594 = tmp207 + tmp206;
	       }
	       tmp80 = tmp76 + tmp79;
	       tmp214 = tmp79 - tmp76;
	       tmp212 = tmp208 + tmp211;
	       tmp400 = tmp211 - tmp208;
	       tmp595 = tmp593 - tmp594;
	       tmp807 = tmp593 + tmp594;
	       tmp598 = tmp596 + tmp597;
	       tmp808 = tmp597 - tmp596;
	  }
	  {
	       fftw_real tmp84;
	       fftw_real tmp601;
	       fftw_real tmp239;
	       fftw_real tmp605;
	       fftw_real tmp87;
	       fftw_real tmp604;
	       fftw_real tmp236;
	       fftw_real tmp602;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp82;
		    fftw_real tmp83;
		    fftw_real tmp237;
		    fftw_real tmp238;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp82 = real_input[7 * real_istride];
		    tmp83 = real_input[57 * real_istride];
		    tmp84 = tmp82 + tmp83;
		    tmp601 = tmp82 - tmp83;
		    tmp237 = imag_input[7 * imag_istride];
		    tmp238 = imag_input[57 * imag_istride];
		    tmp239 = tmp237 - tmp238;
		    tmp605 = tmp237 + tmp238;
	       }
	       {
		    fftw_real tmp85;
		    fftw_real tmp86;
		    fftw_real tmp234;
		    fftw_real tmp235;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp85 = real_input[25 * real_istride];
		    tmp86 = real_input[39 * real_istride];
		    tmp87 = tmp85 + tmp86;
		    tmp604 = tmp86 - tmp85;
		    tmp234 = imag_input[25 * imag_istride];
		    tmp235 = imag_input[39 * imag_istride];
		    tmp236 = tmp234 - tmp235;
		    tmp602 = tmp235 + tmp234;
	       }
	       tmp88 = tmp84 + tmp87;
	       tmp224 = tmp84 - tmp87;
	       tmp240 = tmp236 + tmp239;
	       tmp402 = tmp239 - tmp236;
	       tmp603 = tmp601 - tmp602;
	       tmp811 = tmp601 + tmp602;
	       tmp606 = tmp604 + tmp605;
	       tmp812 = tmp605 - tmp604;
	  }
	  {
	       fftw_real tmp813;
	       fftw_real tmp816;
	       fftw_real tmp592;
	       fftw_real tmp599;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp469;
		    fftw_real tmp470;
		    fftw_real tmp607;
		    fftw_real tmp614;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp81 = tmp73 + tmp80;
		    tmp469 = tmp73 - tmp80;
		    tmp470 = tmp402 + tmp403;
		    tmp471 = tmp469 - tmp470;
		    tmp503 = tmp469 + tmp470;
		    tmp404 = tmp402 - tmp403;
		    tmp607 = (K941544065 * tmp603) - (K336889853 * tmp606);
		    tmp614 = (K903989293 * tmp610) - (K427555093 * tmp613);
		    tmp615 = tmp607 + tmp614;
		    tmp724 = tmp614 - tmp607;
	       }
	       {
		    fftw_real tmp653;
		    fftw_real tmp654;
		    fftw_real tmp847;
		    fftw_real tmp848;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp653 = (K336889853 * tmp603) + (K941544065 * tmp606);
		    tmp654 = (K427555093 * tmp610) + (K903989293 * tmp613);
		    tmp655 = tmp653 - tmp654;
		    tmp721 = tmp653 + tmp654;
		    tmp847 = (K514102744 * tmp811) - (K857728610 * tmp812);
		    tmp848 = (K242980179 * tmp814) - (K970031253 * tmp815);
		    tmp849 = tmp847 + tmp848;
		    tmp910 = tmp848 - tmp847;
	       }
	       tmp813 = (K857728610 * tmp811) + (K514102744 * tmp812);
	       tmp816 = (K970031253 * tmp814) + (K242980179 * tmp815);
	       tmp817 = tmp813 - tmp816;
	       tmp907 = tmp813 + tmp816;
	       {
		    fftw_real tmp806;
		    fftw_real tmp809;
		    fftw_real tmp844;
		    fftw_real tmp845;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp806 = (K146730474 * tmp804) + (K989176509 * tmp805);
		    tmp809 = (K803207531 * tmp807) - (K595699304 * tmp808);
		    tmp810 = tmp806 - tmp809;
		    tmp909 = tmp806 + tmp809;
		    tmp844 = (K989176509 * tmp804) - (K146730474 * tmp805);
		    tmp845 = (K595699304 * tmp807) + (K803207531 * tmp808);
		    tmp846 = tmp844 - tmp845;
		    tmp906 = tmp844 + tmp845;
	       }
	       {
		    fftw_real tmp472;
		    fftw_real tmp473;
		    fftw_real tmp213;
		    fftw_real tmp222;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp96 = tmp88 + tmp95;
		    tmp472 = tmp95 - tmp88;
		    tmp473 = tmp399 + tmp400;
		    tmp474 = tmp472 + tmp473;
		    tmp504 = tmp473 - tmp472;
		    tmp401 = tmp399 - tmp400;
		    tmp213 = tmp205 - tmp212;
		    tmp222 = tmp214 + tmp221;
		    tmp223 = (K995184726 * tmp213) - (K098017140 * tmp222);
		    tmp285 = (K995184726 * tmp222) + (K098017140 * tmp213);
	       }
	       {
		    fftw_real tmp341;
		    fftw_real tmp342;
		    fftw_real tmp650;
		    fftw_real tmp651;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp341 = tmp205 + tmp212;
		    tmp342 = tmp221 - tmp214;
		    tmp343 = (K956940335 * tmp341) - (K290284677 * tmp342);
		    tmp357 = (K956940335 * tmp342) + (K290284677 * tmp341);
		    tmp650 = (K049067674 * tmp588) + (K998795456 * tmp591);
		    tmp651 = (K671558954 * tmp595) + (K740951125 * tmp598);
		    tmp652 = tmp650 - tmp651;
		    tmp723 = tmp650 + tmp651;
	       }
	       tmp592 = (K998795456 * tmp588) - (K049067674 * tmp591);
	       tmp599 = (K740951125 * tmp595) - (K671558954 * tmp598);
	       tmp600 = tmp592 + tmp599;
	       tmp720 = tmp592 - tmp599;
	       {
		    fftw_real tmp232;
		    fftw_real tmp241;
		    fftw_real tmp344;
		    fftw_real tmp345;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp232 = tmp224 - tmp231;
		    tmp241 = tmp233 + tmp240;
		    tmp242 = (K773010453 * tmp232) - (K634393284 * tmp241);
		    tmp286 = (K773010453 * tmp241) + (K634393284 * tmp232);
		    tmp344 = tmp240 - tmp233;
		    tmp345 = tmp224 + tmp231;
		    tmp346 = (K881921264 * tmp344) + (K471396736 * tmp345);
		    tmp358 = (K881921264 * tmp345) - (K471396736 * tmp344);
	       }
	  }
     }
     {
	  fftw_real tmp65;
	  fftw_real tmp421;
	  fftw_real tmp34;
	  fftw_real tmp414;
	  fftw_real tmp129;
	  fftw_real tmp423;
	  fftw_real tmp413;
	  fftw_real tmp424;
	  fftw_real tmp18;
	  fftw_real tmp66;
	  fftw_real tmp398;
	  ASSERT_ALIGNED_DOUBLE;
	  tmp65 = K2_000000000 * (tmp49 + tmp64);
	  tmp421 = K2_000000000 * (tmp417 - tmp420);
	  tmp18 = tmp10 + tmp17;
	  tmp34 = tmp18 + tmp33;
	  tmp414 = tmp18 - tmp33;
	  {
	       fftw_real tmp97;
	       fftw_real tmp128;
	       fftw_real tmp405;
	       fftw_real tmp412;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp97 = tmp81 + tmp96;
	       tmp128 = tmp112 + tmp127;
	       tmp129 = K2_000000000 * (tmp97 + tmp128);
	       tmp423 = tmp97 - tmp128;
	       tmp405 = tmp401 - tmp404;
	       tmp412 = tmp408 - tmp411;
	       tmp413 = K2_000000000 * (tmp405 - tmp412);
	       tmp424 = tmp405 + tmp412;
	  }
	  tmp66 = tmp34 + tmp65;
	  output[64 * ostride] = tmp66 - tmp129;
	  output[0] = tmp66 + tmp129;
	  tmp398 = tmp34 - tmp65;
	  output[32 * ostride] = tmp398 - tmp413;
	  output[96 * ostride] = tmp398 + tmp413;
	  {
	       fftw_real tmp422;
	       fftw_real tmp425;
	       fftw_real tmp426;
	       fftw_real tmp427;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp422 = tmp414 - tmp421;
	       tmp425 = K1_414213562 * (tmp423 - tmp424);
	       output[80 * ostride] = tmp422 - tmp425;
	       output[16 * ostride] = tmp422 + tmp425;
	       tmp426 = tmp414 + tmp421;
	       tmp427 = K1_414213562 * (tmp423 + tmp424);
	       output[48 * ostride] = tmp426 - tmp427;
	       output[112 * ostride] = tmp426 + tmp427;
	  }
     }
     {
	  fftw_real tmp432;
	  fftw_real tmp446;
	  fftw_real tmp442;
	  fftw_real tmp450;
	  fftw_real tmp435;
	  fftw_real tmp447;
	  fftw_real tmp439;
	  fftw_real tmp449;
	  fftw_real tmp428;
	  fftw_real tmp440;
	  fftw_real tmp441;
	  ASSERT_ALIGNED_DOUBLE;
	  tmp428 = tmp10 - tmp17;
	  tmp432 = tmp428 - tmp431;
	  tmp446 = tmp428 + tmp431;
	  tmp440 = tmp127 - tmp112;
	  tmp441 = tmp401 + tmp404;
	  tmp442 = tmp440 + tmp441;
	  tmp450 = tmp441 - tmp440;
	  {
	       fftw_real tmp433;
	       fftw_real tmp434;
	       fftw_real tmp437;
	       fftw_real tmp438;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp433 = tmp49 - tmp64;
	       tmp434 = tmp417 + tmp420;
	       tmp435 = K1_414213562 * (tmp433 - tmp434);
	       tmp447 = K1_414213562 * (tmp433 + tmp434);
	       tmp437 = tmp81 - tmp96;
	       tmp438 = tmp408 + tmp411;
	       tmp439 = tmp437 - tmp438;
	       tmp449 = tmp437 + tmp438;
	  }
	  {
	       fftw_real tmp436;
	       fftw_real tmp443;
	       fftw_real tmp444;
	       fftw_real tmp445;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp436 = tmp432 + tmp435;
	       tmp443 = (K1_847759065 * tmp439) - (K765366864 * tmp442);
	       output[72 * ostride] = tmp436 - tmp443;
	       output[8 * ostride] = tmp436 + tmp443;
	       tmp444 = tmp432 - tmp435;
	       tmp445 = (K1_847759065 * tmp442) + (K765366864 * tmp439);
	       output[40 * ostride] = tmp444 - tmp445;
	       output[104 * ostride] = tmp444 + tmp445;
	  }
	  {
	       fftw_real tmp448;
	       fftw_real tmp451;
	       fftw_real tmp452;
	       fftw_real tmp453;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp448 = tmp446 - tmp447;
	       tmp451 = (K765366864 * tmp449) - (K1_847759065 * tmp450);
	       output[88 * ostride] = tmp448 - tmp451;
	       output[24 * ostride] = tmp448 + tmp451;
	       tmp452 = tmp446 + tmp447;
	       tmp453 = (K765366864 * tmp450) + (K1_847759065 * tmp449);
	       output[56 * ostride] = tmp452 - tmp453;
	       output[120 * ostride] = tmp452 + tmp453;
	  }
     }
     {
	  fftw_real tmp203;
	  fftw_real tmp295;
	  fftw_real tmp164;
	  fftw_real tmp292;
	  fftw_real tmp283;
	  fftw_real tmp297;
	  fftw_real tmp291;
	  fftw_real tmp298;
	  fftw_real tmp144;
	  fftw_real tmp163;
	  fftw_real tmp204;
	  fftw_real tmp284;
	  ASSERT_ALIGNED_DOUBLE;
	  tmp203 = K2_000000000 * (tmp183 + tmp202);
	  tmp295 = K2_000000000 * (tmp293 - tmp294);
	  tmp144 = tmp134 + tmp143;
	  tmp163 = (K1_847759065 * tmp153) - (K765366864 * tmp162);
	  tmp164 = tmp144 + tmp163;
	  tmp292 = tmp144 - tmp163;
	  {
	       fftw_real tmp243;
	       fftw_real tmp282;
	       fftw_real tmp287;
	       fftw_real tmp290;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp243 = tmp223 + tmp242;
	       tmp282 = tmp262 + tmp281;
	       tmp283 = K2_000000000 * (tmp243 + tmp282);
	       tmp297 = tmp243 - tmp282;
	       tmp287 = tmp285 - tmp286;
	       tmp290 = tmp288 - tmp289;
	       tmp291 = K2_000000000 * (tmp287 - tmp290);
	       tmp298 = tmp287 + tmp290;
	  }
	  tmp204 = tmp164 + tmp203;
	  output[66 * ostride] = tmp204 - tmp283;
	  output[2 * ostride] = tmp204 + tmp283;
	  tmp284 = tmp164 - tmp203;
	  output[34 * ostride] = tmp284 - tmp291;
	  output[98 * ostride] = tmp284 + tmp291;
	  {
	       fftw_real tmp296;
	       fftw_real tmp299;
	       fftw_real tmp300;
	       fftw_real tmp301;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp296 = tmp292 - tmp295;
	       tmp299 = K1_414213562 * (tmp297 - tmp298);
	       output[82 * ostride] = tmp296 - tmp299;
	       output[18 * ostride] = tmp296 + tmp299;
	       tmp300 = tmp292 + tmp295;
	       tmp301 = K1_414213562 * (tmp297 + tmp298);
	       output[50 * ostride] = tmp300 - tmp301;
	       output[114 * ostride] = tmp300 + tmp301;
	  }
     }
     {
	  fftw_real tmp304;
	  fftw_real tmp318;
	  fftw_real tmp314;
	  fftw_real tmp322;
	  fftw_real tmp307;
	  fftw_real tmp319;
	  fftw_real tmp311;
	  fftw_real tmp321;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp302;
	       fftw_real tmp303;
	       fftw_real tmp312;
	       fftw_real tmp313;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp302 = tmp134 - tmp143;
	       tmp303 = (K1_847759065 * tmp162) + (K765366864 * tmp153);
	       tmp304 = tmp302 - tmp303;
	       tmp318 = tmp302 + tmp303;
	       tmp312 = tmp285 + tmp286;
	       tmp313 = tmp281 - tmp262;
	       tmp314 = tmp312 + tmp313;
	       tmp322 = tmp312 - tmp313;
	  }
	  {
	       fftw_real tmp305;
	       fftw_real tmp306;
	       fftw_real tmp309;
	       fftw_real tmp310;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp305 = tmp183 - tmp202;
	       tmp306 = tmp293 + tmp294;
	       tmp307 = K1_414213562 * (tmp305 - tmp306);
	       tmp319 = K1_414213562 * (tmp305 + tmp306);
	       tmp309 = tmp223 - tmp242;
	       tmp310 = tmp288 + tmp289;
	       tmp311 = tmp309 - tmp310;
	       tmp321 = tmp309 + tmp310;
	  }
	  {
	       fftw_real tmp308;
	       fftw_real tmp315;
	       fftw_real tmp316;
	       fftw_real tmp317;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp308 = tmp304 + tmp307;
	       tmp315 = (K1_847759065 * tmp311) - (K765366864 * tmp314);
	       output[74 * ostride] = tmp308 - tmp315;
	       output[10 * ostride] = tmp308 + tmp315;
	       tmp316 = tmp304 - tmp307;
	       tmp317 = (K1_847759065 * tmp314) + (K765366864 * tmp311);
	       output[42 * ostride] = tmp316 - tmp317;
	       output[106 * ostride] = tmp316 + tmp317;
	  }
	  {
	       fftw_real tmp320;
	       fftw_real tmp323;
	       fftw_real tmp324;
	       fftw_real tmp325;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp320 = tmp318 - tmp319;
	       tmp323 = (K765366864 * tmp321) - (K1_847759065 * tmp322);
	       output[90 * ostride] = tmp320 - tmp323;
	       output[26 * ostride] = tmp320 + tmp323;
	       tmp324 = tmp318 + tmp319;
	       tmp325 = (K765366864 * tmp322) + (K1_847759065 * tmp321);
	       output[58 * ostride] = tmp324 - tmp325;
	       output[122 * ostride] = tmp324 + tmp325;
	  }
     }
     {
	  fftw_real tmp460;
	  fftw_real tmp488;
	  fftw_real tmp487;
	  fftw_real tmp492;
	  fftw_real tmp483;
	  fftw_real tmp491;
	  fftw_real tmp467;
	  fftw_real tmp489;
	  fftw_real tmp468;
	  fftw_real tmp484;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp456;
	       fftw_real tmp459;
	       fftw_real tmp485;
	       fftw_real tmp486;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp456 = tmp454 - tmp455;
	       tmp459 = K1_414213562 * (tmp457 - tmp458);
	       tmp460 = tmp456 + tmp459;
	       tmp488 = tmp456 - tmp459;
	       tmp485 = (K980785280 * tmp474) + (K195090322 * tmp471);
	       tmp486 = (K831469612 * tmp481) + (K555570233 * tmp478);
	       tmp487 = K2_000000000 * (tmp485 - tmp486);
	       tmp492 = tmp485 + tmp486;
	  }
	  {
	       fftw_real tmp475;
	       fftw_real tmp482;
	       fftw_real tmp463;
	       fftw_real tmp466;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp475 = (K980785280 * tmp471) - (K195090322 * tmp474);
	       tmp482 = (K831469612 * tmp478) - (K555570233 * tmp481);
	       tmp483 = K2_000000000 * (tmp475 + tmp482);
	       tmp491 = tmp475 - tmp482;
	       tmp463 = tmp461 - tmp462;
	       tmp466 = tmp464 + tmp465;
	       tmp467 = (K1_847759065 * tmp463) - (K765366864 * tmp466);
	       tmp489 = (K1_847759065 * tmp466) + (K765366864 * tmp463);
	  }
	  tmp468 = tmp460 + tmp467;
	  output[68 * ostride] = tmp468 - tmp483;
	  output[4 * ostride] = tmp468 + tmp483;
	  tmp484 = tmp460 - tmp467;
	  output[36 * ostride] = tmp484 - tmp487;
	  output[100 * ostride] = tmp484 + tmp487;
	  {
	       fftw_real tmp490;
	       fftw_real tmp493;
	       fftw_real tmp494;
	       fftw_real tmp495;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp490 = tmp488 - tmp489;
	       tmp493 = K1_414213562 * (tmp491 - tmp492);
	       output[84 * ostride] = tmp490 - tmp493;
	       output[20 * ostride] = tmp490 + tmp493;
	       tmp494 = tmp488 + tmp489;
	       tmp495 = K1_414213562 * (tmp491 + tmp492);
	       output[52 * ostride] = tmp494 - tmp495;
	       output[116 * ostride] = tmp494 + tmp495;
	  }
     }
     {
	  fftw_real tmp498;
	  fftw_real tmp514;
	  fftw_real tmp513;
	  fftw_real tmp518;
	  fftw_real tmp509;
	  fftw_real tmp517;
	  fftw_real tmp501;
	  fftw_real tmp515;
	  fftw_real tmp502;
	  fftw_real tmp510;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp496;
	       fftw_real tmp497;
	       fftw_real tmp511;
	       fftw_real tmp512;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp496 = tmp454 + tmp455;
	       tmp497 = K1_414213562 * (tmp457 + tmp458);
	       tmp498 = tmp496 - tmp497;
	       tmp514 = tmp496 + tmp497;
	       tmp511 = (K831469612 * tmp504) + (K555570233 * tmp503);
	       tmp512 = (K980785280 * tmp507) - (K195090322 * tmp506);
	       tmp513 = K2_000000000 * (tmp511 - tmp512);
	       tmp518 = tmp511 + tmp512;
	  }
	  {
	       fftw_real tmp505;
	       fftw_real tmp508;
	       fftw_real tmp499;
	       fftw_real tmp500;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp505 = (K831469612 * tmp503) - (K555570233 * tmp504);
	       tmp508 = (K980785280 * tmp506) + (K195090322 * tmp507);
	       tmp509 = K2_000000000 * (tmp505 - tmp508);
	       tmp517 = tmp505 + tmp508;
	       tmp499 = tmp461 + tmp462;
	       tmp500 = tmp465 - tmp464;
	       tmp501 = (K765366864 * tmp499) - (K1_847759065 * tmp500);
	       tmp515 = (K765366864 * tmp500) + (K1_847759065 * tmp499);
	  }
	  tmp502 = tmp498 + tmp501;
	  output[76 * ostride] = tmp502 - tmp509;
	  output[12 * ostride] = tmp502 + tmp509;
	  tmp510 = tmp498 - tmp501;
	  output[44 * ostride] = tmp510 - tmp513;
	  output[108 * ostride] = tmp510 + tmp513;
	  {
	       fftw_real tmp516;
	       fftw_real tmp519;
	       fftw_real tmp520;
	       fftw_real tmp521;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp516 = tmp514 - tmp515;
	       tmp519 = K1_414213562 * (tmp517 - tmp518);
	       output[92 * ostride] = tmp516 - tmp519;
	       output[28 * ostride] = tmp516 + tmp519;
	       tmp520 = tmp514 + tmp515;
	       tmp521 = K1_414213562 * (tmp517 + tmp518);
	       output[60 * ostride] = tmp520 - tmp521;
	       output[124 * ostride] = tmp520 + tmp521;
	  }
     }
     {
	  fftw_real tmp339;
	  fftw_real tmp367;
	  fftw_real tmp332;
	  fftw_real tmp364;
	  fftw_real tmp355;
	  fftw_real tmp369;
	  fftw_real tmp363;
	  fftw_real tmp370;
	  fftw_real tmp328;
	  fftw_real tmp331;
	  fftw_real tmp340;
	  fftw_real tmp356;
	  ASSERT_ALIGNED_DOUBLE;
	  tmp339 = K2_000000000 * (tmp335 - tmp338);
	  tmp367 = K2_000000000 * (tmp365 - tmp366);
	  tmp328 = tmp326 - tmp327;
	  tmp331 = (K765366864 * tmp329) - (K1_847759065 * tmp330);
	  tmp332 = tmp328 + tmp331;
	  tmp364 = tmp328 - tmp331;
	  {
	       fftw_real tmp347;
	       fftw_real tmp354;
	       fftw_real tmp359;
	       fftw_real tmp362;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp347 = tmp343 - tmp346;
	       tmp354 = tmp350 + tmp353;
	       tmp355 = K2_000000000 * (tmp347 + tmp354);
	       tmp369 = tmp347 - tmp354;
	       tmp359 = tmp357 - tmp358;
	       tmp362 = tmp360 - tmp361;
	       tmp363 = K2_000000000 * (tmp359 - tmp362);
	       tmp370 = tmp359 + tmp362;
	  }
	  tmp340 = tmp332 + tmp339;
	  output[70 * ostride] = tmp340 - tmp355;
	  output[6 * ostride] = tmp340 + tmp355;
	  tmp356 = tmp332 - tmp339;
	  output[38 * ostride] = tmp356 - tmp363;
	  output[102 * ostride] = tmp356 + tmp363;
	  {
	       fftw_real tmp368;
	       fftw_real tmp371;
	       fftw_real tmp372;
	       fftw_real tmp373;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp368 = tmp364 - tmp367;
	       tmp371 = K1_414213562 * (tmp369 - tmp370);
	       output[86 * ostride] = tmp368 - tmp371;
	       output[22 * ostride] = tmp368 + tmp371;
	       tmp372 = tmp364 + tmp367;
	       tmp373 = K1_414213562 * (tmp369 + tmp370);
	       output[54 * ostride] = tmp372 - tmp373;
	       output[118 * ostride] = tmp372 + tmp373;
	  }
     }
     {
	  fftw_real tmp376;
	  fftw_real tmp390;
	  fftw_real tmp386;
	  fftw_real tmp394;
	  fftw_real tmp379;
	  fftw_real tmp391;
	  fftw_real tmp383;
	  fftw_real tmp393;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp374;
	       fftw_real tmp375;
	       fftw_real tmp384;
	       fftw_real tmp385;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp374 = tmp326 + tmp327;
	       tmp375 = (K765366864 * tmp330) + (K1_847759065 * tmp329);
	       tmp376 = tmp374 - tmp375;
	       tmp390 = tmp374 + tmp375;
	       tmp384 = tmp357 + tmp358;
	       tmp385 = tmp353 - tmp350;
	       tmp386 = tmp384 + tmp385;
	       tmp394 = tmp384 - tmp385;
	  }
	  {
	       fftw_real tmp377;
	       fftw_real tmp378;
	       fftw_real tmp381;
	       fftw_real tmp382;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp377 = tmp335 + tmp338;
	       tmp378 = tmp365 + tmp366;
	       tmp379 = K1_414213562 * (tmp377 - tmp378);
	       tmp391 = K1_414213562 * (tmp377 + tmp378);
	       tmp381 = tmp343 + tmp346;
	       tmp382 = tmp360 + tmp361;
	       tmp383 = tmp381 - tmp382;
	       tmp393 = tmp381 + tmp382;
	  }
	  {
	       fftw_real tmp380;
	       fftw_real tmp387;
	       fftw_real tmp388;
	       fftw_real tmp389;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp380 = tmp376 + tmp379;
	       tmp387 = (K1_847759065 * tmp383) - (K765366864 * tmp386);
	       output[78 * ostride] = tmp380 - tmp387;
	       output[14 * ostride] = tmp380 + tmp387;
	       tmp388 = tmp376 - tmp379;
	       tmp389 = (K1_847759065 * tmp386) + (K765366864 * tmp383);
	       output[46 * ostride] = tmp388 - tmp389;
	       output[110 * ostride] = tmp388 + tmp389;
	  }
	  {
	       fftw_real tmp392;
	       fftw_real tmp395;
	       fftw_real tmp396;
	       fftw_real tmp397;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp392 = tmp390 - tmp391;
	       tmp395 = (K765366864 * tmp393) - (K1_847759065 * tmp394);
	       output[94 * ostride] = tmp392 - tmp395;
	       output[30 * ostride] = tmp392 + tmp395;
	       tmp396 = tmp390 + tmp391;
	       tmp397 = (K765366864 * tmp394) + (K1_847759065 * tmp393);
	       output[62 * ostride] = tmp396 - tmp397;
	       output[126 * ostride] = tmp396 + tmp397;
	  }
     }
     {
	  fftw_real tmp584;
	  fftw_real tmp672;
	  fftw_real tmp553;
	  fftw_real tmp665;
	  fftw_real tmp648;
	  fftw_real tmp674;
	  fftw_real tmp664;
	  fftw_real tmp675;
	  fftw_real tmp537;
	  fftw_real tmp552;
	  fftw_real tmp585;
	  fftw_real tmp649;
	  ASSERT_ALIGNED_DOUBLE;
	  tmp584 = K2_000000000 * (tmp568 + tmp583);
	  tmp672 = K2_000000000 * (tmp668 - tmp671);
	  tmp537 = tmp529 + tmp536;
	  tmp552 = K2_000000000 * (tmp544 + tmp551);
	  tmp553 = tmp537 + tmp552;
	  tmp665 = tmp537 - tmp552;
	  {
	       fftw_real tmp616;
	       fftw_real tmp647;
	       fftw_real tmp656;
	       fftw_real tmp663;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp616 = tmp600 + tmp615;
	       tmp647 = tmp631 + tmp646;
	       tmp648 = K2_000000000 * (tmp616 + tmp647);
	       tmp674 = tmp616 - tmp647;
	       tmp656 = tmp652 - tmp655;
	       tmp663 = tmp659 - tmp662;
	       tmp664 = K2_000000000 * (tmp656 - tmp663);
	       tmp675 = tmp656 + tmp663;
	  }
	  tmp585 = tmp553 + tmp584;
	  output[65 * ostride] = tmp585 - tmp648;
	  output[ostride] = tmp585 + tmp648;
	  tmp649 = tmp553 - tmp584;
	  output[33 * ostride] = tmp649 - tmp664;
	  output[97 * ostride] = tmp649 + tmp664;
	  {
	       fftw_real tmp673;
	       fftw_real tmp676;
	       fftw_real tmp677;
	       fftw_real tmp678;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp673 = tmp665 - tmp672;
	       tmp676 = K1_414213562 * (tmp674 - tmp675);
	       output[81 * ostride] = tmp673 - tmp676;
	       output[17 * ostride] = tmp673 + tmp676;
	       tmp677 = tmp665 + tmp672;
	       tmp678 = K1_414213562 * (tmp674 + tmp675);
	       output[49 * ostride] = tmp677 - tmp678;
	       output[113 * ostride] = tmp677 + tmp678;
	  }
     }
     {
	  fftw_real tmp683;
	  fftw_real tmp697;
	  fftw_real tmp693;
	  fftw_real tmp701;
	  fftw_real tmp686;
	  fftw_real tmp698;
	  fftw_real tmp690;
	  fftw_real tmp700;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp679;
	       fftw_real tmp682;
	       fftw_real tmp691;
	       fftw_real tmp692;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp679 = tmp529 - tmp536;
	       tmp682 = K2_000000000 * (tmp680 - tmp681);
	       tmp683 = tmp679 - tmp682;
	       tmp697 = tmp679 + tmp682;
	       tmp691 = tmp652 + tmp655;
	       tmp692 = tmp646 - tmp631;
	       tmp693 = tmp691 + tmp692;
	       tmp701 = tmp691 - tmp692;
	  }
	  {
	       fftw_real tmp684;
	       fftw_real tmp685;
	       fftw_real tmp688;
	       fftw_real tmp689;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp684 = tmp568 - tmp583;
	       tmp685 = tmp668 + tmp671;
	       tmp686 = K1_414213562 * (tmp684 - tmp685);
	       tmp698 = K1_414213562 * (tmp684 + tmp685);
	       tmp688 = tmp600 - tmp615;
	       tmp689 = tmp659 + tmp662;
	       tmp690 = tmp688 - tmp689;
	       tmp700 = tmp688 + tmp689;
	  }
	  {
	       fftw_real tmp687;
	       fftw_real tmp694;
	       fftw_real tmp695;
	       fftw_real tmp696;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp687 = tmp683 + tmp686;
	       tmp694 = (K1_847759065 * tmp690) - (K765366864 * tmp693);
	       output[73 * ostride] = tmp687 - tmp694;
	       output[9 * ostride] = tmp687 + tmp694;
	       tmp695 = tmp683 - tmp686;
	       tmp696 = (K1_847759065 * tmp693) + (K765366864 * tmp690);
	       output[41 * ostride] = tmp695 - tmp696;
	       output[105 * ostride] = tmp695 + tmp696;
	  }
	  {
	       fftw_real tmp699;
	       fftw_real tmp702;
	       fftw_real tmp703;
	       fftw_real tmp704;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp699 = tmp697 - tmp698;
	       tmp702 = (K765366864 * tmp700) - (K1_847759065 * tmp701);
	       output[89 * ostride] = tmp699 - tmp702;
	       output[25 * ostride] = tmp699 + tmp702;
	       tmp703 = tmp697 + tmp698;
	       tmp704 = (K765366864 * tmp701) + (K1_847759065 * tmp700);
	       output[57 * ostride] = tmp703 - tmp704;
	       output[121 * ostride] = tmp703 + tmp704;
	  }
     }
     {
	  fftw_real tmp711;
	  fftw_real tmp739;
	  fftw_real tmp718;
	  fftw_real tmp740;
	  fftw_real tmp726;
	  fftw_real tmp736;
	  fftw_real tmp733;
	  fftw_real tmp737;
	  fftw_real tmp742;
	  fftw_real tmp743;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp707;
	       fftw_real tmp710;
	       fftw_real tmp714;
	       fftw_real tmp717;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp707 = tmp705 - tmp706;
	       tmp710 = K1_414213562 * (tmp708 - tmp709);
	       tmp711 = tmp707 + tmp710;
	       tmp739 = tmp707 - tmp710;
	       tmp714 = tmp712 - tmp713;
	       tmp717 = tmp715 + tmp716;
	       tmp718 = (K1_847759065 * tmp714) - (K765366864 * tmp717);
	       tmp740 = (K1_847759065 * tmp717) + (K765366864 * tmp714);
	       {
		    fftw_real tmp722;
		    fftw_real tmp725;
		    fftw_real tmp729;
		    fftw_real tmp732;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp722 = tmp720 - tmp721;
		    tmp725 = tmp723 + tmp724;
		    tmp726 = (K980785280 * tmp722) - (K195090322 * tmp725);
		    tmp736 = (K980785280 * tmp725) + (K195090322 * tmp722);
		    tmp729 = tmp727 - tmp728;
		    tmp732 = tmp730 + tmp731;
		    tmp733 = (K831469612 * tmp729) - (K555570233 * tmp732);
		    tmp737 = (K831469612 * tmp732) + (K555570233 * tmp729);
	       }
	       tmp742 = tmp726 - tmp733;
	       tmp743 = tmp736 + tmp737;
	  }
	  {
	       fftw_real tmp719;
	       fftw_real tmp734;
	       fftw_real tmp735;
	       fftw_real tmp738;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp719 = tmp711 + tmp718;
	       tmp734 = K2_000000000 * (tmp726 + tmp733);
	       output[69 * ostride] = tmp719 - tmp734;
	       output[5 * ostride] = tmp719 + tmp734;
	       tmp735 = tmp711 - tmp718;
	       tmp738 = K2_000000000 * (tmp736 - tmp737);
	       output[37 * ostride] = tmp735 - tmp738;
	       output[101 * ostride] = tmp735 + tmp738;
	  }
	  {
	       fftw_real tmp741;
	       fftw_real tmp744;
	       fftw_real tmp745;
	       fftw_real tmp746;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp741 = tmp739 - tmp740;
	       tmp744 = K1_414213562 * (tmp742 - tmp743);
	       output[85 * ostride] = tmp741 - tmp744;
	       output[21 * ostride] = tmp741 + tmp744;
	       tmp745 = tmp739 + tmp740;
	       tmp746 = K1_414213562 * (tmp742 + tmp743);
	       output[53 * ostride] = tmp745 - tmp746;
	       output[117 * ostride] = tmp745 + tmp746;
	  }
     }
     {
	  fftw_real tmp749;
	  fftw_real tmp765;
	  fftw_real tmp752;
	  fftw_real tmp766;
	  fftw_real tmp756;
	  fftw_real tmp762;
	  fftw_real tmp759;
	  fftw_real tmp763;
	  fftw_real tmp768;
	  fftw_real tmp769;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp747;
	       fftw_real tmp748;
	       fftw_real tmp750;
	       fftw_real tmp751;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp747 = tmp705 + tmp706;
	       tmp748 = K1_414213562 * (tmp708 + tmp709);
	       tmp749 = tmp747 - tmp748;
	       tmp765 = tmp747 + tmp748;
	       tmp750 = tmp712 + tmp713;
	       tmp751 = tmp715 - tmp716;
	       tmp752 = (K765366864 * tmp750) - (K1_847759065 * tmp751);
	       tmp766 = (K765366864 * tmp751) + (K1_847759065 * tmp750);
	       {
		    fftw_real tmp754;
		    fftw_real tmp755;
		    fftw_real tmp757;
		    fftw_real tmp758;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp754 = tmp720 + tmp721;
		    tmp755 = tmp723 - tmp724;
		    tmp756 = (K831469612 * tmp754) - (K555570233 * tmp755);
		    tmp762 = (K831469612 * tmp755) + (K555570233 * tmp754);
		    tmp757 = tmp730 - tmp731;
		    tmp758 = tmp727 + tmp728;
		    tmp759 = (K980785280 * tmp757) + (K195090322 * tmp758);
		    tmp763 = (K980785280 * tmp758) - (K195090322 * tmp757);
	       }
	       tmp768 = tmp756 + tmp759;
	       tmp769 = tmp762 + tmp763;
	  }
	  {
	       fftw_real tmp753;
	       fftw_real tmp760;
	       fftw_real tmp761;
	       fftw_real tmp764;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp753 = tmp749 + tmp752;
	       tmp760 = K2_000000000 * (tmp756 - tmp759);
	       output[77 * ostride] = tmp753 - tmp760;
	       output[13 * ostride] = tmp753 + tmp760;
	       tmp761 = tmp749 - tmp752;
	       tmp764 = K2_000000000 * (tmp762 - tmp763);
	       output[45 * ostride] = tmp761 - tmp764;
	       output[109 * ostride] = tmp761 + tmp764;
	  }
	  {
	       fftw_real tmp767;
	       fftw_real tmp770;
	       fftw_real tmp771;
	       fftw_real tmp772;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp767 = tmp765 - tmp766;
	       tmp770 = K1_414213562 * (tmp768 - tmp769);
	       output[93 * ostride] = tmp767 - tmp770;
	       output[29 * ostride] = tmp767 + tmp770;
	       tmp771 = tmp765 + tmp766;
	       tmp772 = K1_414213562 * (tmp768 + tmp769);
	       output[61 * ostride] = tmp771 - tmp772;
	       output[125 * ostride] = tmp771 + tmp772;
	  }
     }
     {
	  fftw_real tmp802;
	  fftw_real tmp858;
	  fftw_real tmp864;
	  fftw_real tmp842;
	  fftw_real tmp834;
	  fftw_real tmp859;
	  fftw_real tmp787;
	  fftw_real tmp835;
	  fftw_real tmp850;
	  fftw_real tmp857;
	  fftw_real tmp803;
	  fftw_real tmp863;
	  ASSERT_ALIGNED_DOUBLE;
	  tmp802 = K2_000000000 * (tmp794 + tmp801);
	  tmp850 = tmp846 + tmp849;
	  tmp857 = tmp853 + tmp856;
	  tmp858 = tmp850 - tmp857;
	  tmp864 = K2_000000000 * (tmp850 + tmp857);
	  tmp842 = K2_000000000 * (tmp838 - tmp841);
	  {
	       fftw_real tmp818;
	       fftw_real tmp833;
	       fftw_real tmp779;
	       fftw_real tmp786;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp818 = tmp810 - tmp817;
	       tmp833 = tmp825 - tmp832;
	       tmp834 = K2_000000000 * (tmp818 - tmp833);
	       tmp859 = tmp818 + tmp833;
	       tmp779 = tmp775 + tmp778;
	       tmp786 = K2_000000000 * (tmp782 - tmp785);
	       tmp787 = tmp779 + tmp786;
	       tmp835 = tmp779 - tmp786;
	  }
	  tmp803 = tmp787 - tmp802;
	  output[35 * ostride] = tmp803 - tmp834;
	  output[99 * ostride] = tmp803 + tmp834;
	  tmp863 = tmp787 + tmp802;
	  output[67 * ostride] = tmp863 - tmp864;
	  output[3 * ostride] = tmp863 + tmp864;
	  {
	       fftw_real tmp843;
	       fftw_real tmp860;
	       fftw_real tmp861;
	       fftw_real tmp862;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp843 = tmp835 - tmp842;
	       tmp860 = K1_414213562 * (tmp858 - tmp859);
	       output[83 * ostride] = tmp843 - tmp860;
	       output[19 * ostride] = tmp843 + tmp860;
	       tmp861 = tmp835 + tmp842;
	       tmp862 = K1_414213562 * (tmp859 + tmp858);
	       output[51 * ostride] = tmp861 - tmp862;
	       output[115 * ostride] = tmp861 + tmp862;
	  }
     }
     {
	  fftw_real tmp869;
	  fftw_real tmp883;
	  fftw_real tmp879;
	  fftw_real tmp887;
	  fftw_real tmp872;
	  fftw_real tmp884;
	  fftw_real tmp876;
	  fftw_real tmp886;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp865;
	       fftw_real tmp868;
	       fftw_real tmp877;
	       fftw_real tmp878;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp865 = tmp775 - tmp778;
	       tmp868 = K2_000000000 * (tmp866 - tmp867);
	       tmp869 = tmp865 + tmp868;
	       tmp883 = tmp865 - tmp868;
	       tmp877 = tmp810 + tmp817;
	       tmp878 = tmp856 - tmp853;
	       tmp879 = tmp877 - tmp878;
	       tmp887 = tmp877 + tmp878;
	  }
	  {
	       fftw_real tmp870;
	       fftw_real tmp871;
	       fftw_real tmp874;
	       fftw_real tmp875;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp870 = tmp794 - tmp801;
	       tmp871 = tmp838 + tmp841;
	       tmp872 = K1_414213562 * (tmp870 + tmp871);
	       tmp884 = K1_414213562 * (tmp870 - tmp871);
	       tmp874 = tmp846 - tmp849;
	       tmp875 = tmp825 + tmp832;
	       tmp876 = tmp874 + tmp875;
	       tmp886 = tmp874 - tmp875;
	  }
	  {
	       fftw_real tmp873;
	       fftw_real tmp880;
	       fftw_real tmp881;
	       fftw_real tmp882;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp873 = tmp869 - tmp872;
	       tmp880 = (K765366864 * tmp876) - (K1_847759065 * tmp879);
	       output[91 * ostride] = tmp873 - tmp880;
	       output[27 * ostride] = tmp873 + tmp880;
	       tmp881 = tmp869 + tmp872;
	       tmp882 = (K1_847759065 * tmp876) + (K765366864 * tmp879);
	       output[59 * ostride] = tmp881 - tmp882;
	       output[123 * ostride] = tmp881 + tmp882;
	  }
	  {
	       fftw_real tmp885;
	       fftw_real tmp888;
	       fftw_real tmp889;
	       fftw_real tmp890;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp885 = tmp883 + tmp884;
	       tmp888 = (K1_847759065 * tmp886) - (K765366864 * tmp887);
	       output[75 * ostride] = tmp885 - tmp888;
	       output[11 * ostride] = tmp885 + tmp888;
	       tmp889 = tmp883 - tmp884;
	       tmp890 = (K765366864 * tmp886) + (K1_847759065 * tmp887);
	       output[43 * ostride] = tmp889 - tmp890;
	       output[107 * ostride] = tmp889 + tmp890;
	  }
     }
     {
	  fftw_real tmp897;
	  fftw_real tmp925;
	  fftw_real tmp904;
	  fftw_real tmp926;
	  fftw_real tmp912;
	  fftw_real tmp922;
	  fftw_real tmp919;
	  fftw_real tmp923;
	  fftw_real tmp928;
	  fftw_real tmp929;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp893;
	       fftw_real tmp896;
	       fftw_real tmp900;
	       fftw_real tmp903;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp893 = tmp891 - tmp892;
	       tmp896 = K1_414213562 * (tmp894 - tmp895);
	       tmp897 = tmp893 + tmp896;
	       tmp925 = tmp893 - tmp896;
	       tmp900 = tmp898 - tmp899;
	       tmp903 = tmp901 + tmp902;
	       tmp904 = (K1_847759065 * tmp900) - (K765366864 * tmp903);
	       tmp926 = (K1_847759065 * tmp903) + (K765366864 * tmp900);
	       {
		    fftw_real tmp908;
		    fftw_real tmp911;
		    fftw_real tmp915;
		    fftw_real tmp918;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp908 = tmp906 - tmp907;
		    tmp911 = tmp909 + tmp910;
		    tmp912 = (K980785280 * tmp908) - (K195090322 * tmp911);
		    tmp922 = (K980785280 * tmp911) + (K195090322 * tmp908);
		    tmp915 = tmp913 - tmp914;
		    tmp918 = tmp916 - tmp917;
		    tmp919 = (K831469612 * tmp915) - (K555570233 * tmp918);
		    tmp923 = (K555570233 * tmp915) + (K831469612 * tmp918);
	       }
	       tmp928 = tmp912 - tmp919;
	       tmp929 = tmp922 + tmp923;
	  }
	  {
	       fftw_real tmp905;
	       fftw_real tmp920;
	       fftw_real tmp921;
	       fftw_real tmp924;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp905 = tmp897 + tmp904;
	       tmp920 = K2_000000000 * (tmp912 + tmp919);
	       output[71 * ostride] = tmp905 - tmp920;
	       output[7 * ostride] = tmp905 + tmp920;
	       tmp921 = tmp897 - tmp904;
	       tmp924 = K2_000000000 * (tmp922 - tmp923);
	       output[39 * ostride] = tmp921 - tmp924;
	       output[103 * ostride] = tmp921 + tmp924;
	  }
	  {
	       fftw_real tmp927;
	       fftw_real tmp930;
	       fftw_real tmp931;
	       fftw_real tmp932;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp927 = tmp925 - tmp926;
	       tmp930 = K1_414213562 * (tmp928 - tmp929);
	       output[87 * ostride] = tmp927 - tmp930;
	       output[23 * ostride] = tmp927 + tmp930;
	       tmp931 = tmp925 + tmp926;
	       tmp932 = K1_414213562 * (tmp928 + tmp929);
	       output[55 * ostride] = tmp931 - tmp932;
	       output[119 * ostride] = tmp931 + tmp932;
	  }
     }
     {
	  fftw_real tmp935;
	  fftw_real tmp951;
	  fftw_real tmp938;
	  fftw_real tmp952;
	  fftw_real tmp942;
	  fftw_real tmp948;
	  fftw_real tmp945;
	  fftw_real tmp949;
	  fftw_real tmp954;
	  fftw_real tmp955;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp933;
	       fftw_real tmp934;
	       fftw_real tmp936;
	       fftw_real tmp937;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp933 = tmp891 + tmp892;
	       tmp934 = K1_414213562 * (tmp895 + tmp894);
	       tmp935 = tmp933 - tmp934;
	       tmp951 = tmp933 + tmp934;
	       tmp936 = tmp898 + tmp899;
	       tmp937 = tmp901 - tmp902;
	       tmp938 = (K765366864 * tmp936) - (K1_847759065 * tmp937);
	       tmp952 = (K765366864 * tmp937) + (K1_847759065 * tmp936);
	       {
		    fftw_real tmp940;
		    fftw_real tmp941;
		    fftw_real tmp943;
		    fftw_real tmp944;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp940 = tmp909 - tmp910;
		    tmp941 = tmp906 + tmp907;
		    tmp942 = (K831469612 * tmp940) + (K555570233 * tmp941);
		    tmp948 = (K831469612 * tmp941) - (K555570233 * tmp940);
		    tmp943 = tmp913 + tmp914;
		    tmp944 = tmp916 + tmp917;
		    tmp945 = (K980785280 * tmp943) - (K195090322 * tmp944);
		    tmp949 = (K195090322 * tmp943) + (K980785280 * tmp944);
	       }
	       tmp954 = tmp948 + tmp949;
	       tmp955 = tmp942 + tmp945;
	  }
	  {
	       fftw_real tmp939;
	       fftw_real tmp946;
	       fftw_real tmp947;
	       fftw_real tmp950;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp939 = tmp935 - tmp938;
	       tmp946 = K2_000000000 * (tmp942 - tmp945);
	       output[47 * ostride] = tmp939 - tmp946;
	       output[111 * ostride] = tmp939 + tmp946;
	       tmp947 = tmp935 + tmp938;
	       tmp950 = K2_000000000 * (tmp948 - tmp949);
	       output[79 * ostride] = tmp947 - tmp950;
	       output[15 * ostride] = tmp947 + tmp950;
	  }
	  {
	       fftw_real tmp953;
	       fftw_real tmp956;
	       fftw_real tmp957;
	       fftw_real tmp958;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp953 = tmp951 - tmp952;
	       tmp956 = K1_414213562 * (tmp954 - tmp955);
	       output[95 * ostride] = tmp953 - tmp956;
	       output[31 * ostride] = tmp953 + tmp956;
	       tmp957 = tmp951 + tmp952;
	       tmp958 = K1_414213562 * (tmp955 + tmp954);
	       output[63 * ostride] = tmp957 - tmp958;
	       output[127 * ostride] = tmp957 + tmp958;
	  }
     }
}

fftw_codelet_desc fftw_hc2real_128_desc =
{
     "fftw_hc2real_128",
     (void (*)()) fftw_hc2real_128,
     128,
     FFTW_BACKWARD,
     FFTW_HC2REAL,
     2831,
     0,
     (const int *) 0,
};
