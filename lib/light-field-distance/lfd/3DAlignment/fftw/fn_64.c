/*
 * Copyright (c) 1997-1999 Massachusetts Institute of Technology
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 *
 */

/* This file was automatically generated --- DO NOT EDIT */
/* Generated on Sun Nov  7 20:43:52 EST 1999 */

#include "fftw-int.h"
#include "fftw.h"

/* Generated by: ./genfft -magic-alignment-check -magic-twiddle-load-all -magic-variables 4 -magic-loopi -notwiddle 64 */

/*
 * This function contains 912 FP additions, 248 FP multiplications,
 * (or, 808 additions, 144 multiplications, 104 fused multiply/add),
 * 156 stack variables, and 256 memory accesses
 */
static const fftw_real K555570233 = FFTW_KONST(+0.555570233019602224742830813948532874374937191);
static const fftw_real K831469612 = FFTW_KONST(+0.831469612302545237078788377617905756738560812);
static const fftw_real K881921264 = FFTW_KONST(+0.881921264348355029712756863660388349508442621);
static const fftw_real K471396736 = FFTW_KONST(+0.471396736825997648556387625905254377657460319);
static const fftw_real K290284677 = FFTW_KONST(+0.290284677254462367636192375817395274691476278);
static const fftw_real K956940335 = FFTW_KONST(+0.956940335732208864935797886980269969482849206);
static const fftw_real K980785280 = FFTW_KONST(+0.980785280403230449126182236134239036973933731);
static const fftw_real K195090322 = FFTW_KONST(+0.195090322016128267848284868477022240927691618);
static const fftw_real K995184726 = FFTW_KONST(+0.995184726672196886244836953109479921575474869);
static const fftw_real K098017140 = FFTW_KONST(+0.098017140329560601994195563888641845861136673);
static const fftw_real K773010453 = FFTW_KONST(+0.773010453362736960810906609758469800971041293);
static const fftw_real K634393284 = FFTW_KONST(+0.634393284163645498215171613225493370675687095);
static const fftw_real K923879532 = FFTW_KONST(+0.923879532511286756128183189396788286822416626);
static const fftw_real K382683432 = FFTW_KONST(+0.382683432365089771728459984030398866761344562);
static const fftw_real K707106781 = FFTW_KONST(+0.707106781186547524400844362104849039284835938);

/*
 * Generator Id's : 
 * $Id: exprdag.ml,v 1.41 1999/05/26 15:44:14 fftw Exp $
 * $Id: fft.ml,v 1.43 1999/05/17 19:44:18 fftw Exp $
 * $Id: to_c.ml,v 1.25 1999/10/26 21:41:32 stevenj Exp $
 */

void fftw_no_twiddle_64(const fftw_complex *input, fftw_complex *output, int istride, int ostride)
{
     fftw_real tmp193;
     fftw_real tmp471;
     fftw_real tmp15;
     fftw_real tmp815;
     fftw_real tmp719;
     fftw_real tmp781;
     fftw_real tmp142;
     fftw_real tmp849;
     fftw_real tmp371;
     fftw_real tmp537;
     fftw_real tmp637;
     fftw_real tmp755;
     fftw_real tmp200;
     fftw_real tmp538;
     fftw_real tmp374;
     fftw_real tmp472;
     fftw_real tmp109;
     fftw_real tmp837;
     fftw_real tmp693;
     fftw_real tmp773;
     fftw_real tmp844;
     fftw_real tmp892;
     fftw_real tmp710;
     fftw_real tmp776;
     fftw_real tmp329;
     fftw_real tmp429;
     fftw_real tmp519;
     fftw_real tmp593;
     fftw_real tmp362;
     fftw_real tmp432;
     fftw_real tmp530;
     fftw_real tmp596;
     fftw_real tmp30;
     fftw_real tmp850;
     fftw_real tmp640;
     fftw_real tmp721;
     fftw_real tmp157;
     fftw_real tmp816;
     fftw_real tmp643;
     fftw_real tmp720;
     fftw_real tmp208;
     fftw_real tmp377;
     fftw_real tmp476;
     fftw_real tmp541;
     fftw_real tmp215;
     fftw_real tmp376;
     fftw_real tmp479;
     fftw_real tmp540;
     fftw_real tmp124;
     fftw_real tmp845;
     fftw_real tmp365;
     fftw_real tmp430;
     fftw_real tmp352;
     fftw_real tmp433;
     fftw_real tmp840;
     fftw_real tmp893;
     fftw_real tmp526;
     fftw_real tmp597;
     fftw_real tmp533;
     fftw_real tmp594;
     fftw_real tmp704;
     fftw_real tmp777;
     fftw_real tmp713;
     fftw_real tmp774;
     fftw_real tmp46;
     fftw_real tmp819;
     fftw_real tmp648;
     fftw_real tmp758;
     fftw_real tmp173;
     fftw_real tmp818;
     fftw_real tmp651;
     fftw_real tmp759;
     fftw_real tmp228;
     fftw_real tmp414;
     fftw_real tmp484;
     fftw_real tmp578;
     fftw_real tmp235;
     fftw_real tmp415;
     fftw_real tmp487;
     fftw_real tmp579;
     fftw_real tmp78;
     fftw_real tmp831;
     fftw_real tmp666;
     fftw_real tmp769;
     fftw_real tmp828;
     fftw_real tmp887;
     fftw_real tmp683;
     fftw_real tmp766;
     fftw_real tmp274;
     fftw_real tmp425;
     fftw_real tmp500;
     fftw_real tmp589;
     fftw_real tmp307;
     fftw_real tmp422;
     fftw_real tmp511;
     fftw_real tmp586;
     fftw_real tmp61;
     fftw_real tmp821;
     fftw_real tmp655;
     fftw_real tmp761;
     fftw_real tmp188;
     fftw_real tmp822;
     fftw_real tmp658;
     fftw_real tmp762;
     fftw_real tmp247;
     fftw_real tmp417;
     fftw_real tmp491;
     fftw_real tmp581;
     fftw_real tmp254;
     fftw_real tmp418;
     fftw_real tmp494;
     fftw_real tmp582;
     fftw_real tmp93;
     fftw_real tmp829;
     fftw_real tmp310;
     fftw_real tmp426;
     fftw_real tmp297;
     fftw_real tmp423;
     fftw_real tmp834;
     fftw_real tmp888;
     fftw_real tmp507;
     fftw_real tmp587;
     fftw_real tmp514;
     fftw_real tmp590;
     fftw_real tmp677;
     fftw_real tmp767;
     fftw_real tmp686;
     fftw_real tmp770;
     ASSERT_ALIGNED_DOUBLE;
     {
	  fftw_real tmp3;
	  fftw_real tmp191;
	  fftw_real tmp130;
	  fftw_real tmp370;
	  fftw_real tmp6;
	  fftw_real tmp369;
	  fftw_real tmp133;
	  fftw_real tmp192;
	  fftw_real tmp10;
	  fftw_real tmp195;
	  fftw_real tmp137;
	  fftw_real tmp194;
	  fftw_real tmp13;
	  fftw_real tmp197;
	  fftw_real tmp140;
	  fftw_real tmp198;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp1;
	       fftw_real tmp2;
	       fftw_real tmp128;
	       fftw_real tmp129;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp1 = c_re(input[0]);
	       tmp2 = c_re(input[32 * istride]);
	       tmp3 = tmp1 + tmp2;
	       tmp191 = tmp1 - tmp2;
	       tmp128 = c_im(input[0]);
	       tmp129 = c_im(input[32 * istride]);
	       tmp130 = tmp128 + tmp129;
	       tmp370 = tmp128 - tmp129;
	  }
	  {
	       fftw_real tmp4;
	       fftw_real tmp5;
	       fftw_real tmp131;
	       fftw_real tmp132;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp4 = c_re(input[16 * istride]);
	       tmp5 = c_re(input[48 * istride]);
	       tmp6 = tmp4 + tmp5;
	       tmp369 = tmp4 - tmp5;
	       tmp131 = c_im(input[16 * istride]);
	       tmp132 = c_im(input[48 * istride]);
	       tmp133 = tmp131 + tmp132;
	       tmp192 = tmp131 - tmp132;
	  }
	  {
	       fftw_real tmp8;
	       fftw_real tmp9;
	       fftw_real tmp135;
	       fftw_real tmp136;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp8 = c_re(input[8 * istride]);
	       tmp9 = c_re(input[40 * istride]);
	       tmp10 = tmp8 + tmp9;
	       tmp195 = tmp8 - tmp9;
	       tmp135 = c_im(input[8 * istride]);
	       tmp136 = c_im(input[40 * istride]);
	       tmp137 = tmp135 + tmp136;
	       tmp194 = tmp135 - tmp136;
	  }
	  {
	       fftw_real tmp11;
	       fftw_real tmp12;
	       fftw_real tmp138;
	       fftw_real tmp139;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp11 = c_re(input[56 * istride]);
	       tmp12 = c_re(input[24 * istride]);
	       tmp13 = tmp11 + tmp12;
	       tmp197 = tmp11 - tmp12;
	       tmp138 = c_im(input[56 * istride]);
	       tmp139 = c_im(input[24 * istride]);
	       tmp140 = tmp138 + tmp139;
	       tmp198 = tmp138 - tmp139;
	  }
	  {
	       fftw_real tmp7;
	       fftw_real tmp14;
	       fftw_real tmp635;
	       fftw_real tmp636;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp193 = tmp191 - tmp192;
	       tmp471 = tmp191 + tmp192;
	       tmp7 = tmp3 + tmp6;
	       tmp14 = tmp10 + tmp13;
	       tmp15 = tmp7 + tmp14;
	       tmp815 = tmp7 - tmp14;
	       {
		    fftw_real tmp717;
		    fftw_real tmp718;
		    fftw_real tmp134;
		    fftw_real tmp141;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp717 = tmp130 - tmp133;
		    tmp718 = tmp13 - tmp10;
		    tmp719 = tmp717 - tmp718;
		    tmp781 = tmp718 + tmp717;
		    tmp134 = tmp130 + tmp133;
		    tmp141 = tmp137 + tmp140;
		    tmp142 = tmp134 + tmp141;
		    tmp849 = tmp134 - tmp141;
	       }
	       tmp371 = tmp369 + tmp370;
	       tmp537 = tmp370 - tmp369;
	       tmp635 = tmp3 - tmp6;
	       tmp636 = tmp137 - tmp140;
	       tmp637 = tmp635 - tmp636;
	       tmp755 = tmp635 + tmp636;
	       {
		    fftw_real tmp196;
		    fftw_real tmp199;
		    fftw_real tmp372;
		    fftw_real tmp373;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp196 = tmp194 - tmp195;
		    tmp199 = tmp197 + tmp198;
		    tmp200 = K707106781 * (tmp196 - tmp199);
		    tmp538 = K707106781 * (tmp196 + tmp199);
		    tmp372 = tmp197 - tmp198;
		    tmp373 = tmp195 + tmp194;
		    tmp374 = K707106781 * (tmp372 - tmp373);
		    tmp472 = K707106781 * (tmp373 + tmp372);
	       }
	  }
     }
     {
	  fftw_real tmp97;
	  fftw_real tmp313;
	  fftw_real tmp357;
	  fftw_real tmp706;
	  fftw_real tmp100;
	  fftw_real tmp354;
	  fftw_real tmp316;
	  fftw_real tmp707;
	  fftw_real tmp107;
	  fftw_real tmp691;
	  fftw_real tmp327;
	  fftw_real tmp359;
	  fftw_real tmp104;
	  fftw_real tmp690;
	  fftw_real tmp322;
	  fftw_real tmp360;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp95;
	       fftw_real tmp96;
	       fftw_real tmp314;
	       fftw_real tmp315;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp95 = c_re(input[63 * istride]);
	       tmp96 = c_re(input[31 * istride]);
	       tmp97 = tmp95 + tmp96;
	       tmp313 = tmp95 - tmp96;
	       {
		    fftw_real tmp355;
		    fftw_real tmp356;
		    fftw_real tmp98;
		    fftw_real tmp99;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp355 = c_im(input[63 * istride]);
		    tmp356 = c_im(input[31 * istride]);
		    tmp357 = tmp355 - tmp356;
		    tmp706 = tmp355 + tmp356;
		    tmp98 = c_re(input[15 * istride]);
		    tmp99 = c_re(input[47 * istride]);
		    tmp100 = tmp98 + tmp99;
		    tmp354 = tmp98 - tmp99;
	       }
	       tmp314 = c_im(input[15 * istride]);
	       tmp315 = c_im(input[47 * istride]);
	       tmp316 = tmp314 - tmp315;
	       tmp707 = tmp314 + tmp315;
	       {
		    fftw_real tmp105;
		    fftw_real tmp106;
		    fftw_real tmp323;
		    fftw_real tmp324;
		    fftw_real tmp325;
		    fftw_real tmp326;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp105 = c_re(input[55 * istride]);
		    tmp106 = c_re(input[23 * istride]);
		    tmp323 = tmp105 - tmp106;
		    tmp324 = c_im(input[55 * istride]);
		    tmp325 = c_im(input[23 * istride]);
		    tmp326 = tmp324 - tmp325;
		    tmp107 = tmp105 + tmp106;
		    tmp691 = tmp324 + tmp325;
		    tmp327 = tmp323 + tmp326;
		    tmp359 = tmp323 - tmp326;
	       }
	       {
		    fftw_real tmp102;
		    fftw_real tmp103;
		    fftw_real tmp321;
		    fftw_real tmp318;
		    fftw_real tmp319;
		    fftw_real tmp320;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp102 = c_re(input[7 * istride]);
		    tmp103 = c_re(input[39 * istride]);
		    tmp321 = tmp102 - tmp103;
		    tmp318 = c_im(input[7 * istride]);
		    tmp319 = c_im(input[39 * istride]);
		    tmp320 = tmp318 - tmp319;
		    tmp104 = tmp102 + tmp103;
		    tmp690 = tmp318 + tmp319;
		    tmp322 = tmp320 - tmp321;
		    tmp360 = tmp321 + tmp320;
	       }
	  }
	  {
	       fftw_real tmp101;
	       fftw_real tmp108;
	       fftw_real tmp689;
	       fftw_real tmp692;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp101 = tmp97 + tmp100;
	       tmp108 = tmp104 + tmp107;
	       tmp109 = tmp101 + tmp108;
	       tmp837 = tmp101 - tmp108;
	       tmp689 = tmp97 - tmp100;
	       tmp692 = tmp690 - tmp691;
	       tmp693 = tmp689 - tmp692;
	       tmp773 = tmp689 + tmp692;
	  }
	  {
	       fftw_real tmp842;
	       fftw_real tmp843;
	       fftw_real tmp708;
	       fftw_real tmp709;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp842 = tmp706 + tmp707;
	       tmp843 = tmp690 + tmp691;
	       tmp844 = tmp842 - tmp843;
	       tmp892 = tmp842 + tmp843;
	       tmp708 = tmp706 - tmp707;
	       tmp709 = tmp107 - tmp104;
	       tmp710 = tmp708 - tmp709;
	       tmp776 = tmp709 + tmp708;
	  }
	  {
	       fftw_real tmp317;
	       fftw_real tmp328;
	       fftw_real tmp517;
	       fftw_real tmp518;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp317 = tmp313 - tmp316;
	       tmp328 = K707106781 * (tmp322 - tmp327);
	       tmp329 = tmp317 - tmp328;
	       tmp429 = tmp317 + tmp328;
	       tmp517 = tmp313 + tmp316;
	       tmp518 = K707106781 * (tmp360 + tmp359);
	       tmp519 = tmp517 - tmp518;
	       tmp593 = tmp517 + tmp518;
	  }
	  {
	       fftw_real tmp358;
	       fftw_real tmp361;
	       fftw_real tmp528;
	       fftw_real tmp529;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp358 = tmp354 + tmp357;
	       tmp361 = K707106781 * (tmp359 - tmp360);
	       tmp362 = tmp358 - tmp361;
	       tmp432 = tmp358 + tmp361;
	       tmp528 = tmp357 - tmp354;
	       tmp529 = K707106781 * (tmp322 + tmp327);
	       tmp530 = tmp528 - tmp529;
	       tmp596 = tmp528 + tmp529;
	  }
     }
     {
	  fftw_real tmp18;
	  fftw_real tmp205;
	  fftw_real tmp145;
	  fftw_real tmp203;
	  fftw_real tmp21;
	  fftw_real tmp202;
	  fftw_real tmp148;
	  fftw_real tmp206;
	  fftw_real tmp25;
	  fftw_real tmp212;
	  fftw_real tmp152;
	  fftw_real tmp210;
	  fftw_real tmp28;
	  fftw_real tmp209;
	  fftw_real tmp155;
	  fftw_real tmp213;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp16;
	       fftw_real tmp17;
	       fftw_real tmp143;
	       fftw_real tmp144;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp16 = c_re(input[4 * istride]);
	       tmp17 = c_re(input[36 * istride]);
	       tmp18 = tmp16 + tmp17;
	       tmp205 = tmp16 - tmp17;
	       tmp143 = c_im(input[4 * istride]);
	       tmp144 = c_im(input[36 * istride]);
	       tmp145 = tmp143 + tmp144;
	       tmp203 = tmp143 - tmp144;
	  }
	  {
	       fftw_real tmp19;
	       fftw_real tmp20;
	       fftw_real tmp146;
	       fftw_real tmp147;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp19 = c_re(input[20 * istride]);
	       tmp20 = c_re(input[52 * istride]);
	       tmp21 = tmp19 + tmp20;
	       tmp202 = tmp19 - tmp20;
	       tmp146 = c_im(input[20 * istride]);
	       tmp147 = c_im(input[52 * istride]);
	       tmp148 = tmp146 + tmp147;
	       tmp206 = tmp146 - tmp147;
	  }
	  {
	       fftw_real tmp23;
	       fftw_real tmp24;
	       fftw_real tmp150;
	       fftw_real tmp151;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp23 = c_re(input[60 * istride]);
	       tmp24 = c_re(input[28 * istride]);
	       tmp25 = tmp23 + tmp24;
	       tmp212 = tmp23 - tmp24;
	       tmp150 = c_im(input[60 * istride]);
	       tmp151 = c_im(input[28 * istride]);
	       tmp152 = tmp150 + tmp151;
	       tmp210 = tmp150 - tmp151;
	  }
	  {
	       fftw_real tmp26;
	       fftw_real tmp27;
	       fftw_real tmp153;
	       fftw_real tmp154;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp26 = c_re(input[12 * istride]);
	       tmp27 = c_re(input[44 * istride]);
	       tmp28 = tmp26 + tmp27;
	       tmp209 = tmp26 - tmp27;
	       tmp153 = c_im(input[12 * istride]);
	       tmp154 = c_im(input[44 * istride]);
	       tmp155 = tmp153 + tmp154;
	       tmp213 = tmp153 - tmp154;
	  }
	  {
	       fftw_real tmp22;
	       fftw_real tmp29;
	       fftw_real tmp638;
	       fftw_real tmp639;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp22 = tmp18 + tmp21;
	       tmp29 = tmp25 + tmp28;
	       tmp30 = tmp22 + tmp29;
	       tmp850 = tmp29 - tmp22;
	       tmp638 = tmp145 - tmp148;
	       tmp639 = tmp18 - tmp21;
	       tmp640 = tmp638 - tmp639;
	       tmp721 = tmp639 + tmp638;
	  }
	  {
	       fftw_real tmp149;
	       fftw_real tmp156;
	       fftw_real tmp641;
	       fftw_real tmp642;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp149 = tmp145 + tmp148;
	       tmp156 = tmp152 + tmp155;
	       tmp157 = tmp149 + tmp156;
	       tmp816 = tmp149 - tmp156;
	       tmp641 = tmp25 - tmp28;
	       tmp642 = tmp152 - tmp155;
	       tmp643 = tmp641 + tmp642;
	       tmp720 = tmp641 - tmp642;
	  }
	  {
	       fftw_real tmp204;
	       fftw_real tmp207;
	       fftw_real tmp474;
	       fftw_real tmp475;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp204 = tmp202 + tmp203;
	       tmp207 = tmp205 - tmp206;
	       tmp208 = (K382683432 * tmp204) - (K923879532 * tmp207);
	       tmp377 = (K923879532 * tmp204) + (K382683432 * tmp207);
	       tmp474 = tmp203 - tmp202;
	       tmp475 = tmp205 + tmp206;
	       tmp476 = (K923879532 * tmp474) - (K382683432 * tmp475);
	       tmp541 = (K382683432 * tmp474) + (K923879532 * tmp475);
	  }
	  {
	       fftw_real tmp211;
	       fftw_real tmp214;
	       fftw_real tmp477;
	       fftw_real tmp478;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp211 = tmp209 + tmp210;
	       tmp214 = tmp212 - tmp213;
	       tmp215 = (K382683432 * tmp211) + (K923879532 * tmp214);
	       tmp376 = (K382683432 * tmp214) - (K923879532 * tmp211);
	       tmp477 = tmp210 - tmp209;
	       tmp478 = tmp212 + tmp213;
	       tmp479 = (K923879532 * tmp477) + (K382683432 * tmp478);
	       tmp540 = (K923879532 * tmp478) - (K382683432 * tmp477);
	  }
     }
     {
	  fftw_real tmp112;
	  fftw_real tmp694;
	  fftw_real tmp115;
	  fftw_real tmp695;
	  fftw_real tmp334;
	  fftw_real tmp520;
	  fftw_real tmp339;
	  fftw_real tmp521;
	  fftw_real tmp697;
	  fftw_real tmp696;
	  fftw_real tmp119;
	  fftw_real tmp700;
	  fftw_real tmp122;
	  fftw_real tmp701;
	  fftw_real tmp345;
	  fftw_real tmp523;
	  fftw_real tmp350;
	  fftw_real tmp524;
	  fftw_real tmp702;
	  fftw_real tmp699;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp335;
	       fftw_real tmp333;
	       fftw_real tmp330;
	       fftw_real tmp338;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp110;
		    fftw_real tmp111;
		    fftw_real tmp331;
		    fftw_real tmp332;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp110 = c_re(input[3 * istride]);
		    tmp111 = c_re(input[35 * istride]);
		    tmp112 = tmp110 + tmp111;
		    tmp335 = tmp110 - tmp111;
		    tmp331 = c_im(input[3 * istride]);
		    tmp332 = c_im(input[35 * istride]);
		    tmp333 = tmp331 - tmp332;
		    tmp694 = tmp331 + tmp332;
	       }
	       {
		    fftw_real tmp113;
		    fftw_real tmp114;
		    fftw_real tmp336;
		    fftw_real tmp337;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp113 = c_re(input[19 * istride]);
		    tmp114 = c_re(input[51 * istride]);
		    tmp115 = tmp113 + tmp114;
		    tmp330 = tmp113 - tmp114;
		    tmp336 = c_im(input[19 * istride]);
		    tmp337 = c_im(input[51 * istride]);
		    tmp338 = tmp336 - tmp337;
		    tmp695 = tmp336 + tmp337;
	       }
	       tmp334 = tmp330 + tmp333;
	       tmp520 = tmp333 - tmp330;
	       tmp339 = tmp335 - tmp338;
	       tmp521 = tmp335 + tmp338;
	       tmp697 = tmp112 - tmp115;
	       tmp696 = tmp694 - tmp695;
	  }
	  {
	       fftw_real tmp346;
	       fftw_real tmp344;
	       fftw_real tmp341;
	       fftw_real tmp349;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp117;
		    fftw_real tmp118;
		    fftw_real tmp342;
		    fftw_real tmp343;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp117 = c_re(input[59 * istride]);
		    tmp118 = c_re(input[27 * istride]);
		    tmp119 = tmp117 + tmp118;
		    tmp346 = tmp117 - tmp118;
		    tmp342 = c_im(input[59 * istride]);
		    tmp343 = c_im(input[27 * istride]);
		    tmp344 = tmp342 - tmp343;
		    tmp700 = tmp342 + tmp343;
	       }
	       {
		    fftw_real tmp120;
		    fftw_real tmp121;
		    fftw_real tmp347;
		    fftw_real tmp348;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp120 = c_re(input[11 * istride]);
		    tmp121 = c_re(input[43 * istride]);
		    tmp122 = tmp120 + tmp121;
		    tmp341 = tmp120 - tmp121;
		    tmp347 = c_im(input[11 * istride]);
		    tmp348 = c_im(input[43 * istride]);
		    tmp349 = tmp347 - tmp348;
		    tmp701 = tmp347 + tmp348;
	       }
	       tmp345 = tmp341 + tmp344;
	       tmp523 = tmp344 - tmp341;
	       tmp350 = tmp346 - tmp349;
	       tmp524 = tmp346 + tmp349;
	       tmp702 = tmp700 - tmp701;
	       tmp699 = tmp119 - tmp122;
	  }
	  {
	       fftw_real tmp116;
	       fftw_real tmp123;
	       fftw_real tmp363;
	       fftw_real tmp364;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp116 = tmp112 + tmp115;
	       tmp123 = tmp119 + tmp122;
	       tmp124 = tmp116 + tmp123;
	       tmp845 = tmp123 - tmp116;
	       tmp363 = (K382683432 * tmp350) - (K923879532 * tmp345);
	       tmp364 = (K923879532 * tmp334) + (K382683432 * tmp339);
	       tmp365 = tmp363 - tmp364;
	       tmp430 = tmp364 + tmp363;
	  }
	  {
	       fftw_real tmp340;
	       fftw_real tmp351;
	       fftw_real tmp838;
	       fftw_real tmp839;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp340 = (K382683432 * tmp334) - (K923879532 * tmp339);
	       tmp351 = (K382683432 * tmp345) + (K923879532 * tmp350);
	       tmp352 = tmp340 - tmp351;
	       tmp433 = tmp340 + tmp351;
	       tmp838 = tmp694 + tmp695;
	       tmp839 = tmp700 + tmp701;
	       tmp840 = tmp838 - tmp839;
	       tmp893 = tmp838 + tmp839;
	  }
	  {
	       fftw_real tmp522;
	       fftw_real tmp525;
	       fftw_real tmp531;
	       fftw_real tmp532;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp522 = (K923879532 * tmp520) - (K382683432 * tmp521);
	       tmp525 = (K923879532 * tmp523) + (K382683432 * tmp524);
	       tmp526 = tmp522 - tmp525;
	       tmp597 = tmp522 + tmp525;
	       tmp531 = (K923879532 * tmp524) - (K382683432 * tmp523);
	       tmp532 = (K382683432 * tmp520) + (K923879532 * tmp521);
	       tmp533 = tmp531 - tmp532;
	       tmp594 = tmp532 + tmp531;
	  }
	  {
	       fftw_real tmp698;
	       fftw_real tmp703;
	       fftw_real tmp711;
	       fftw_real tmp712;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp698 = tmp696 - tmp697;
	       tmp703 = tmp699 + tmp702;
	       tmp704 = K707106781 * (tmp698 - tmp703);
	       tmp777 = K707106781 * (tmp698 + tmp703);
	       tmp711 = tmp699 - tmp702;
	       tmp712 = tmp697 + tmp696;
	       tmp713 = K707106781 * (tmp711 - tmp712);
	       tmp774 = K707106781 * (tmp712 + tmp711);
	  }
     }
     {
	  fftw_real tmp34;
	  fftw_real tmp229;
	  fftw_real tmp161;
	  fftw_real tmp219;
	  fftw_real tmp37;
	  fftw_real tmp218;
	  fftw_real tmp164;
	  fftw_real tmp230;
	  fftw_real tmp44;
	  fftw_real tmp233;
	  fftw_real tmp223;
	  fftw_real tmp171;
	  fftw_real tmp41;
	  fftw_real tmp232;
	  fftw_real tmp226;
	  fftw_real tmp168;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp32;
	       fftw_real tmp33;
	       fftw_real tmp162;
	       fftw_real tmp163;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp32 = c_re(input[2 * istride]);
	       tmp33 = c_re(input[34 * istride]);
	       tmp34 = tmp32 + tmp33;
	       tmp229 = tmp32 - tmp33;
	       {
		    fftw_real tmp159;
		    fftw_real tmp160;
		    fftw_real tmp35;
		    fftw_real tmp36;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp159 = c_im(input[2 * istride]);
		    tmp160 = c_im(input[34 * istride]);
		    tmp161 = tmp159 + tmp160;
		    tmp219 = tmp159 - tmp160;
		    tmp35 = c_re(input[18 * istride]);
		    tmp36 = c_re(input[50 * istride]);
		    tmp37 = tmp35 + tmp36;
		    tmp218 = tmp35 - tmp36;
	       }
	       tmp162 = c_im(input[18 * istride]);
	       tmp163 = c_im(input[50 * istride]);
	       tmp164 = tmp162 + tmp163;
	       tmp230 = tmp162 - tmp163;
	       {
		    fftw_real tmp42;
		    fftw_real tmp43;
		    fftw_real tmp221;
		    fftw_real tmp169;
		    fftw_real tmp170;
		    fftw_real tmp222;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp42 = c_re(input[58 * istride]);
		    tmp43 = c_re(input[26 * istride]);
		    tmp221 = tmp42 - tmp43;
		    tmp169 = c_im(input[58 * istride]);
		    tmp170 = c_im(input[26 * istride]);
		    tmp222 = tmp169 - tmp170;
		    tmp44 = tmp42 + tmp43;
		    tmp233 = tmp221 + tmp222;
		    tmp223 = tmp221 - tmp222;
		    tmp171 = tmp169 + tmp170;
	       }
	       {
		    fftw_real tmp39;
		    fftw_real tmp40;
		    fftw_real tmp224;
		    fftw_real tmp166;
		    fftw_real tmp167;
		    fftw_real tmp225;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp39 = c_re(input[10 * istride]);
		    tmp40 = c_re(input[42 * istride]);
		    tmp224 = tmp39 - tmp40;
		    tmp166 = c_im(input[10 * istride]);
		    tmp167 = c_im(input[42 * istride]);
		    tmp225 = tmp166 - tmp167;
		    tmp41 = tmp39 + tmp40;
		    tmp232 = tmp225 - tmp224;
		    tmp226 = tmp224 + tmp225;
		    tmp168 = tmp166 + tmp167;
	       }
	  }
	  {
	       fftw_real tmp38;
	       fftw_real tmp45;
	       fftw_real tmp646;
	       fftw_real tmp647;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp38 = tmp34 + tmp37;
	       tmp45 = tmp41 + tmp44;
	       tmp46 = tmp38 + tmp45;
	       tmp819 = tmp38 - tmp45;
	       tmp646 = tmp161 - tmp164;
	       tmp647 = tmp44 - tmp41;
	       tmp648 = tmp646 - tmp647;
	       tmp758 = tmp647 + tmp646;
	  }
	  {
	       fftw_real tmp165;
	       fftw_real tmp172;
	       fftw_real tmp649;
	       fftw_real tmp650;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp165 = tmp161 + tmp164;
	       tmp172 = tmp168 + tmp171;
	       tmp173 = tmp165 + tmp172;
	       tmp818 = tmp165 - tmp172;
	       tmp649 = tmp34 - tmp37;
	       tmp650 = tmp168 - tmp171;
	       tmp651 = tmp649 - tmp650;
	       tmp759 = tmp649 + tmp650;
	  }
	  {
	       fftw_real tmp220;
	       fftw_real tmp227;
	       fftw_real tmp482;
	       fftw_real tmp483;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp220 = tmp218 + tmp219;
	       tmp227 = K707106781 * (tmp223 - tmp226);
	       tmp228 = tmp220 - tmp227;
	       tmp414 = tmp220 + tmp227;
	       tmp482 = tmp219 - tmp218;
	       tmp483 = K707106781 * (tmp232 + tmp233);
	       tmp484 = tmp482 - tmp483;
	       tmp578 = tmp482 + tmp483;
	  }
	  {
	       fftw_real tmp231;
	       fftw_real tmp234;
	       fftw_real tmp485;
	       fftw_real tmp486;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp231 = tmp229 - tmp230;
	       tmp234 = K707106781 * (tmp232 - tmp233);
	       tmp235 = tmp231 - tmp234;
	       tmp415 = tmp231 + tmp234;
	       tmp485 = tmp229 + tmp230;
	       tmp486 = K707106781 * (tmp226 + tmp223);
	       tmp487 = tmp485 - tmp486;
	       tmp579 = tmp485 + tmp486;
	  }
     }
     {
	  fftw_real tmp66;
	  fftw_real tmp299;
	  fftw_real tmp261;
	  fftw_real tmp662;
	  fftw_real tmp69;
	  fftw_real tmp258;
	  fftw_real tmp302;
	  fftw_real tmp663;
	  fftw_real tmp76;
	  fftw_real tmp681;
	  fftw_real tmp267;
	  fftw_real tmp305;
	  fftw_real tmp73;
	  fftw_real tmp680;
	  fftw_real tmp272;
	  fftw_real tmp304;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp64;
	       fftw_real tmp65;
	       fftw_real tmp300;
	       fftw_real tmp301;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp64 = c_re(input[istride]);
	       tmp65 = c_re(input[33 * istride]);
	       tmp66 = tmp64 + tmp65;
	       tmp299 = tmp64 - tmp65;
	       {
		    fftw_real tmp259;
		    fftw_real tmp260;
		    fftw_real tmp67;
		    fftw_real tmp68;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp259 = c_im(input[istride]);
		    tmp260 = c_im(input[33 * istride]);
		    tmp261 = tmp259 - tmp260;
		    tmp662 = tmp259 + tmp260;
		    tmp67 = c_re(input[17 * istride]);
		    tmp68 = c_re(input[49 * istride]);
		    tmp69 = tmp67 + tmp68;
		    tmp258 = tmp67 - tmp68;
	       }
	       tmp300 = c_im(input[17 * istride]);
	       tmp301 = c_im(input[49 * istride]);
	       tmp302 = tmp300 - tmp301;
	       tmp663 = tmp300 + tmp301;
	       {
		    fftw_real tmp74;
		    fftw_real tmp75;
		    fftw_real tmp263;
		    fftw_real tmp264;
		    fftw_real tmp265;
		    fftw_real tmp266;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp74 = c_re(input[57 * istride]);
		    tmp75 = c_re(input[25 * istride]);
		    tmp263 = tmp74 - tmp75;
		    tmp264 = c_im(input[57 * istride]);
		    tmp265 = c_im(input[25 * istride]);
		    tmp266 = tmp264 - tmp265;
		    tmp76 = tmp74 + tmp75;
		    tmp681 = tmp264 + tmp265;
		    tmp267 = tmp263 - tmp266;
		    tmp305 = tmp263 + tmp266;
	       }
	       {
		    fftw_real tmp71;
		    fftw_real tmp72;
		    fftw_real tmp268;
		    fftw_real tmp269;
		    fftw_real tmp270;
		    fftw_real tmp271;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp71 = c_re(input[9 * istride]);
		    tmp72 = c_re(input[41 * istride]);
		    tmp268 = tmp71 - tmp72;
		    tmp269 = c_im(input[9 * istride]);
		    tmp270 = c_im(input[41 * istride]);
		    tmp271 = tmp269 - tmp270;
		    tmp73 = tmp71 + tmp72;
		    tmp680 = tmp269 + tmp270;
		    tmp272 = tmp268 + tmp271;
		    tmp304 = tmp271 - tmp268;
	       }
	  }
	  {
	       fftw_real tmp70;
	       fftw_real tmp77;
	       fftw_real tmp664;
	       fftw_real tmp665;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp70 = tmp66 + tmp69;
	       tmp77 = tmp73 + tmp76;
	       tmp78 = tmp70 + tmp77;
	       tmp831 = tmp70 - tmp77;
	       tmp664 = tmp662 - tmp663;
	       tmp665 = tmp76 - tmp73;
	       tmp666 = tmp664 - tmp665;
	       tmp769 = tmp665 + tmp664;
	  }
	  {
	       fftw_real tmp826;
	       fftw_real tmp827;
	       fftw_real tmp679;
	       fftw_real tmp682;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp826 = tmp662 + tmp663;
	       tmp827 = tmp680 + tmp681;
	       tmp828 = tmp826 - tmp827;
	       tmp887 = tmp826 + tmp827;
	       tmp679 = tmp66 - tmp69;
	       tmp682 = tmp680 - tmp681;
	       tmp683 = tmp679 - tmp682;
	       tmp766 = tmp679 + tmp682;
	  }
	  {
	       fftw_real tmp262;
	       fftw_real tmp273;
	       fftw_real tmp498;
	       fftw_real tmp499;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp262 = tmp258 + tmp261;
	       tmp273 = K707106781 * (tmp267 - tmp272);
	       tmp274 = tmp262 - tmp273;
	       tmp425 = tmp262 + tmp273;
	       tmp498 = tmp261 - tmp258;
	       tmp499 = K707106781 * (tmp304 + tmp305);
	       tmp500 = tmp498 - tmp499;
	       tmp589 = tmp498 + tmp499;
	  }
	  {
	       fftw_real tmp303;
	       fftw_real tmp306;
	       fftw_real tmp509;
	       fftw_real tmp510;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp303 = tmp299 - tmp302;
	       tmp306 = K707106781 * (tmp304 - tmp305);
	       tmp307 = tmp303 - tmp306;
	       tmp422 = tmp303 + tmp306;
	       tmp509 = tmp299 + tmp302;
	       tmp510 = K707106781 * (tmp272 + tmp267);
	       tmp511 = tmp509 - tmp510;
	       tmp586 = tmp509 + tmp510;
	  }
     }
     {
	  fftw_real tmp49;
	  fftw_real tmp248;
	  fftw_real tmp176;
	  fftw_real tmp238;
	  fftw_real tmp52;
	  fftw_real tmp237;
	  fftw_real tmp179;
	  fftw_real tmp249;
	  fftw_real tmp59;
	  fftw_real tmp252;
	  fftw_real tmp242;
	  fftw_real tmp186;
	  fftw_real tmp56;
	  fftw_real tmp251;
	  fftw_real tmp245;
	  fftw_real tmp183;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp47;
	       fftw_real tmp48;
	       fftw_real tmp177;
	       fftw_real tmp178;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp47 = c_re(input[62 * istride]);
	       tmp48 = c_re(input[30 * istride]);
	       tmp49 = tmp47 + tmp48;
	       tmp248 = tmp47 - tmp48;
	       {
		    fftw_real tmp174;
		    fftw_real tmp175;
		    fftw_real tmp50;
		    fftw_real tmp51;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp174 = c_im(input[62 * istride]);
		    tmp175 = c_im(input[30 * istride]);
		    tmp176 = tmp174 + tmp175;
		    tmp238 = tmp174 - tmp175;
		    tmp50 = c_re(input[14 * istride]);
		    tmp51 = c_re(input[46 * istride]);
		    tmp52 = tmp50 + tmp51;
		    tmp237 = tmp50 - tmp51;
	       }
	       tmp177 = c_im(input[14 * istride]);
	       tmp178 = c_im(input[46 * istride]);
	       tmp179 = tmp177 + tmp178;
	       tmp249 = tmp177 - tmp178;
	       {
		    fftw_real tmp57;
		    fftw_real tmp58;
		    fftw_real tmp240;
		    fftw_real tmp184;
		    fftw_real tmp185;
		    fftw_real tmp241;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp57 = c_re(input[54 * istride]);
		    tmp58 = c_re(input[22 * istride]);
		    tmp240 = tmp57 - tmp58;
		    tmp184 = c_im(input[54 * istride]);
		    tmp185 = c_im(input[22 * istride]);
		    tmp241 = tmp184 - tmp185;
		    tmp59 = tmp57 + tmp58;
		    tmp252 = tmp240 + tmp241;
		    tmp242 = tmp240 - tmp241;
		    tmp186 = tmp184 + tmp185;
	       }
	       {
		    fftw_real tmp54;
		    fftw_real tmp55;
		    fftw_real tmp243;
		    fftw_real tmp181;
		    fftw_real tmp182;
		    fftw_real tmp244;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp54 = c_re(input[6 * istride]);
		    tmp55 = c_re(input[38 * istride]);
		    tmp243 = tmp54 - tmp55;
		    tmp181 = c_im(input[6 * istride]);
		    tmp182 = c_im(input[38 * istride]);
		    tmp244 = tmp181 - tmp182;
		    tmp56 = tmp54 + tmp55;
		    tmp251 = tmp244 - tmp243;
		    tmp245 = tmp243 + tmp244;
		    tmp183 = tmp181 + tmp182;
	       }
	  }
	  {
	       fftw_real tmp53;
	       fftw_real tmp60;
	       fftw_real tmp653;
	       fftw_real tmp654;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp53 = tmp49 + tmp52;
	       tmp60 = tmp56 + tmp59;
	       tmp61 = tmp53 + tmp60;
	       tmp821 = tmp53 - tmp60;
	       tmp653 = tmp176 - tmp179;
	       tmp654 = tmp59 - tmp56;
	       tmp655 = tmp653 - tmp654;
	       tmp761 = tmp654 + tmp653;
	  }
	  {
	       fftw_real tmp180;
	       fftw_real tmp187;
	       fftw_real tmp656;
	       fftw_real tmp657;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp180 = tmp176 + tmp179;
	       tmp187 = tmp183 + tmp186;
	       tmp188 = tmp180 + tmp187;
	       tmp822 = tmp180 - tmp187;
	       tmp656 = tmp49 - tmp52;
	       tmp657 = tmp183 - tmp186;
	       tmp658 = tmp656 - tmp657;
	       tmp762 = tmp656 + tmp657;
	  }
	  {
	       fftw_real tmp239;
	       fftw_real tmp246;
	       fftw_real tmp489;
	       fftw_real tmp490;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp239 = tmp237 + tmp238;
	       tmp246 = K707106781 * (tmp242 - tmp245);
	       tmp247 = tmp239 - tmp246;
	       tmp417 = tmp239 + tmp246;
	       tmp489 = tmp248 + tmp249;
	       tmp490 = K707106781 * (tmp245 + tmp242);
	       tmp491 = tmp489 - tmp490;
	       tmp581 = tmp489 + tmp490;
	  }
	  {
	       fftw_real tmp250;
	       fftw_real tmp253;
	       fftw_real tmp492;
	       fftw_real tmp493;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp250 = tmp248 - tmp249;
	       tmp253 = K707106781 * (tmp251 - tmp252);
	       tmp254 = tmp250 - tmp253;
	       tmp418 = tmp250 + tmp253;
	       tmp492 = tmp238 - tmp237;
	       tmp493 = K707106781 * (tmp251 + tmp252);
	       tmp494 = tmp492 - tmp493;
	       tmp582 = tmp492 + tmp493;
	  }
     }
     {
	  fftw_real tmp81;
	  fftw_real tmp673;
	  fftw_real tmp84;
	  fftw_real tmp674;
	  fftw_real tmp290;
	  fftw_real tmp504;
	  fftw_real tmp295;
	  fftw_real tmp505;
	  fftw_real tmp675;
	  fftw_real tmp672;
	  fftw_real tmp88;
	  fftw_real tmp668;
	  fftw_real tmp91;
	  fftw_real tmp669;
	  fftw_real tmp279;
	  fftw_real tmp501;
	  fftw_real tmp284;
	  fftw_real tmp502;
	  fftw_real tmp670;
	  fftw_real tmp667;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp291;
	       fftw_real tmp289;
	       fftw_real tmp286;
	       fftw_real tmp294;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp79;
		    fftw_real tmp80;
		    fftw_real tmp287;
		    fftw_real tmp288;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp79 = c_re(input[5 * istride]);
		    tmp80 = c_re(input[37 * istride]);
		    tmp81 = tmp79 + tmp80;
		    tmp291 = tmp79 - tmp80;
		    tmp287 = c_im(input[5 * istride]);
		    tmp288 = c_im(input[37 * istride]);
		    tmp289 = tmp287 - tmp288;
		    tmp673 = tmp287 + tmp288;
	       }
	       {
		    fftw_real tmp82;
		    fftw_real tmp83;
		    fftw_real tmp292;
		    fftw_real tmp293;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp82 = c_re(input[21 * istride]);
		    tmp83 = c_re(input[53 * istride]);
		    tmp84 = tmp82 + tmp83;
		    tmp286 = tmp82 - tmp83;
		    tmp292 = c_im(input[21 * istride]);
		    tmp293 = c_im(input[53 * istride]);
		    tmp294 = tmp292 - tmp293;
		    tmp674 = tmp292 + tmp293;
	       }
	       tmp290 = tmp286 + tmp289;
	       tmp504 = tmp289 - tmp286;
	       tmp295 = tmp291 - tmp294;
	       tmp505 = tmp291 + tmp294;
	       tmp675 = tmp673 - tmp674;
	       tmp672 = tmp81 - tmp84;
	  }
	  {
	       fftw_real tmp275;
	       fftw_real tmp283;
	       fftw_real tmp280;
	       fftw_real tmp278;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp86;
		    fftw_real tmp87;
		    fftw_real tmp281;
		    fftw_real tmp282;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp86 = c_re(input[61 * istride]);
		    tmp87 = c_re(input[29 * istride]);
		    tmp88 = tmp86 + tmp87;
		    tmp275 = tmp86 - tmp87;
		    tmp281 = c_im(input[61 * istride]);
		    tmp282 = c_im(input[29 * istride]);
		    tmp283 = tmp281 - tmp282;
		    tmp668 = tmp281 + tmp282;
	       }
	       {
		    fftw_real tmp89;
		    fftw_real tmp90;
		    fftw_real tmp276;
		    fftw_real tmp277;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp89 = c_re(input[13 * istride]);
		    tmp90 = c_re(input[45 * istride]);
		    tmp91 = tmp89 + tmp90;
		    tmp280 = tmp89 - tmp90;
		    tmp276 = c_im(input[13 * istride]);
		    tmp277 = c_im(input[45 * istride]);
		    tmp278 = tmp276 - tmp277;
		    tmp669 = tmp276 + tmp277;
	       }
	       tmp279 = tmp275 - tmp278;
	       tmp501 = tmp275 + tmp278;
	       tmp284 = tmp280 + tmp283;
	       tmp502 = tmp283 - tmp280;
	       tmp670 = tmp668 - tmp669;
	       tmp667 = tmp88 - tmp91;
	  }
	  {
	       fftw_real tmp85;
	       fftw_real tmp92;
	       fftw_real tmp308;
	       fftw_real tmp309;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp85 = tmp81 + tmp84;
	       tmp92 = tmp88 + tmp91;
	       tmp93 = tmp85 + tmp92;
	       tmp829 = tmp92 - tmp85;
	       tmp308 = (K382683432 * tmp290) - (K923879532 * tmp295);
	       tmp309 = (K382683432 * tmp284) + (K923879532 * tmp279);
	       tmp310 = tmp308 - tmp309;
	       tmp426 = tmp308 + tmp309;
	  }
	  {
	       fftw_real tmp285;
	       fftw_real tmp296;
	       fftw_real tmp832;
	       fftw_real tmp833;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp285 = (K382683432 * tmp279) - (K923879532 * tmp284);
	       tmp296 = (K923879532 * tmp290) + (K382683432 * tmp295);
	       tmp297 = tmp285 - tmp296;
	       tmp423 = tmp296 + tmp285;
	       tmp832 = tmp673 + tmp674;
	       tmp833 = tmp668 + tmp669;
	       tmp834 = tmp832 - tmp833;
	       tmp888 = tmp832 + tmp833;
	  }
	  {
	       fftw_real tmp503;
	       fftw_real tmp506;
	       fftw_real tmp512;
	       fftw_real tmp513;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp503 = (K923879532 * tmp501) - (K382683432 * tmp502);
	       tmp506 = (K382683432 * tmp504) + (K923879532 * tmp505);
	       tmp507 = tmp503 - tmp506;
	       tmp587 = tmp506 + tmp503;
	       tmp512 = (K923879532 * tmp504) - (K382683432 * tmp505);
	       tmp513 = (K923879532 * tmp502) + (K382683432 * tmp501);
	       tmp514 = tmp512 - tmp513;
	       tmp590 = tmp512 + tmp513;
	  }
	  {
	       fftw_real tmp671;
	       fftw_real tmp676;
	       fftw_real tmp684;
	       fftw_real tmp685;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp671 = tmp667 - tmp670;
	       tmp676 = tmp672 + tmp675;
	       tmp677 = K707106781 * (tmp671 - tmp676);
	       tmp767 = K707106781 * (tmp676 + tmp671);
	       tmp684 = tmp675 - tmp672;
	       tmp685 = tmp667 + tmp670;
	       tmp686 = K707106781 * (tmp684 - tmp685);
	       tmp770 = K707106781 * (tmp684 + tmp685);
	  }
     }
     {
	  fftw_real tmp63;
	  fftw_real tmp907;
	  fftw_real tmp910;
	  fftw_real tmp912;
	  fftw_real tmp126;
	  fftw_real tmp127;
	  fftw_real tmp190;
	  fftw_real tmp911;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp31;
	       fftw_real tmp62;
	       fftw_real tmp908;
	       fftw_real tmp909;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp31 = tmp15 + tmp30;
	       tmp62 = tmp46 + tmp61;
	       tmp63 = tmp31 + tmp62;
	       tmp907 = tmp31 - tmp62;
	       tmp908 = tmp887 + tmp888;
	       tmp909 = tmp892 + tmp893;
	       tmp910 = tmp908 - tmp909;
	       tmp912 = tmp908 + tmp909;
	  }
	  {
	       fftw_real tmp94;
	       fftw_real tmp125;
	       fftw_real tmp158;
	       fftw_real tmp189;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp94 = tmp78 + tmp93;
	       tmp125 = tmp109 + tmp124;
	       tmp126 = tmp94 + tmp125;
	       tmp127 = tmp125 - tmp94;
	       tmp158 = tmp142 + tmp157;
	       tmp189 = tmp173 + tmp188;
	       tmp190 = tmp158 - tmp189;
	       tmp911 = tmp158 + tmp189;
	  }
	  c_re(output[32 * ostride]) = tmp63 - tmp126;
	  c_re(output[0]) = tmp63 + tmp126;
	  c_im(output[16 * ostride]) = tmp127 + tmp190;
	  c_im(output[48 * ostride]) = tmp190 - tmp127;
	  c_re(output[48 * ostride]) = tmp907 - tmp910;
	  c_re(output[16 * ostride]) = tmp907 + tmp910;
	  c_im(output[32 * ostride]) = tmp911 - tmp912;
	  c_im(output[0]) = tmp911 + tmp912;
     }
     {
	  fftw_real tmp885;
	  fftw_real tmp901;
	  fftw_real tmp899;
	  fftw_real tmp905;
	  fftw_real tmp890;
	  fftw_real tmp902;
	  fftw_real tmp895;
	  fftw_real tmp903;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp883;
	       fftw_real tmp884;
	       fftw_real tmp897;
	       fftw_real tmp898;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp883 = tmp15 - tmp30;
	       tmp884 = tmp173 - tmp188;
	       tmp885 = tmp883 + tmp884;
	       tmp901 = tmp883 - tmp884;
	       tmp897 = tmp142 - tmp157;
	       tmp898 = tmp61 - tmp46;
	       tmp899 = tmp897 - tmp898;
	       tmp905 = tmp898 + tmp897;
	  }
	  {
	       fftw_real tmp886;
	       fftw_real tmp889;
	       fftw_real tmp891;
	       fftw_real tmp894;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp886 = tmp78 - tmp93;
	       tmp889 = tmp887 - tmp888;
	       tmp890 = tmp886 + tmp889;
	       tmp902 = tmp889 - tmp886;
	       tmp891 = tmp109 - tmp124;
	       tmp894 = tmp892 - tmp893;
	       tmp895 = tmp891 - tmp894;
	       tmp903 = tmp891 + tmp894;
	  }
	  {
	       fftw_real tmp896;
	       fftw_real tmp900;
	       fftw_real tmp904;
	       fftw_real tmp906;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp896 = K707106781 * (tmp890 + tmp895);
	       c_re(output[40 * ostride]) = tmp885 - tmp896;
	       c_re(output[8 * ostride]) = tmp885 + tmp896;
	       tmp900 = K707106781 * (tmp895 - tmp890);
	       c_im(output[56 * ostride]) = tmp899 - tmp900;
	       c_im(output[24 * ostride]) = tmp899 + tmp900;
	       tmp904 = K707106781 * (tmp902 - tmp903);
	       c_re(output[56 * ostride]) = tmp901 - tmp904;
	       c_re(output[24 * ostride]) = tmp901 + tmp904;
	       tmp906 = K707106781 * (tmp902 + tmp903);
	       c_im(output[40 * ostride]) = tmp905 - tmp906;
	       c_im(output[8 * ostride]) = tmp905 + tmp906;
	  }
     }
     {
	  fftw_real tmp217;
	  fftw_real tmp391;
	  fftw_real tmp396;
	  fftw_real tmp406;
	  fftw_real tmp399;
	  fftw_real tmp407;
	  fftw_real tmp367;
	  fftw_real tmp387;
	  fftw_real tmp312;
	  fftw_real tmp386;
	  fftw_real tmp379;
	  fftw_real tmp401;
	  fftw_real tmp382;
	  fftw_real tmp392;
	  fftw_real tmp256;
	  fftw_real tmp402;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp201;
	       fftw_real tmp216;
	       fftw_real tmp394;
	       fftw_real tmp395;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp201 = tmp193 - tmp200;
	       tmp216 = tmp208 - tmp215;
	       tmp217 = tmp201 - tmp216;
	       tmp391 = tmp201 + tmp216;
	       tmp394 = tmp274 + tmp297;
	       tmp395 = tmp307 + tmp310;
	       tmp396 = (K634393284 * tmp394) + (K773010453 * tmp395);
	       tmp406 = (K773010453 * tmp394) - (K634393284 * tmp395);
	  }
	  {
	       fftw_real tmp397;
	       fftw_real tmp398;
	       fftw_real tmp353;
	       fftw_real tmp366;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp397 = tmp329 + tmp352;
	       tmp398 = tmp362 + tmp365;
	       tmp399 = (K773010453 * tmp397) - (K634393284 * tmp398);
	       tmp407 = (K773010453 * tmp398) + (K634393284 * tmp397);
	       tmp353 = tmp329 - tmp352;
	       tmp366 = tmp362 - tmp365;
	       tmp367 = (K098017140 * tmp353) - (K995184726 * tmp366);
	       tmp387 = (K098017140 * tmp366) + (K995184726 * tmp353);
	  }
	  {
	       fftw_real tmp298;
	       fftw_real tmp311;
	       fftw_real tmp375;
	       fftw_real tmp378;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp298 = tmp274 - tmp297;
	       tmp311 = tmp307 - tmp310;
	       tmp312 = (K995184726 * tmp298) + (K098017140 * tmp311);
	       tmp386 = (K098017140 * tmp298) - (K995184726 * tmp311);
	       tmp375 = tmp371 - tmp374;
	       tmp378 = tmp376 - tmp377;
	       tmp379 = tmp375 - tmp378;
	       tmp401 = tmp375 + tmp378;
	  }
	  {
	       fftw_real tmp380;
	       fftw_real tmp381;
	       fftw_real tmp236;
	       fftw_real tmp255;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp380 = (K195090322 * tmp254) - (K980785280 * tmp247);
	       tmp381 = (K980785280 * tmp228) + (K195090322 * tmp235);
	       tmp382 = tmp380 - tmp381;
	       tmp392 = tmp381 + tmp380;
	       tmp236 = (K195090322 * tmp228) - (K980785280 * tmp235);
	       tmp255 = (K195090322 * tmp247) + (K980785280 * tmp254);
	       tmp256 = tmp236 - tmp255;
	       tmp402 = tmp236 + tmp255;
	  }
	  {
	       fftw_real tmp257;
	       fftw_real tmp368;
	       fftw_real tmp383;
	       fftw_real tmp384;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp257 = tmp217 + tmp256;
	       tmp368 = tmp312 + tmp367;
	       c_re(output[47 * ostride]) = tmp257 - tmp368;
	       c_re(output[15 * ostride]) = tmp257 + tmp368;
	       tmp383 = tmp379 - tmp382;
	       tmp384 = tmp367 - tmp312;
	       c_im(output[63 * ostride]) = tmp383 - tmp384;
	       c_im(output[31 * ostride]) = tmp383 + tmp384;
	  }
	  {
	       fftw_real tmp389;
	       fftw_real tmp390;
	       fftw_real tmp385;
	       fftw_real tmp388;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp389 = tmp379 + tmp382;
	       tmp390 = tmp386 + tmp387;
	       c_im(output[47 * ostride]) = tmp389 - tmp390;
	       c_im(output[15 * ostride]) = tmp389 + tmp390;
	       tmp385 = tmp217 - tmp256;
	       tmp388 = tmp386 - tmp387;
	       c_re(output[63 * ostride]) = tmp385 - tmp388;
	       c_re(output[31 * ostride]) = tmp385 + tmp388;
	  }
	  {
	       fftw_real tmp393;
	       fftw_real tmp400;
	       fftw_real tmp403;
	       fftw_real tmp404;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp393 = tmp391 + tmp392;
	       tmp400 = tmp396 + tmp399;
	       c_re(output[39 * ostride]) = tmp393 - tmp400;
	       c_re(output[7 * ostride]) = tmp393 + tmp400;
	       tmp403 = tmp401 - tmp402;
	       tmp404 = tmp399 - tmp396;
	       c_im(output[55 * ostride]) = tmp403 - tmp404;
	       c_im(output[23 * ostride]) = tmp403 + tmp404;
	  }
	  {
	       fftw_real tmp409;
	       fftw_real tmp410;
	       fftw_real tmp405;
	       fftw_real tmp408;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp409 = tmp401 + tmp402;
	       tmp410 = tmp406 + tmp407;
	       c_im(output[39 * ostride]) = tmp409 - tmp410;
	       c_im(output[7 * ostride]) = tmp409 + tmp410;
	       tmp405 = tmp391 - tmp392;
	       tmp408 = tmp406 - tmp407;
	       c_re(output[55 * ostride]) = tmp405 - tmp408;
	       c_re(output[23 * ostride]) = tmp405 + tmp408;
	  }
     }
     {
	  fftw_real tmp413;
	  fftw_real tmp451;
	  fftw_real tmp456;
	  fftw_real tmp466;
	  fftw_real tmp459;
	  fftw_real tmp467;
	  fftw_real tmp435;
	  fftw_real tmp447;
	  fftw_real tmp428;
	  fftw_real tmp446;
	  fftw_real tmp439;
	  fftw_real tmp461;
	  fftw_real tmp442;
	  fftw_real tmp452;
	  fftw_real tmp420;
	  fftw_real tmp462;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp411;
	       fftw_real tmp412;
	       fftw_real tmp454;
	       fftw_real tmp455;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp411 = tmp193 + tmp200;
	       tmp412 = tmp377 + tmp376;
	       tmp413 = tmp411 - tmp412;
	       tmp451 = tmp411 + tmp412;
	       tmp454 = tmp422 + tmp423;
	       tmp455 = tmp425 + tmp426;
	       tmp456 = (K956940335 * tmp454) + (K290284677 * tmp455);
	       tmp466 = (K956940335 * tmp455) - (K290284677 * tmp454);
	  }
	  {
	       fftw_real tmp457;
	       fftw_real tmp458;
	       fftw_real tmp431;
	       fftw_real tmp434;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp457 = tmp429 + tmp430;
	       tmp458 = tmp432 + tmp433;
	       tmp459 = (K956940335 * tmp457) - (K290284677 * tmp458);
	       tmp467 = (K290284677 * tmp457) + (K956940335 * tmp458);
	       tmp431 = tmp429 - tmp430;
	       tmp434 = tmp432 - tmp433;
	       tmp435 = (K471396736 * tmp431) - (K881921264 * tmp434);
	       tmp447 = (K881921264 * tmp431) + (K471396736 * tmp434);
	  }
	  {
	       fftw_real tmp424;
	       fftw_real tmp427;
	       fftw_real tmp437;
	       fftw_real tmp438;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp424 = tmp422 - tmp423;
	       tmp427 = tmp425 - tmp426;
	       tmp428 = (K471396736 * tmp424) + (K881921264 * tmp427);
	       tmp446 = (K471396736 * tmp427) - (K881921264 * tmp424);
	       tmp437 = tmp371 + tmp374;
	       tmp438 = tmp208 + tmp215;
	       tmp439 = tmp437 - tmp438;
	       tmp461 = tmp437 + tmp438;
	  }
	  {
	       fftw_real tmp440;
	       fftw_real tmp441;
	       fftw_real tmp416;
	       fftw_real tmp419;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp440 = (K831469612 * tmp418) - (K555570233 * tmp417);
	       tmp441 = (K555570233 * tmp414) + (K831469612 * tmp415);
	       tmp442 = tmp440 - tmp441;
	       tmp452 = tmp441 + tmp440;
	       tmp416 = (K831469612 * tmp414) - (K555570233 * tmp415);
	       tmp419 = (K831469612 * tmp417) + (K555570233 * tmp418);
	       tmp420 = tmp416 - tmp419;
	       tmp462 = tmp416 + tmp419;
	  }
	  {
	       fftw_real tmp421;
	       fftw_real tmp436;
	       fftw_real tmp443;
	       fftw_real tmp444;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp421 = tmp413 + tmp420;
	       tmp436 = tmp428 + tmp435;
	       c_re(output[43 * ostride]) = tmp421 - tmp436;
	       c_re(output[11 * ostride]) = tmp421 + tmp436;
	       tmp443 = tmp439 - tmp442;
	       tmp444 = tmp435 - tmp428;
	       c_im(output[59 * ostride]) = tmp443 - tmp444;
	       c_im(output[27 * ostride]) = tmp443 + tmp444;
	  }
	  {
	       fftw_real tmp449;
	       fftw_real tmp450;
	       fftw_real tmp445;
	       fftw_real tmp448;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp449 = tmp439 + tmp442;
	       tmp450 = tmp446 + tmp447;
	       c_im(output[43 * ostride]) = tmp449 - tmp450;
	       c_im(output[11 * ostride]) = tmp449 + tmp450;
	       tmp445 = tmp413 - tmp420;
	       tmp448 = tmp446 - tmp447;
	       c_re(output[59 * ostride]) = tmp445 - tmp448;
	       c_re(output[27 * ostride]) = tmp445 + tmp448;
	  }
	  {
	       fftw_real tmp453;
	       fftw_real tmp460;
	       fftw_real tmp463;
	       fftw_real tmp464;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp453 = tmp451 + tmp452;
	       tmp460 = tmp456 + tmp459;
	       c_re(output[35 * ostride]) = tmp453 - tmp460;
	       c_re(output[3 * ostride]) = tmp453 + tmp460;
	       tmp463 = tmp461 - tmp462;
	       tmp464 = tmp459 - tmp456;
	       c_im(output[51 * ostride]) = tmp463 - tmp464;
	       c_im(output[19 * ostride]) = tmp463 + tmp464;
	  }
	  {
	       fftw_real tmp469;
	       fftw_real tmp470;
	       fftw_real tmp465;
	       fftw_real tmp468;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp469 = tmp461 + tmp462;
	       tmp470 = tmp466 + tmp467;
	       c_im(output[35 * ostride]) = tmp469 - tmp470;
	       c_im(output[3 * ostride]) = tmp469 + tmp470;
	       tmp465 = tmp451 - tmp452;
	       tmp468 = tmp466 - tmp467;
	       c_re(output[51 * ostride]) = tmp465 - tmp468;
	       c_re(output[19 * ostride]) = tmp465 + tmp468;
	  }
     }
     {
	  fftw_real tmp817;
	  fftw_real tmp863;
	  fftw_real tmp824;
	  fftw_real tmp874;
	  fftw_real tmp854;
	  fftw_real tmp864;
	  fftw_real tmp836;
	  fftw_real tmp858;
	  fftw_real tmp851;
	  fftw_real tmp873;
	  fftw_real tmp868;
	  fftw_real tmp878;
	  fftw_real tmp847;
	  fftw_real tmp859;
	  fftw_real tmp871;
	  fftw_real tmp879;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp820;
	       fftw_real tmp823;
	       fftw_real tmp866;
	       fftw_real tmp867;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp817 = tmp815 - tmp816;
	       tmp863 = tmp815 + tmp816;
	       tmp820 = tmp818 - tmp819;
	       tmp823 = tmp821 + tmp822;
	       tmp824 = K707106781 * (tmp820 - tmp823);
	       tmp874 = K707106781 * (tmp820 + tmp823);
	       {
		    fftw_real tmp852;
		    fftw_real tmp853;
		    fftw_real tmp830;
		    fftw_real tmp835;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp852 = tmp821 - tmp822;
		    tmp853 = tmp819 + tmp818;
		    tmp854 = K707106781 * (tmp852 - tmp853);
		    tmp864 = K707106781 * (tmp853 + tmp852);
		    tmp830 = tmp828 - tmp829;
		    tmp835 = tmp831 - tmp834;
		    tmp836 = (K923879532 * tmp830) + (K382683432 * tmp835);
		    tmp858 = (K382683432 * tmp830) - (K923879532 * tmp835);
	       }
	       tmp851 = tmp849 - tmp850;
	       tmp873 = tmp850 + tmp849;
	       tmp866 = tmp829 + tmp828;
	       tmp867 = tmp831 + tmp834;
	       tmp868 = (K382683432 * tmp866) + (K923879532 * tmp867);
	       tmp878 = (K923879532 * tmp866) - (K382683432 * tmp867);
	       {
		    fftw_real tmp841;
		    fftw_real tmp846;
		    fftw_real tmp869;
		    fftw_real tmp870;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp841 = tmp837 - tmp840;
		    tmp846 = tmp844 - tmp845;
		    tmp847 = (K382683432 * tmp841) - (K923879532 * tmp846);
		    tmp859 = (K382683432 * tmp846) + (K923879532 * tmp841);
		    tmp869 = tmp837 + tmp840;
		    tmp870 = tmp845 + tmp844;
		    tmp871 = (K923879532 * tmp869) - (K382683432 * tmp870);
		    tmp879 = (K923879532 * tmp870) + (K382683432 * tmp869);
	       }
	  }
	  {
	       fftw_real tmp825;
	       fftw_real tmp848;
	       fftw_real tmp855;
	       fftw_real tmp856;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp825 = tmp817 + tmp824;
	       tmp848 = tmp836 + tmp847;
	       c_re(output[44 * ostride]) = tmp825 - tmp848;
	       c_re(output[12 * ostride]) = tmp825 + tmp848;
	       tmp855 = tmp851 - tmp854;
	       tmp856 = tmp847 - tmp836;
	       c_im(output[60 * ostride]) = tmp855 - tmp856;
	       c_im(output[28 * ostride]) = tmp855 + tmp856;
	  }
	  {
	       fftw_real tmp861;
	       fftw_real tmp862;
	       fftw_real tmp857;
	       fftw_real tmp860;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp861 = tmp851 + tmp854;
	       tmp862 = tmp858 + tmp859;
	       c_im(output[44 * ostride]) = tmp861 - tmp862;
	       c_im(output[12 * ostride]) = tmp861 + tmp862;
	       tmp857 = tmp817 - tmp824;
	       tmp860 = tmp858 - tmp859;
	       c_re(output[60 * ostride]) = tmp857 - tmp860;
	       c_re(output[28 * ostride]) = tmp857 + tmp860;
	  }
	  {
	       fftw_real tmp865;
	       fftw_real tmp872;
	       fftw_real tmp875;
	       fftw_real tmp876;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp865 = tmp863 + tmp864;
	       tmp872 = tmp868 + tmp871;
	       c_re(output[36 * ostride]) = tmp865 - tmp872;
	       c_re(output[4 * ostride]) = tmp865 + tmp872;
	       tmp875 = tmp873 - tmp874;
	       tmp876 = tmp871 - tmp868;
	       c_im(output[52 * ostride]) = tmp875 - tmp876;
	       c_im(output[20 * ostride]) = tmp875 + tmp876;
	  }
	  {
	       fftw_real tmp881;
	       fftw_real tmp882;
	       fftw_real tmp877;
	       fftw_real tmp880;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp881 = tmp873 + tmp874;
	       tmp882 = tmp878 + tmp879;
	       c_im(output[36 * ostride]) = tmp881 - tmp882;
	       c_im(output[4 * ostride]) = tmp881 + tmp882;
	       tmp877 = tmp863 - tmp864;
	       tmp880 = tmp878 - tmp879;
	       c_re(output[52 * ostride]) = tmp877 - tmp880;
	       c_re(output[20 * ostride]) = tmp877 + tmp880;
	  }
     }
     {
	  fftw_real tmp757;
	  fftw_real tmp795;
	  fftw_real tmp800;
	  fftw_real tmp810;
	  fftw_real tmp803;
	  fftw_real tmp811;
	  fftw_real tmp779;
	  fftw_real tmp791;
	  fftw_real tmp783;
	  fftw_real tmp805;
	  fftw_real tmp764;
	  fftw_real tmp806;
	  fftw_real tmp786;
	  fftw_real tmp796;
	  fftw_real tmp772;
	  fftw_real tmp790;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp756;
	       fftw_real tmp798;
	       fftw_real tmp799;
	       fftw_real tmp782;
	       fftw_real tmp760;
	       fftw_real tmp763;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp756 = K707106781 * (tmp721 + tmp720);
	       tmp757 = tmp755 - tmp756;
	       tmp795 = tmp755 + tmp756;
	       tmp798 = tmp766 + tmp767;
	       tmp799 = tmp769 + tmp770;
	       tmp800 = (K980785280 * tmp798) + (K195090322 * tmp799);
	       tmp810 = (K980785280 * tmp799) - (K195090322 * tmp798);
	       {
		    fftw_real tmp801;
		    fftw_real tmp802;
		    fftw_real tmp775;
		    fftw_real tmp778;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp801 = tmp773 + tmp774;
		    tmp802 = tmp776 + tmp777;
		    tmp803 = (K980785280 * tmp801) - (K195090322 * tmp802);
		    tmp811 = (K195090322 * tmp801) + (K980785280 * tmp802);
		    tmp775 = tmp773 - tmp774;
		    tmp778 = tmp776 - tmp777;
		    tmp779 = (K555570233 * tmp775) - (K831469612 * tmp778);
		    tmp791 = (K831469612 * tmp775) + (K555570233 * tmp778);
	       }
	       tmp782 = K707106781 * (tmp640 + tmp643);
	       tmp783 = tmp781 - tmp782;
	       tmp805 = tmp781 + tmp782;
	       tmp760 = (K923879532 * tmp758) - (K382683432 * tmp759);
	       tmp763 = (K923879532 * tmp761) + (K382683432 * tmp762);
	       tmp764 = tmp760 - tmp763;
	       tmp806 = tmp760 + tmp763;
	       {
		    fftw_real tmp784;
		    fftw_real tmp785;
		    fftw_real tmp768;
		    fftw_real tmp771;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp784 = (K923879532 * tmp762) - (K382683432 * tmp761);
		    tmp785 = (K382683432 * tmp758) + (K923879532 * tmp759);
		    tmp786 = tmp784 - tmp785;
		    tmp796 = tmp785 + tmp784;
		    tmp768 = tmp766 - tmp767;
		    tmp771 = tmp769 - tmp770;
		    tmp772 = (K555570233 * tmp768) + (K831469612 * tmp771);
		    tmp790 = (K555570233 * tmp771) - (K831469612 * tmp768);
	       }
	  }
	  {
	       fftw_real tmp765;
	       fftw_real tmp780;
	       fftw_real tmp787;
	       fftw_real tmp788;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp765 = tmp757 + tmp764;
	       tmp780 = tmp772 + tmp779;
	       c_re(output[42 * ostride]) = tmp765 - tmp780;
	       c_re(output[10 * ostride]) = tmp765 + tmp780;
	       tmp787 = tmp783 - tmp786;
	       tmp788 = tmp779 - tmp772;
	       c_im(output[58 * ostride]) = tmp787 - tmp788;
	       c_im(output[26 * ostride]) = tmp787 + tmp788;
	  }
	  {
	       fftw_real tmp793;
	       fftw_real tmp794;
	       fftw_real tmp789;
	       fftw_real tmp792;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp793 = tmp783 + tmp786;
	       tmp794 = tmp790 + tmp791;
	       c_im(output[42 * ostride]) = tmp793 - tmp794;
	       c_im(output[10 * ostride]) = tmp793 + tmp794;
	       tmp789 = tmp757 - tmp764;
	       tmp792 = tmp790 - tmp791;
	       c_re(output[58 * ostride]) = tmp789 - tmp792;
	       c_re(output[26 * ostride]) = tmp789 + tmp792;
	  }
	  {
	       fftw_real tmp797;
	       fftw_real tmp804;
	       fftw_real tmp807;
	       fftw_real tmp808;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp797 = tmp795 + tmp796;
	       tmp804 = tmp800 + tmp803;
	       c_re(output[34 * ostride]) = tmp797 - tmp804;
	       c_re(output[2 * ostride]) = tmp797 + tmp804;
	       tmp807 = tmp805 - tmp806;
	       tmp808 = tmp803 - tmp800;
	       c_im(output[50 * ostride]) = tmp807 - tmp808;
	       c_im(output[18 * ostride]) = tmp807 + tmp808;
	  }
	  {
	       fftw_real tmp813;
	       fftw_real tmp814;
	       fftw_real tmp809;
	       fftw_real tmp812;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp813 = tmp805 + tmp806;
	       tmp814 = tmp810 + tmp811;
	       c_im(output[34 * ostride]) = tmp813 - tmp814;
	       c_im(output[2 * ostride]) = tmp813 + tmp814;
	       tmp809 = tmp795 - tmp796;
	       tmp812 = tmp810 - tmp811;
	       c_re(output[50 * ostride]) = tmp809 - tmp812;
	       c_re(output[18 * ostride]) = tmp809 + tmp812;
	  }
     }
     {
	  fftw_real tmp645;
	  fftw_real tmp735;
	  fftw_real tmp740;
	  fftw_real tmp750;
	  fftw_real tmp743;
	  fftw_real tmp751;
	  fftw_real tmp715;
	  fftw_real tmp731;
	  fftw_real tmp723;
	  fftw_real tmp745;
	  fftw_real tmp660;
	  fftw_real tmp746;
	  fftw_real tmp726;
	  fftw_real tmp736;
	  fftw_real tmp688;
	  fftw_real tmp730;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp644;
	       fftw_real tmp738;
	       fftw_real tmp739;
	       fftw_real tmp722;
	       fftw_real tmp652;
	       fftw_real tmp659;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp644 = K707106781 * (tmp640 - tmp643);
	       tmp645 = tmp637 - tmp644;
	       tmp735 = tmp637 + tmp644;
	       tmp738 = tmp666 + tmp677;
	       tmp739 = tmp683 + tmp686;
	       tmp740 = (K555570233 * tmp738) + (K831469612 * tmp739);
	       tmp750 = (K831469612 * tmp738) - (K555570233 * tmp739);
	       {
		    fftw_real tmp741;
		    fftw_real tmp742;
		    fftw_real tmp705;
		    fftw_real tmp714;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp741 = tmp693 + tmp704;
		    tmp742 = tmp710 + tmp713;
		    tmp743 = (K831469612 * tmp741) - (K555570233 * tmp742);
		    tmp751 = (K831469612 * tmp742) + (K555570233 * tmp741);
		    tmp705 = tmp693 - tmp704;
		    tmp714 = tmp710 - tmp713;
		    tmp715 = (K195090322 * tmp705) - (K980785280 * tmp714);
		    tmp731 = (K195090322 * tmp714) + (K980785280 * tmp705);
	       }
	       tmp722 = K707106781 * (tmp720 - tmp721);
	       tmp723 = tmp719 - tmp722;
	       tmp745 = tmp719 + tmp722;
	       tmp652 = (K382683432 * tmp648) - (K923879532 * tmp651);
	       tmp659 = (K382683432 * tmp655) + (K923879532 * tmp658);
	       tmp660 = tmp652 - tmp659;
	       tmp746 = tmp652 + tmp659;
	       {
		    fftw_real tmp724;
		    fftw_real tmp725;
		    fftw_real tmp678;
		    fftw_real tmp687;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp724 = (K382683432 * tmp658) - (K923879532 * tmp655);
		    tmp725 = (K923879532 * tmp648) + (K382683432 * tmp651);
		    tmp726 = tmp724 - tmp725;
		    tmp736 = tmp725 + tmp724;
		    tmp678 = tmp666 - tmp677;
		    tmp687 = tmp683 - tmp686;
		    tmp688 = (K980785280 * tmp678) + (K195090322 * tmp687);
		    tmp730 = (K195090322 * tmp678) - (K980785280 * tmp687);
	       }
	  }
	  {
	       fftw_real tmp661;
	       fftw_real tmp716;
	       fftw_real tmp727;
	       fftw_real tmp728;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp661 = tmp645 + tmp660;
	       tmp716 = tmp688 + tmp715;
	       c_re(output[46 * ostride]) = tmp661 - tmp716;
	       c_re(output[14 * ostride]) = tmp661 + tmp716;
	       tmp727 = tmp723 - tmp726;
	       tmp728 = tmp715 - tmp688;
	       c_im(output[62 * ostride]) = tmp727 - tmp728;
	       c_im(output[30 * ostride]) = tmp727 + tmp728;
	  }
	  {
	       fftw_real tmp733;
	       fftw_real tmp734;
	       fftw_real tmp729;
	       fftw_real tmp732;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp733 = tmp723 + tmp726;
	       tmp734 = tmp730 + tmp731;
	       c_im(output[46 * ostride]) = tmp733 - tmp734;
	       c_im(output[14 * ostride]) = tmp733 + tmp734;
	       tmp729 = tmp645 - tmp660;
	       tmp732 = tmp730 - tmp731;
	       c_re(output[62 * ostride]) = tmp729 - tmp732;
	       c_re(output[30 * ostride]) = tmp729 + tmp732;
	  }
	  {
	       fftw_real tmp737;
	       fftw_real tmp744;
	       fftw_real tmp747;
	       fftw_real tmp748;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp737 = tmp735 + tmp736;
	       tmp744 = tmp740 + tmp743;
	       c_re(output[38 * ostride]) = tmp737 - tmp744;
	       c_re(output[6 * ostride]) = tmp737 + tmp744;
	       tmp747 = tmp745 - tmp746;
	       tmp748 = tmp743 - tmp740;
	       c_im(output[54 * ostride]) = tmp747 - tmp748;
	       c_im(output[22 * ostride]) = tmp747 + tmp748;
	  }
	  {
	       fftw_real tmp753;
	       fftw_real tmp754;
	       fftw_real tmp749;
	       fftw_real tmp752;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp753 = tmp745 + tmp746;
	       tmp754 = tmp750 + tmp751;
	       c_im(output[38 * ostride]) = tmp753 - tmp754;
	       c_im(output[6 * ostride]) = tmp753 + tmp754;
	       tmp749 = tmp735 - tmp736;
	       tmp752 = tmp750 - tmp751;
	       c_re(output[54 * ostride]) = tmp749 - tmp752;
	       c_re(output[22 * ostride]) = tmp749 + tmp752;
	  }
     }
     {
	  fftw_real tmp481;
	  fftw_real tmp555;
	  fftw_real tmp560;
	  fftw_real tmp570;
	  fftw_real tmp563;
	  fftw_real tmp571;
	  fftw_real tmp535;
	  fftw_real tmp551;
	  fftw_real tmp516;
	  fftw_real tmp550;
	  fftw_real tmp543;
	  fftw_real tmp565;
	  fftw_real tmp546;
	  fftw_real tmp556;
	  fftw_real tmp496;
	  fftw_real tmp566;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp473;
	       fftw_real tmp480;
	       fftw_real tmp558;
	       fftw_real tmp559;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp473 = tmp471 - tmp472;
	       tmp480 = tmp476 - tmp479;
	       tmp481 = tmp473 - tmp480;
	       tmp555 = tmp473 + tmp480;
	       tmp558 = tmp500 + tmp507;
	       tmp559 = tmp511 + tmp514;
	       tmp560 = (K471396736 * tmp558) + (K881921264 * tmp559);
	       tmp570 = (K881921264 * tmp558) - (K471396736 * tmp559);
	  }
	  {
	       fftw_real tmp561;
	       fftw_real tmp562;
	       fftw_real tmp527;
	       fftw_real tmp534;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp561 = tmp519 + tmp526;
	       tmp562 = tmp530 + tmp533;
	       tmp563 = (K881921264 * tmp561) - (K471396736 * tmp562);
	       tmp571 = (K881921264 * tmp562) + (K471396736 * tmp561);
	       tmp527 = tmp519 - tmp526;
	       tmp534 = tmp530 - tmp533;
	       tmp535 = (K290284677 * tmp527) - (K956940335 * tmp534);
	       tmp551 = (K290284677 * tmp534) + (K956940335 * tmp527);
	  }
	  {
	       fftw_real tmp508;
	       fftw_real tmp515;
	       fftw_real tmp539;
	       fftw_real tmp542;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp508 = tmp500 - tmp507;
	       tmp515 = tmp511 - tmp514;
	       tmp516 = (K956940335 * tmp508) + (K290284677 * tmp515);
	       tmp550 = (K290284677 * tmp508) - (K956940335 * tmp515);
	       tmp539 = tmp537 - tmp538;
	       tmp542 = tmp540 - tmp541;
	       tmp543 = tmp539 - tmp542;
	       tmp565 = tmp539 + tmp542;
	  }
	  {
	       fftw_real tmp544;
	       fftw_real tmp545;
	       fftw_real tmp488;
	       fftw_real tmp495;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp544 = (K555570233 * tmp491) - (K831469612 * tmp494);
	       tmp545 = (K555570233 * tmp487) + (K831469612 * tmp484);
	       tmp546 = tmp544 - tmp545;
	       tmp556 = tmp545 + tmp544;
	       tmp488 = (K555570233 * tmp484) - (K831469612 * tmp487);
	       tmp495 = (K831469612 * tmp491) + (K555570233 * tmp494);
	       tmp496 = tmp488 - tmp495;
	       tmp566 = tmp488 + tmp495;
	  }
	  {
	       fftw_real tmp497;
	       fftw_real tmp536;
	       fftw_real tmp547;
	       fftw_real tmp548;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp497 = tmp481 + tmp496;
	       tmp536 = tmp516 + tmp535;
	       c_re(output[45 * ostride]) = tmp497 - tmp536;
	       c_re(output[13 * ostride]) = tmp497 + tmp536;
	       tmp547 = tmp543 - tmp546;
	       tmp548 = tmp535 - tmp516;
	       c_im(output[61 * ostride]) = tmp547 - tmp548;
	       c_im(output[29 * ostride]) = tmp547 + tmp548;
	  }
	  {
	       fftw_real tmp553;
	       fftw_real tmp554;
	       fftw_real tmp549;
	       fftw_real tmp552;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp553 = tmp543 + tmp546;
	       tmp554 = tmp550 + tmp551;
	       c_im(output[45 * ostride]) = tmp553 - tmp554;
	       c_im(output[13 * ostride]) = tmp553 + tmp554;
	       tmp549 = tmp481 - tmp496;
	       tmp552 = tmp550 - tmp551;
	       c_re(output[61 * ostride]) = tmp549 - tmp552;
	       c_re(output[29 * ostride]) = tmp549 + tmp552;
	  }
	  {
	       fftw_real tmp557;
	       fftw_real tmp564;
	       fftw_real tmp567;
	       fftw_real tmp568;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp557 = tmp555 + tmp556;
	       tmp564 = tmp560 + tmp563;
	       c_re(output[37 * ostride]) = tmp557 - tmp564;
	       c_re(output[5 * ostride]) = tmp557 + tmp564;
	       tmp567 = tmp565 - tmp566;
	       tmp568 = tmp563 - tmp560;
	       c_im(output[53 * ostride]) = tmp567 - tmp568;
	       c_im(output[21 * ostride]) = tmp567 + tmp568;
	  }
	  {
	       fftw_real tmp573;
	       fftw_real tmp574;
	       fftw_real tmp569;
	       fftw_real tmp572;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp573 = tmp565 + tmp566;
	       tmp574 = tmp570 + tmp571;
	       c_im(output[37 * ostride]) = tmp573 - tmp574;
	       c_im(output[5 * ostride]) = tmp573 + tmp574;
	       tmp569 = tmp555 - tmp556;
	       tmp572 = tmp570 - tmp571;
	       c_re(output[53 * ostride]) = tmp569 - tmp572;
	       c_re(output[21 * ostride]) = tmp569 + tmp572;
	  }
     }
     {
	  fftw_real tmp577;
	  fftw_real tmp615;
	  fftw_real tmp620;
	  fftw_real tmp630;
	  fftw_real tmp623;
	  fftw_real tmp631;
	  fftw_real tmp599;
	  fftw_real tmp611;
	  fftw_real tmp592;
	  fftw_real tmp610;
	  fftw_real tmp603;
	  fftw_real tmp625;
	  fftw_real tmp606;
	  fftw_real tmp616;
	  fftw_real tmp584;
	  fftw_real tmp626;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp575;
	       fftw_real tmp576;
	       fftw_real tmp618;
	       fftw_real tmp619;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp575 = tmp471 + tmp472;
	       tmp576 = tmp541 + tmp540;
	       tmp577 = tmp575 - tmp576;
	       tmp615 = tmp575 + tmp576;
	       tmp618 = tmp586 + tmp587;
	       tmp619 = tmp589 + tmp590;
	       tmp620 = (K995184726 * tmp618) + (K098017140 * tmp619);
	       tmp630 = (K995184726 * tmp619) - (K098017140 * tmp618);
	  }
	  {
	       fftw_real tmp621;
	       fftw_real tmp622;
	       fftw_real tmp595;
	       fftw_real tmp598;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp621 = tmp593 + tmp594;
	       tmp622 = tmp596 + tmp597;
	       tmp623 = (K995184726 * tmp621) - (K098017140 * tmp622);
	       tmp631 = (K098017140 * tmp621) + (K995184726 * tmp622);
	       tmp595 = tmp593 - tmp594;
	       tmp598 = tmp596 - tmp597;
	       tmp599 = (K634393284 * tmp595) - (K773010453 * tmp598);
	       tmp611 = (K773010453 * tmp595) + (K634393284 * tmp598);
	  }
	  {
	       fftw_real tmp588;
	       fftw_real tmp591;
	       fftw_real tmp601;
	       fftw_real tmp602;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp588 = tmp586 - tmp587;
	       tmp591 = tmp589 - tmp590;
	       tmp592 = (K634393284 * tmp588) + (K773010453 * tmp591);
	       tmp610 = (K634393284 * tmp591) - (K773010453 * tmp588);
	       tmp601 = tmp537 + tmp538;
	       tmp602 = tmp476 + tmp479;
	       tmp603 = tmp601 - tmp602;
	       tmp625 = tmp601 + tmp602;
	  }
	  {
	       fftw_real tmp604;
	       fftw_real tmp605;
	       fftw_real tmp580;
	       fftw_real tmp583;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp604 = (K980785280 * tmp581) - (K195090322 * tmp582);
	       tmp605 = (K980785280 * tmp579) + (K195090322 * tmp578);
	       tmp606 = tmp604 - tmp605;
	       tmp616 = tmp605 + tmp604;
	       tmp580 = (K980785280 * tmp578) - (K195090322 * tmp579);
	       tmp583 = (K195090322 * tmp581) + (K980785280 * tmp582);
	       tmp584 = tmp580 - tmp583;
	       tmp626 = tmp580 + tmp583;
	  }
	  {
	       fftw_real tmp585;
	       fftw_real tmp600;
	       fftw_real tmp607;
	       fftw_real tmp608;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp585 = tmp577 + tmp584;
	       tmp600 = tmp592 + tmp599;
	       c_re(output[41 * ostride]) = tmp585 - tmp600;
	       c_re(output[9 * ostride]) = tmp585 + tmp600;
	       tmp607 = tmp603 - tmp606;
	       tmp608 = tmp599 - tmp592;
	       c_im(output[57 * ostride]) = tmp607 - tmp608;
	       c_im(output[25 * ostride]) = tmp607 + tmp608;
	  }
	  {
	       fftw_real tmp613;
	       fftw_real tmp614;
	       fftw_real tmp609;
	       fftw_real tmp612;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp613 = tmp603 + tmp606;
	       tmp614 = tmp610 + tmp611;
	       c_im(output[41 * ostride]) = tmp613 - tmp614;
	       c_im(output[9 * ostride]) = tmp613 + tmp614;
	       tmp609 = tmp577 - tmp584;
	       tmp612 = tmp610 - tmp611;
	       c_re(output[57 * ostride]) = tmp609 - tmp612;
	       c_re(output[25 * ostride]) = tmp609 + tmp612;
	  }
	  {
	       fftw_real tmp617;
	       fftw_real tmp624;
	       fftw_real tmp627;
	       fftw_real tmp628;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp617 = tmp615 + tmp616;
	       tmp624 = tmp620 + tmp623;
	       c_re(output[33 * ostride]) = tmp617 - tmp624;
	       c_re(output[ostride]) = tmp617 + tmp624;
	       tmp627 = tmp625 - tmp626;
	       tmp628 = tmp623 - tmp620;
	       c_im(output[49 * ostride]) = tmp627 - tmp628;
	       c_im(output[17 * ostride]) = tmp627 + tmp628;
	  }
	  {
	       fftw_real tmp633;
	       fftw_real tmp634;
	       fftw_real tmp629;
	       fftw_real tmp632;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp633 = tmp625 + tmp626;
	       tmp634 = tmp630 + tmp631;
	       c_im(output[33 * ostride]) = tmp633 - tmp634;
	       c_im(output[ostride]) = tmp633 + tmp634;
	       tmp629 = tmp615 - tmp616;
	       tmp632 = tmp630 - tmp631;
	       c_re(output[49 * ostride]) = tmp629 - tmp632;
	       c_re(output[17 * ostride]) = tmp629 + tmp632;
	  }
     }
}

fftw_codelet_desc fftw_no_twiddle_64_desc =
{
     "fftw_no_twiddle_64",
     (void (*)()) fftw_no_twiddle_64,
     64,
     FFTW_FORWARD,
     FFTW_NOTW,
     1409,
     0,
     (const int *) 0,
};
