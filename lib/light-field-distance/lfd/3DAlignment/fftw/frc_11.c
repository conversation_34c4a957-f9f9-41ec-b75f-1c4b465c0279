/*
 * Copyright (c) 1997-1999 Massachusetts Institute of Technology
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 *
 */

/* This file was automatically generated --- DO NOT EDIT */
/* Generated on Sun Nov  7 20:43:55 EST 1999 */

#include "fftw-int.h"
#include "fftw.h"

/* Generated by: ./genfft -magic-alignment-check -magic-twiddle-load-all -magic-variables 4 -magic-loopi -real2hc 11 */

/*
 * This function contains 60 FP additions, 50 FP multiplications,
 * (or, 60 additions, 50 multiplications, 0 fused multiply/add),
 * 17 stack variables, and 22 memory accesses
 */
static const fftw_real K654860733 = FFTW_KONST(+0.654860733945285064056925072466293553183791199);
static const fftw_real K142314838 = FFTW_KONST(+0.142314838273285140443792668616369668791051361);
static const fftw_real K959492973 = FFTW_KONST(+0.959492973614497389890368057066327699062454848);
static const fftw_real K415415013 = FFTW_KONST(+0.415415013001886425529274149229623203524004910);
static const fftw_real K841253532 = FFTW_KONST(+0.841253532831181168861811648919367717513292498);
static const fftw_real K989821441 = FFTW_KONST(+0.989821441880932732376092037776718787376519372);
static const fftw_real K909631995 = FFTW_KONST(+0.909631995354518371411715383079028460060241051);
static const fftw_real K281732556 = FFTW_KONST(+0.281732556841429697711417915346616899035777899);
static const fftw_real K540640817 = FFTW_KONST(+0.540640817455597582107635954318691695431770608);
static const fftw_real K755749574 = FFTW_KONST(+0.755749574354258283774035843972344420179717445);

/*
 * Generator Id's : 
 * $Id: exprdag.ml,v 1.41 1999/05/26 15:44:14 fftw Exp $
 * $Id: fft.ml,v 1.43 1999/05/17 19:44:18 fftw Exp $
 * $Id: to_c.ml,v 1.25 1999/10/26 21:41:32 stevenj Exp $
 */

void fftw_real2hc_11(const fftw_real *input, fftw_real *real_output, fftw_real *imag_output, int istride, int real_ostride, int imag_ostride)
{
     fftw_real tmp1;
     fftw_real tmp4;
     fftw_real tmp21;
     fftw_real tmp16;
     fftw_real tmp17;
     fftw_real tmp13;
     fftw_real tmp18;
     fftw_real tmp10;
     fftw_real tmp20;
     fftw_real tmp7;
     fftw_real tmp19;
     fftw_real tmp11;
     fftw_real tmp12;
     ASSERT_ALIGNED_DOUBLE;
     tmp1 = input[0];
     {
	  fftw_real tmp2;
	  fftw_real tmp3;
	  fftw_real tmp14;
	  fftw_real tmp15;
	  ASSERT_ALIGNED_DOUBLE;
	  tmp2 = input[2 * istride];
	  tmp3 = input[9 * istride];
	  tmp4 = tmp2 + tmp3;
	  tmp21 = tmp3 - tmp2;
	  tmp14 = input[istride];
	  tmp15 = input[10 * istride];
	  tmp16 = tmp14 + tmp15;
	  tmp17 = tmp15 - tmp14;
     }
     tmp11 = input[3 * istride];
     tmp12 = input[8 * istride];
     tmp13 = tmp11 + tmp12;
     tmp18 = tmp12 - tmp11;
     {
	  fftw_real tmp8;
	  fftw_real tmp9;
	  fftw_real tmp5;
	  fftw_real tmp6;
	  ASSERT_ALIGNED_DOUBLE;
	  tmp8 = input[5 * istride];
	  tmp9 = input[6 * istride];
	  tmp10 = tmp8 + tmp9;
	  tmp20 = tmp9 - tmp8;
	  tmp5 = input[4 * istride];
	  tmp6 = input[7 * istride];
	  tmp7 = tmp5 + tmp6;
	  tmp19 = tmp6 - tmp5;
     }
     imag_output[4 * imag_ostride] = (K755749574 * tmp17) + (K540640817 * tmp18) + (K281732556 * tmp19) - (K909631995 * tmp20) - (K989821441 * tmp21);
     imag_output[imag_ostride] = (K540640817 * tmp17) + (K909631995 * tmp21) + (K989821441 * tmp18) + (K755749574 * tmp19) + (K281732556 * tmp20);
     imag_output[5 * imag_ostride] = (K281732556 * tmp17) + (K755749574 * tmp18) + (K989821441 * tmp20) - (K909631995 * tmp19) - (K540640817 * tmp21);
     imag_output[2 * imag_ostride] = (K909631995 * tmp17) + (K755749574 * tmp21) - (K540640817 * tmp20) - (K989821441 * tmp19) - (K281732556 * tmp18);
     imag_output[3 * imag_ostride] = (K989821441 * tmp17) + (K540640817 * tmp19) + (K755749574 * tmp20) - (K909631995 * tmp18) - (K281732556 * tmp21);
     real_output[4 * real_ostride] = tmp1 + (K841253532 * tmp13) + (K415415013 * tmp10) - (K959492973 * tmp7) - (K142314838 * tmp4) - (K654860733 * tmp16);
     real_output[5 * real_ostride] = tmp1 + (K841253532 * tmp4) + (K415415013 * tmp7) - (K142314838 * tmp10) - (K654860733 * tmp13) - (K959492973 * tmp16);
     real_output[real_ostride] = tmp1 + (K841253532 * tmp16) + (K415415013 * tmp4) - (K959492973 * tmp10) - (K654860733 * tmp7) - (K142314838 * tmp13);
     real_output[0] = tmp1 + tmp16 + tmp4 + tmp13 + tmp7 + tmp10;
     real_output[3 * real_ostride] = tmp1 + (K415415013 * tmp13) + (K841253532 * tmp7) - (K654860733 * tmp10) - (K959492973 * tmp4) - (K142314838 * tmp16);
     real_output[2 * real_ostride] = tmp1 + (K415415013 * tmp16) + (K841253532 * tmp10) - (K142314838 * tmp7) - (K959492973 * tmp13) - (K654860733 * tmp4);
}

fftw_codelet_desc fftw_real2hc_11_desc =
{
     "fftw_real2hc_11",
     (void (*)()) fftw_real2hc_11,
     11,
     FFTW_FORWARD,
     FFTW_REAL2HC,
     244,
     0,
     (const int *) 0,
};
