/*
 * Copyright (c) 1997-1999 Massachusetts Institute of Technology
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 *
 */

/* This file was automatically generated --- DO NOT EDIT */
/* Generated on Sun Nov  7 20:43:55 EST 1999 */

#include "fftw-int.h"
#include "fftw.h"

/* Generated by: ./genfft -magic-alignment-check -magic-twiddle-load-all -magic-variables 4 -magic-loopi -real2hc 9 */

/*
 * This function contains 38 FP additions, 26 FP multiplications,
 * (or, 29 additions, 17 multiplications, 9 fused multiply/add),
 * 21 stack variables, and 18 memory accesses
 */
static const fftw_real K342020143 = FFTW_KONST(+0.342020143325668733044099614682259580763083368);
static const fftw_real K813797681 = FFTW_KONST(+0.813797681349373692844693217248393223289101568);
static const fftw_real K939692620 = FFTW_KONST(+0.939692620785908384054109277324731469936208134);
static const fftw_real K296198132 = FFTW_KONST(+0.296198132726023843175338011893050938967728390);
static const fftw_real K852868531 = FFTW_KONST(+0.852868531952443209628250963940074071936020296);
static const fftw_real K173648177 = FFTW_KONST(+0.173648177666930348851716626769314796000375677);
static const fftw_real K556670399 = FFTW_KONST(+0.556670399226419366452912952047023132968291906);
static const fftw_real K766044443 = FFTW_KONST(+0.766044443118978035202392650555416673935832457);
static const fftw_real K984807753 = FFTW_KONST(+0.984807753012208059366743024589523013670643252);
static const fftw_real K150383733 = FFTW_KONST(+0.150383733180435296639271897612501926072238258);
static const fftw_real K642787609 = FFTW_KONST(+0.642787609686539326322643409907263432907559884);
static const fftw_real K663413948 = FFTW_KONST(+0.663413948168938396205421319635891297216863310);
static const fftw_real K866025403 = FFTW_KONST(+0.866025403784438646763723170752936183471402627);
static const fftw_real K500000000 = FFTW_KONST(+0.500000000000000000000000000000000000000000000);

/*
 * Generator Id's : 
 * $Id: exprdag.ml,v 1.41 1999/05/26 15:44:14 fftw Exp $
 * $Id: fft.ml,v 1.43 1999/05/17 19:44:18 fftw Exp $
 * $Id: to_c.ml,v 1.25 1999/10/26 21:41:32 stevenj Exp $
 */

void fftw_real2hc_9(const fftw_real *input, fftw_real *real_output, fftw_real *imag_output, int istride, int real_ostride, int imag_ostride)
{
     fftw_real tmp1;
     fftw_real tmp15;
     fftw_real tmp20;
     fftw_real tmp19;
     fftw_real tmp4;
     fftw_real tmp27;
     fftw_real tmp10;
     fftw_real tmp18;
     fftw_real tmp21;
     fftw_real tmp2;
     fftw_real tmp3;
     fftw_real tmp5;
     fftw_real tmp16;
     ASSERT_ALIGNED_DOUBLE;
     tmp1 = input[0];
     {
	  fftw_real tmp11;
	  fftw_real tmp12;
	  fftw_real tmp13;
	  fftw_real tmp14;
	  ASSERT_ALIGNED_DOUBLE;
	  tmp11 = input[2 * istride];
	  tmp12 = input[5 * istride];
	  tmp13 = input[8 * istride];
	  tmp14 = tmp12 + tmp13;
	  tmp15 = tmp11 + tmp14;
	  tmp20 = tmp11 - (K500000000 * tmp14);
	  tmp19 = tmp13 - tmp12;
     }
     tmp2 = input[3 * istride];
     tmp3 = input[6 * istride];
     tmp4 = tmp2 + tmp3;
     tmp27 = tmp3 - tmp2;
     {
	  fftw_real tmp6;
	  fftw_real tmp7;
	  fftw_real tmp8;
	  fftw_real tmp9;
	  ASSERT_ALIGNED_DOUBLE;
	  tmp6 = input[istride];
	  tmp7 = input[4 * istride];
	  tmp8 = input[7 * istride];
	  tmp9 = tmp7 + tmp8;
	  tmp10 = tmp6 + tmp9;
	  tmp18 = tmp6 - (K500000000 * tmp9);
	  tmp21 = tmp8 - tmp7;
     }
     imag_output[3 * imag_ostride] = K866025403 * (tmp15 - tmp10);
     tmp5 = tmp1 + tmp4;
     tmp16 = tmp10 + tmp15;
     real_output[3 * real_ostride] = tmp5 - (K500000000 * tmp16);
     real_output[0] = tmp5 + tmp16;
     {
	  fftw_real tmp29;
	  fftw_real tmp25;
	  fftw_real tmp26;
	  fftw_real tmp28;
	  fftw_real tmp17;
	  fftw_real tmp22;
	  fftw_real tmp23;
	  fftw_real tmp24;
	  ASSERT_ALIGNED_DOUBLE;
	  tmp29 = K866025403 * tmp27;
	  tmp25 = (K663413948 * tmp21) - (K642787609 * tmp18);
	  tmp26 = (K150383733 * tmp19) - (K984807753 * tmp20);
	  tmp28 = tmp25 + tmp26;
	  tmp17 = tmp1 - (K500000000 * tmp4);
	  tmp22 = (K766044443 * tmp18) + (K556670399 * tmp21);
	  tmp23 = (K173648177 * tmp20) + (K852868531 * tmp19);
	  tmp24 = tmp22 + tmp23;
	  real_output[real_ostride] = tmp17 + tmp24;
	  real_output[4 * real_ostride] = tmp17 + (K866025403 * (tmp25 - tmp26)) - (K500000000 * tmp24);
	  real_output[2 * real_ostride] = tmp17 + (K173648177 * tmp18) - (K296198132 * tmp19) - (K939692620 * tmp20) - (K852868531 * tmp21);
	  imag_output[imag_ostride] = tmp29 + tmp28;
	  imag_output[4 * imag_ostride] = (K866025403 * (tmp27 + (tmp23 - tmp22))) - (K500000000 * tmp28);
	  imag_output[2 * imag_ostride] = (K813797681 * tmp19) - (K342020143 * tmp20) - (K150383733 * tmp21) - (K984807753 * tmp18) - tmp29;
     }
}

fftw_codelet_desc fftw_real2hc_9_desc =
{
     "fftw_real2hc_9",
     (void (*)()) fftw_real2hc_9,
     9,
     FFTW_FORWARD,
     FFTW_REAL2HC,
     200,
     0,
     (const int *) 0,
};
