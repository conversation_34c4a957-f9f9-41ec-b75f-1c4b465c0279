/*
 * Copyright (c) 1997-1999 Massachusetts Institute of Technology
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 *
 */

/* This file was automatically generated --- DO NOT EDIT */
/* Generated on Sun Nov  7 20:45:01 EST 1999 */

#include "fftw-int.h"
#include "fftw.h"

/* Generated by: ./genfft -magic-alignment-check -magic-twiddle-load-all -magic-variables 4 -magic-loopi -twiddleinv 32 */

/*
 * This function contains 434 FP additions, 208 FP multiplications,
 * (or, 340 additions, 114 multiplications, 94 fused multiply/add),
 * 90 stack variables, and 128 memory accesses
 */
static const fftw_real K555570233 = FFTW_KONST(+0.555570233019602224742830813948532874374937191);
static const fftw_real K831469612 = FFTW_KONST(+0.831469612302545237078788377617905756738560812);
static const fftw_real K980785280 = FFTW_KONST(+0.980785280403230449126182236134239036973933731);
static const fftw_real K195090322 = FFTW_KONST(+0.195090322016128267848284868477022240927691618);
static const fftw_real K923879532 = FFTW_KONST(+0.923879532511286756128183189396788286822416626);
static const fftw_real K382683432 = FFTW_KONST(+0.382683432365089771728459984030398866761344562);
static const fftw_real K707106781 = FFTW_KONST(+0.707106781186547524400844362104849039284835938);

/*
 * Generator Id's : 
 * $Id: exprdag.ml,v 1.41 1999/05/26 15:44:14 fftw Exp $
 * $Id: fft.ml,v 1.43 1999/05/17 19:44:18 fftw Exp $
 * $Id: to_c.ml,v 1.25 1999/10/26 21:41:32 stevenj Exp $
 */

void fftwi_twiddle_32(fftw_complex *A, const fftw_complex *W, int iostride, int m, int dist)
{
     int i;
     fftw_complex *inout;
     inout = A;
     for (i = m; i > 0; i = i - 1, inout = inout + dist, W = W + 31) {
	  fftw_real tmp19;
	  fftw_real tmp387;
	  fftw_real tmp472;
	  fftw_real tmp486;
	  fftw_real tmp442;
	  fftw_real tmp456;
	  fftw_real tmp191;
	  fftw_real tmp303;
	  fftw_real tmp161;
	  fftw_real tmp403;
	  fftw_real tmp276;
	  fftw_real tmp316;
	  fftw_real tmp372;
	  fftw_real tmp400;
	  fftw_real tmp259;
	  fftw_real tmp319;
	  fftw_real tmp42;
	  fftw_real tmp455;
	  fftw_real tmp201;
	  fftw_real tmp304;
	  fftw_real tmp390;
	  fftw_real tmp437;
	  fftw_real tmp196;
	  fftw_real tmp305;
	  fftw_real tmp184;
	  fftw_real tmp401;
	  fftw_real tmp375;
	  fftw_real tmp404;
	  fftw_real tmp270;
	  fftw_real tmp317;
	  fftw_real tmp279;
	  fftw_real tmp320;
	  fftw_real tmp66;
	  fftw_real tmp395;
	  fftw_real tmp224;
	  fftw_real tmp312;
	  fftw_real tmp357;
	  fftw_real tmp396;
	  fftw_real tmp219;
	  fftw_real tmp311;
	  fftw_real tmp114;
	  fftw_real tmp410;
	  fftw_real tmp249;
	  fftw_real tmp323;
	  fftw_real tmp363;
	  fftw_real tmp407;
	  fftw_real tmp232;
	  fftw_real tmp326;
	  fftw_real tmp89;
	  fftw_real tmp393;
	  fftw_real tmp213;
	  fftw_real tmp309;
	  fftw_real tmp354;
	  fftw_real tmp392;
	  fftw_real tmp208;
	  fftw_real tmp308;
	  fftw_real tmp137;
	  fftw_real tmp408;
	  fftw_real tmp366;
	  fftw_real tmp411;
	  fftw_real tmp243;
	  fftw_real tmp324;
	  fftw_real tmp252;
	  fftw_real tmp327;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp1;
	       fftw_real tmp440;
	       fftw_real tmp6;
	       fftw_real tmp439;
	       fftw_real tmp12;
	       fftw_real tmp188;
	       fftw_real tmp17;
	       fftw_real tmp189;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp1 = c_re(inout[0]);
	       tmp440 = c_im(inout[0]);
	       {
		    fftw_real tmp3;
		    fftw_real tmp5;
		    fftw_real tmp2;
		    fftw_real tmp4;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp3 = c_re(inout[16 * iostride]);
		    tmp5 = c_im(inout[16 * iostride]);
		    tmp2 = c_re(W[15]);
		    tmp4 = c_im(W[15]);
		    tmp6 = (tmp2 * tmp3) + (tmp4 * tmp5);
		    tmp439 = (tmp2 * tmp5) - (tmp4 * tmp3);
	       }
	       {
		    fftw_real tmp9;
		    fftw_real tmp11;
		    fftw_real tmp8;
		    fftw_real tmp10;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp9 = c_re(inout[8 * iostride]);
		    tmp11 = c_im(inout[8 * iostride]);
		    tmp8 = c_re(W[7]);
		    tmp10 = c_im(W[7]);
		    tmp12 = (tmp8 * tmp9) + (tmp10 * tmp11);
		    tmp188 = (tmp8 * tmp11) - (tmp10 * tmp9);
	       }
	       {
		    fftw_real tmp14;
		    fftw_real tmp16;
		    fftw_real tmp13;
		    fftw_real tmp15;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp14 = c_re(inout[24 * iostride]);
		    tmp16 = c_im(inout[24 * iostride]);
		    tmp13 = c_re(W[23]);
		    tmp15 = c_im(W[23]);
		    tmp17 = (tmp13 * tmp14) + (tmp15 * tmp16);
		    tmp189 = (tmp13 * tmp16) - (tmp15 * tmp14);
	       }
	       {
		    fftw_real tmp7;
		    fftw_real tmp18;
		    fftw_real tmp470;
		    fftw_real tmp471;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp7 = tmp1 + tmp6;
		    tmp18 = tmp12 + tmp17;
		    tmp19 = tmp7 + tmp18;
		    tmp387 = tmp7 - tmp18;
		    tmp470 = tmp12 - tmp17;
		    tmp471 = tmp440 - tmp439;
		    tmp472 = tmp470 + tmp471;
		    tmp486 = tmp471 - tmp470;
	       }
	       {
		    fftw_real tmp438;
		    fftw_real tmp441;
		    fftw_real tmp187;
		    fftw_real tmp190;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp438 = tmp188 + tmp189;
		    tmp441 = tmp439 + tmp440;
		    tmp442 = tmp438 + tmp441;
		    tmp456 = tmp441 - tmp438;
		    tmp187 = tmp1 - tmp6;
		    tmp190 = tmp188 - tmp189;
		    tmp191 = tmp187 - tmp190;
		    tmp303 = tmp187 + tmp190;
	       }
	  }
	  {
	       fftw_real tmp143;
	       fftw_real tmp272;
	       fftw_real tmp159;
	       fftw_real tmp257;
	       fftw_real tmp148;
	       fftw_real tmp273;
	       fftw_real tmp154;
	       fftw_real tmp256;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp140;
		    fftw_real tmp142;
		    fftw_real tmp139;
		    fftw_real tmp141;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp140 = c_re(inout[31 * iostride]);
		    tmp142 = c_im(inout[31 * iostride]);
		    tmp139 = c_re(W[30]);
		    tmp141 = c_im(W[30]);
		    tmp143 = (tmp139 * tmp140) + (tmp141 * tmp142);
		    tmp272 = (tmp139 * tmp142) - (tmp141 * tmp140);
	       }
	       {
		    fftw_real tmp156;
		    fftw_real tmp158;
		    fftw_real tmp155;
		    fftw_real tmp157;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp156 = c_re(inout[23 * iostride]);
		    tmp158 = c_im(inout[23 * iostride]);
		    tmp155 = c_re(W[22]);
		    tmp157 = c_im(W[22]);
		    tmp159 = (tmp155 * tmp156) + (tmp157 * tmp158);
		    tmp257 = (tmp155 * tmp158) - (tmp157 * tmp156);
	       }
	       {
		    fftw_real tmp145;
		    fftw_real tmp147;
		    fftw_real tmp144;
		    fftw_real tmp146;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp145 = c_re(inout[15 * iostride]);
		    tmp147 = c_im(inout[15 * iostride]);
		    tmp144 = c_re(W[14]);
		    tmp146 = c_im(W[14]);
		    tmp148 = (tmp144 * tmp145) + (tmp146 * tmp147);
		    tmp273 = (tmp144 * tmp147) - (tmp146 * tmp145);
	       }
	       {
		    fftw_real tmp151;
		    fftw_real tmp153;
		    fftw_real tmp150;
		    fftw_real tmp152;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp151 = c_re(inout[7 * iostride]);
		    tmp153 = c_im(inout[7 * iostride]);
		    tmp150 = c_re(W[6]);
		    tmp152 = c_im(W[6]);
		    tmp154 = (tmp150 * tmp151) + (tmp152 * tmp153);
		    tmp256 = (tmp150 * tmp153) - (tmp152 * tmp151);
	       }
	       {
		    fftw_real tmp149;
		    fftw_real tmp160;
		    fftw_real tmp274;
		    fftw_real tmp275;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp149 = tmp143 + tmp148;
		    tmp160 = tmp154 + tmp159;
		    tmp161 = tmp149 + tmp160;
		    tmp403 = tmp149 - tmp160;
		    tmp274 = tmp272 - tmp273;
		    tmp275 = tmp154 - tmp159;
		    tmp276 = tmp274 + tmp275;
		    tmp316 = tmp274 - tmp275;
	       }
	       {
		    fftw_real tmp370;
		    fftw_real tmp371;
		    fftw_real tmp255;
		    fftw_real tmp258;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp370 = tmp272 + tmp273;
		    tmp371 = tmp256 + tmp257;
		    tmp372 = tmp370 + tmp371;
		    tmp400 = tmp370 - tmp371;
		    tmp255 = tmp143 - tmp148;
		    tmp258 = tmp256 - tmp257;
		    tmp259 = tmp255 - tmp258;
		    tmp319 = tmp255 + tmp258;
	       }
	  }
	  {
	       fftw_real tmp24;
	       fftw_real tmp193;
	       fftw_real tmp40;
	       fftw_real tmp199;
	       fftw_real tmp29;
	       fftw_real tmp194;
	       fftw_real tmp35;
	       fftw_real tmp198;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp21;
		    fftw_real tmp23;
		    fftw_real tmp20;
		    fftw_real tmp22;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp21 = c_re(inout[4 * iostride]);
		    tmp23 = c_im(inout[4 * iostride]);
		    tmp20 = c_re(W[3]);
		    tmp22 = c_im(W[3]);
		    tmp24 = (tmp20 * tmp21) + (tmp22 * tmp23);
		    tmp193 = (tmp20 * tmp23) - (tmp22 * tmp21);
	       }
	       {
		    fftw_real tmp37;
		    fftw_real tmp39;
		    fftw_real tmp36;
		    fftw_real tmp38;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp37 = c_re(inout[12 * iostride]);
		    tmp39 = c_im(inout[12 * iostride]);
		    tmp36 = c_re(W[11]);
		    tmp38 = c_im(W[11]);
		    tmp40 = (tmp36 * tmp37) + (tmp38 * tmp39);
		    tmp199 = (tmp36 * tmp39) - (tmp38 * tmp37);
	       }
	       {
		    fftw_real tmp26;
		    fftw_real tmp28;
		    fftw_real tmp25;
		    fftw_real tmp27;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp26 = c_re(inout[20 * iostride]);
		    tmp28 = c_im(inout[20 * iostride]);
		    tmp25 = c_re(W[19]);
		    tmp27 = c_im(W[19]);
		    tmp29 = (tmp25 * tmp26) + (tmp27 * tmp28);
		    tmp194 = (tmp25 * tmp28) - (tmp27 * tmp26);
	       }
	       {
		    fftw_real tmp32;
		    fftw_real tmp34;
		    fftw_real tmp31;
		    fftw_real tmp33;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp32 = c_re(inout[28 * iostride]);
		    tmp34 = c_im(inout[28 * iostride]);
		    tmp31 = c_re(W[27]);
		    tmp33 = c_im(W[27]);
		    tmp35 = (tmp31 * tmp32) + (tmp33 * tmp34);
		    tmp198 = (tmp31 * tmp34) - (tmp33 * tmp32);
	       }
	       {
		    fftw_real tmp30;
		    fftw_real tmp41;
		    fftw_real tmp197;
		    fftw_real tmp200;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp30 = tmp24 + tmp29;
		    tmp41 = tmp35 + tmp40;
		    tmp42 = tmp30 + tmp41;
		    tmp455 = tmp30 - tmp41;
		    tmp197 = tmp35 - tmp40;
		    tmp200 = tmp198 - tmp199;
		    tmp201 = tmp197 + tmp200;
		    tmp304 = tmp200 - tmp197;
	       }
	       {
		    fftw_real tmp388;
		    fftw_real tmp389;
		    fftw_real tmp192;
		    fftw_real tmp195;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp388 = tmp198 + tmp199;
		    tmp389 = tmp193 + tmp194;
		    tmp390 = tmp388 - tmp389;
		    tmp437 = tmp389 + tmp388;
		    tmp192 = tmp24 - tmp29;
		    tmp195 = tmp193 - tmp194;
		    tmp196 = tmp192 - tmp195;
		    tmp305 = tmp192 + tmp195;
	       }
	  }
	  {
	       fftw_real tmp166;
	       fftw_real tmp261;
	       fftw_real tmp171;
	       fftw_real tmp262;
	       fftw_real tmp260;
	       fftw_real tmp263;
	       fftw_real tmp177;
	       fftw_real tmp266;
	       fftw_real tmp182;
	       fftw_real tmp267;
	       fftw_real tmp265;
	       fftw_real tmp268;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp163;
		    fftw_real tmp165;
		    fftw_real tmp162;
		    fftw_real tmp164;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp163 = c_re(inout[3 * iostride]);
		    tmp165 = c_im(inout[3 * iostride]);
		    tmp162 = c_re(W[2]);
		    tmp164 = c_im(W[2]);
		    tmp166 = (tmp162 * tmp163) + (tmp164 * tmp165);
		    tmp261 = (tmp162 * tmp165) - (tmp164 * tmp163);
	       }
	       {
		    fftw_real tmp168;
		    fftw_real tmp170;
		    fftw_real tmp167;
		    fftw_real tmp169;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp168 = c_re(inout[19 * iostride]);
		    tmp170 = c_im(inout[19 * iostride]);
		    tmp167 = c_re(W[18]);
		    tmp169 = c_im(W[18]);
		    tmp171 = (tmp167 * tmp168) + (tmp169 * tmp170);
		    tmp262 = (tmp167 * tmp170) - (tmp169 * tmp168);
	       }
	       tmp260 = tmp166 - tmp171;
	       tmp263 = tmp261 - tmp262;
	       {
		    fftw_real tmp174;
		    fftw_real tmp176;
		    fftw_real tmp173;
		    fftw_real tmp175;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp174 = c_re(inout[27 * iostride]);
		    tmp176 = c_im(inout[27 * iostride]);
		    tmp173 = c_re(W[26]);
		    tmp175 = c_im(W[26]);
		    tmp177 = (tmp173 * tmp174) + (tmp175 * tmp176);
		    tmp266 = (tmp173 * tmp176) - (tmp175 * tmp174);
	       }
	       {
		    fftw_real tmp179;
		    fftw_real tmp181;
		    fftw_real tmp178;
		    fftw_real tmp180;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp179 = c_re(inout[11 * iostride]);
		    tmp181 = c_im(inout[11 * iostride]);
		    tmp178 = c_re(W[10]);
		    tmp180 = c_im(W[10]);
		    tmp182 = (tmp178 * tmp179) + (tmp180 * tmp181);
		    tmp267 = (tmp178 * tmp181) - (tmp180 * tmp179);
	       }
	       tmp265 = tmp177 - tmp182;
	       tmp268 = tmp266 - tmp267;
	       {
		    fftw_real tmp172;
		    fftw_real tmp183;
		    fftw_real tmp373;
		    fftw_real tmp374;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp172 = tmp166 + tmp171;
		    tmp183 = tmp177 + tmp182;
		    tmp184 = tmp172 + tmp183;
		    tmp401 = tmp172 - tmp183;
		    tmp373 = tmp261 + tmp262;
		    tmp374 = tmp266 + tmp267;
		    tmp375 = tmp373 + tmp374;
		    tmp404 = tmp374 - tmp373;
	       }
	       {
		    fftw_real tmp264;
		    fftw_real tmp269;
		    fftw_real tmp277;
		    fftw_real tmp278;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp264 = tmp260 - tmp263;
		    tmp269 = tmp265 + tmp268;
		    tmp270 = K707106781 * (tmp264 + tmp269);
		    tmp317 = K707106781 * (tmp264 - tmp269);
		    tmp277 = tmp260 + tmp263;
		    tmp278 = tmp268 - tmp265;
		    tmp279 = K707106781 * (tmp277 + tmp278);
		    tmp320 = K707106781 * (tmp278 - tmp277);
	       }
	  }
	  {
	       fftw_real tmp48;
	       fftw_real tmp215;
	       fftw_real tmp64;
	       fftw_real tmp222;
	       fftw_real tmp53;
	       fftw_real tmp216;
	       fftw_real tmp59;
	       fftw_real tmp221;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp45;
		    fftw_real tmp47;
		    fftw_real tmp44;
		    fftw_real tmp46;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp45 = c_re(inout[2 * iostride]);
		    tmp47 = c_im(inout[2 * iostride]);
		    tmp44 = c_re(W[1]);
		    tmp46 = c_im(W[1]);
		    tmp48 = (tmp44 * tmp45) + (tmp46 * tmp47);
		    tmp215 = (tmp44 * tmp47) - (tmp46 * tmp45);
	       }
	       {
		    fftw_real tmp61;
		    fftw_real tmp63;
		    fftw_real tmp60;
		    fftw_real tmp62;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp61 = c_re(inout[26 * iostride]);
		    tmp63 = c_im(inout[26 * iostride]);
		    tmp60 = c_re(W[25]);
		    tmp62 = c_im(W[25]);
		    tmp64 = (tmp60 * tmp61) + (tmp62 * tmp63);
		    tmp222 = (tmp60 * tmp63) - (tmp62 * tmp61);
	       }
	       {
		    fftw_real tmp50;
		    fftw_real tmp52;
		    fftw_real tmp49;
		    fftw_real tmp51;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp50 = c_re(inout[18 * iostride]);
		    tmp52 = c_im(inout[18 * iostride]);
		    tmp49 = c_re(W[17]);
		    tmp51 = c_im(W[17]);
		    tmp53 = (tmp49 * tmp50) + (tmp51 * tmp52);
		    tmp216 = (tmp49 * tmp52) - (tmp51 * tmp50);
	       }
	       {
		    fftw_real tmp56;
		    fftw_real tmp58;
		    fftw_real tmp55;
		    fftw_real tmp57;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp56 = c_re(inout[10 * iostride]);
		    tmp58 = c_im(inout[10 * iostride]);
		    tmp55 = c_re(W[9]);
		    tmp57 = c_im(W[9]);
		    tmp59 = (tmp55 * tmp56) + (tmp57 * tmp58);
		    tmp221 = (tmp55 * tmp58) - (tmp57 * tmp56);
	       }
	       {
		    fftw_real tmp54;
		    fftw_real tmp65;
		    fftw_real tmp220;
		    fftw_real tmp223;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp54 = tmp48 + tmp53;
		    tmp65 = tmp59 + tmp64;
		    tmp66 = tmp54 + tmp65;
		    tmp395 = tmp54 - tmp65;
		    tmp220 = tmp48 - tmp53;
		    tmp223 = tmp221 - tmp222;
		    tmp224 = tmp220 - tmp223;
		    tmp312 = tmp220 + tmp223;
	       }
	       {
		    fftw_real tmp355;
		    fftw_real tmp356;
		    fftw_real tmp217;
		    fftw_real tmp218;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp355 = tmp215 + tmp216;
		    tmp356 = tmp221 + tmp222;
		    tmp357 = tmp355 + tmp356;
		    tmp396 = tmp355 - tmp356;
		    tmp217 = tmp215 - tmp216;
		    tmp218 = tmp59 - tmp64;
		    tmp219 = tmp217 + tmp218;
		    tmp311 = tmp217 - tmp218;
	       }
	  }
	  {
	       fftw_real tmp96;
	       fftw_real tmp245;
	       fftw_real tmp112;
	       fftw_real tmp230;
	       fftw_real tmp101;
	       fftw_real tmp246;
	       fftw_real tmp107;
	       fftw_real tmp229;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp93;
		    fftw_real tmp95;
		    fftw_real tmp92;
		    fftw_real tmp94;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp93 = c_re(inout[iostride]);
		    tmp95 = c_im(inout[iostride]);
		    tmp92 = c_re(W[0]);
		    tmp94 = c_im(W[0]);
		    tmp96 = (tmp92 * tmp93) + (tmp94 * tmp95);
		    tmp245 = (tmp92 * tmp95) - (tmp94 * tmp93);
	       }
	       {
		    fftw_real tmp109;
		    fftw_real tmp111;
		    fftw_real tmp108;
		    fftw_real tmp110;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp109 = c_re(inout[25 * iostride]);
		    tmp111 = c_im(inout[25 * iostride]);
		    tmp108 = c_re(W[24]);
		    tmp110 = c_im(W[24]);
		    tmp112 = (tmp108 * tmp109) + (tmp110 * tmp111);
		    tmp230 = (tmp108 * tmp111) - (tmp110 * tmp109);
	       }
	       {
		    fftw_real tmp98;
		    fftw_real tmp100;
		    fftw_real tmp97;
		    fftw_real tmp99;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp98 = c_re(inout[17 * iostride]);
		    tmp100 = c_im(inout[17 * iostride]);
		    tmp97 = c_re(W[16]);
		    tmp99 = c_im(W[16]);
		    tmp101 = (tmp97 * tmp98) + (tmp99 * tmp100);
		    tmp246 = (tmp97 * tmp100) - (tmp99 * tmp98);
	       }
	       {
		    fftw_real tmp104;
		    fftw_real tmp106;
		    fftw_real tmp103;
		    fftw_real tmp105;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp104 = c_re(inout[9 * iostride]);
		    tmp106 = c_im(inout[9 * iostride]);
		    tmp103 = c_re(W[8]);
		    tmp105 = c_im(W[8]);
		    tmp107 = (tmp103 * tmp104) + (tmp105 * tmp106);
		    tmp229 = (tmp103 * tmp106) - (tmp105 * tmp104);
	       }
	       {
		    fftw_real tmp102;
		    fftw_real tmp113;
		    fftw_real tmp247;
		    fftw_real tmp248;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp102 = tmp96 + tmp101;
		    tmp113 = tmp107 + tmp112;
		    tmp114 = tmp102 + tmp113;
		    tmp410 = tmp102 - tmp113;
		    tmp247 = tmp245 - tmp246;
		    tmp248 = tmp107 - tmp112;
		    tmp249 = tmp247 + tmp248;
		    tmp323 = tmp247 - tmp248;
	       }
	       {
		    fftw_real tmp361;
		    fftw_real tmp362;
		    fftw_real tmp228;
		    fftw_real tmp231;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp361 = tmp245 + tmp246;
		    tmp362 = tmp229 + tmp230;
		    tmp363 = tmp361 + tmp362;
		    tmp407 = tmp361 - tmp362;
		    tmp228 = tmp96 - tmp101;
		    tmp231 = tmp229 - tmp230;
		    tmp232 = tmp228 - tmp231;
		    tmp326 = tmp228 + tmp231;
	       }
	  }
	  {
	       fftw_real tmp71;
	       fftw_real tmp204;
	       fftw_real tmp87;
	       fftw_real tmp211;
	       fftw_real tmp76;
	       fftw_real tmp205;
	       fftw_real tmp82;
	       fftw_real tmp210;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp68;
		    fftw_real tmp70;
		    fftw_real tmp67;
		    fftw_real tmp69;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp68 = c_re(inout[30 * iostride]);
		    tmp70 = c_im(inout[30 * iostride]);
		    tmp67 = c_re(W[29]);
		    tmp69 = c_im(W[29]);
		    tmp71 = (tmp67 * tmp68) + (tmp69 * tmp70);
		    tmp204 = (tmp67 * tmp70) - (tmp69 * tmp68);
	       }
	       {
		    fftw_real tmp84;
		    fftw_real tmp86;
		    fftw_real tmp83;
		    fftw_real tmp85;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp84 = c_re(inout[22 * iostride]);
		    tmp86 = c_im(inout[22 * iostride]);
		    tmp83 = c_re(W[21]);
		    tmp85 = c_im(W[21]);
		    tmp87 = (tmp83 * tmp84) + (tmp85 * tmp86);
		    tmp211 = (tmp83 * tmp86) - (tmp85 * tmp84);
	       }
	       {
		    fftw_real tmp73;
		    fftw_real tmp75;
		    fftw_real tmp72;
		    fftw_real tmp74;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp73 = c_re(inout[14 * iostride]);
		    tmp75 = c_im(inout[14 * iostride]);
		    tmp72 = c_re(W[13]);
		    tmp74 = c_im(W[13]);
		    tmp76 = (tmp72 * tmp73) + (tmp74 * tmp75);
		    tmp205 = (tmp72 * tmp75) - (tmp74 * tmp73);
	       }
	       {
		    fftw_real tmp79;
		    fftw_real tmp81;
		    fftw_real tmp78;
		    fftw_real tmp80;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp79 = c_re(inout[6 * iostride]);
		    tmp81 = c_im(inout[6 * iostride]);
		    tmp78 = c_re(W[5]);
		    tmp80 = c_im(W[5]);
		    tmp82 = (tmp78 * tmp79) + (tmp80 * tmp81);
		    tmp210 = (tmp78 * tmp81) - (tmp80 * tmp79);
	       }
	       {
		    fftw_real tmp77;
		    fftw_real tmp88;
		    fftw_real tmp209;
		    fftw_real tmp212;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp77 = tmp71 + tmp76;
		    tmp88 = tmp82 + tmp87;
		    tmp89 = tmp77 + tmp88;
		    tmp393 = tmp77 - tmp88;
		    tmp209 = tmp71 - tmp76;
		    tmp212 = tmp210 - tmp211;
		    tmp213 = tmp209 - tmp212;
		    tmp309 = tmp209 + tmp212;
	       }
	       {
		    fftw_real tmp352;
		    fftw_real tmp353;
		    fftw_real tmp206;
		    fftw_real tmp207;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp352 = tmp204 + tmp205;
		    tmp353 = tmp210 + tmp211;
		    tmp354 = tmp352 + tmp353;
		    tmp392 = tmp352 - tmp353;
		    tmp206 = tmp204 - tmp205;
		    tmp207 = tmp82 - tmp87;
		    tmp208 = tmp206 + tmp207;
		    tmp308 = tmp206 - tmp207;
	       }
	  }
	  {
	       fftw_real tmp119;
	       fftw_real tmp234;
	       fftw_real tmp124;
	       fftw_real tmp235;
	       fftw_real tmp233;
	       fftw_real tmp236;
	       fftw_real tmp130;
	       fftw_real tmp239;
	       fftw_real tmp135;
	       fftw_real tmp240;
	       fftw_real tmp238;
	       fftw_real tmp241;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp116;
		    fftw_real tmp118;
		    fftw_real tmp115;
		    fftw_real tmp117;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp116 = c_re(inout[5 * iostride]);
		    tmp118 = c_im(inout[5 * iostride]);
		    tmp115 = c_re(W[4]);
		    tmp117 = c_im(W[4]);
		    tmp119 = (tmp115 * tmp116) + (tmp117 * tmp118);
		    tmp234 = (tmp115 * tmp118) - (tmp117 * tmp116);
	       }
	       {
		    fftw_real tmp121;
		    fftw_real tmp123;
		    fftw_real tmp120;
		    fftw_real tmp122;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp121 = c_re(inout[21 * iostride]);
		    tmp123 = c_im(inout[21 * iostride]);
		    tmp120 = c_re(W[20]);
		    tmp122 = c_im(W[20]);
		    tmp124 = (tmp120 * tmp121) + (tmp122 * tmp123);
		    tmp235 = (tmp120 * tmp123) - (tmp122 * tmp121);
	       }
	       tmp233 = tmp119 - tmp124;
	       tmp236 = tmp234 - tmp235;
	       {
		    fftw_real tmp127;
		    fftw_real tmp129;
		    fftw_real tmp126;
		    fftw_real tmp128;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp127 = c_re(inout[29 * iostride]);
		    tmp129 = c_im(inout[29 * iostride]);
		    tmp126 = c_re(W[28]);
		    tmp128 = c_im(W[28]);
		    tmp130 = (tmp126 * tmp127) + (tmp128 * tmp129);
		    tmp239 = (tmp126 * tmp129) - (tmp128 * tmp127);
	       }
	       {
		    fftw_real tmp132;
		    fftw_real tmp134;
		    fftw_real tmp131;
		    fftw_real tmp133;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp132 = c_re(inout[13 * iostride]);
		    tmp134 = c_im(inout[13 * iostride]);
		    tmp131 = c_re(W[12]);
		    tmp133 = c_im(W[12]);
		    tmp135 = (tmp131 * tmp132) + (tmp133 * tmp134);
		    tmp240 = (tmp131 * tmp134) - (tmp133 * tmp132);
	       }
	       tmp238 = tmp130 - tmp135;
	       tmp241 = tmp239 - tmp240;
	       {
		    fftw_real tmp125;
		    fftw_real tmp136;
		    fftw_real tmp364;
		    fftw_real tmp365;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp125 = tmp119 + tmp124;
		    tmp136 = tmp130 + tmp135;
		    tmp137 = tmp125 + tmp136;
		    tmp408 = tmp125 - tmp136;
		    tmp364 = tmp234 + tmp235;
		    tmp365 = tmp239 + tmp240;
		    tmp366 = tmp364 + tmp365;
		    tmp411 = tmp365 - tmp364;
	       }
	       {
		    fftw_real tmp237;
		    fftw_real tmp242;
		    fftw_real tmp250;
		    fftw_real tmp251;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp237 = tmp233 - tmp236;
		    tmp242 = tmp238 + tmp241;
		    tmp243 = K707106781 * (tmp237 + tmp242);
		    tmp324 = K707106781 * (tmp237 - tmp242);
		    tmp250 = tmp233 + tmp236;
		    tmp251 = tmp241 - tmp238;
		    tmp252 = K707106781 * (tmp250 + tmp251);
		    tmp327 = K707106781 * (tmp251 - tmp250);
	       }
	  }
	  {
	       fftw_real tmp91;
	       fftw_real tmp383;
	       fftw_real tmp444;
	       fftw_real tmp446;
	       fftw_real tmp186;
	       fftw_real tmp445;
	       fftw_real tmp386;
	       fftw_real tmp435;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp43;
		    fftw_real tmp90;
		    fftw_real tmp436;
		    fftw_real tmp443;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp43 = tmp19 + tmp42;
		    tmp90 = tmp66 + tmp89;
		    tmp91 = tmp43 + tmp90;
		    tmp383 = tmp43 - tmp90;
		    tmp436 = tmp357 + tmp354;
		    tmp443 = tmp437 + tmp442;
		    tmp444 = tmp436 + tmp443;
		    tmp446 = tmp443 - tmp436;
	       }
	       {
		    fftw_real tmp138;
		    fftw_real tmp185;
		    fftw_real tmp384;
		    fftw_real tmp385;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp138 = tmp114 + tmp137;
		    tmp185 = tmp161 + tmp184;
		    tmp186 = tmp138 + tmp185;
		    tmp445 = tmp138 - tmp185;
		    tmp384 = tmp372 + tmp375;
		    tmp385 = tmp363 + tmp366;
		    tmp386 = tmp384 - tmp385;
		    tmp435 = tmp385 + tmp384;
	       }
	       c_re(inout[16 * iostride]) = tmp91 - tmp186;
	       c_re(inout[0]) = tmp91 + tmp186;
	       c_re(inout[24 * iostride]) = tmp383 - tmp386;
	       c_re(inout[8 * iostride]) = tmp383 + tmp386;
	       c_im(inout[0]) = tmp435 + tmp444;
	       c_im(inout[16 * iostride]) = tmp444 - tmp435;
	       c_im(inout[8 * iostride]) = tmp445 + tmp446;
	       c_im(inout[24 * iostride]) = tmp446 - tmp445;
	  }
	  {
	       fftw_real tmp359;
	       fftw_real tmp379;
	       fftw_real tmp450;
	       fftw_real tmp452;
	       fftw_real tmp368;
	       fftw_real tmp381;
	       fftw_real tmp377;
	       fftw_real tmp380;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp351;
		    fftw_real tmp358;
		    fftw_real tmp448;
		    fftw_real tmp449;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp351 = tmp19 - tmp42;
		    tmp358 = tmp354 - tmp357;
		    tmp359 = tmp351 + tmp358;
		    tmp379 = tmp351 - tmp358;
		    tmp448 = tmp66 - tmp89;
		    tmp449 = tmp442 - tmp437;
		    tmp450 = tmp448 + tmp449;
		    tmp452 = tmp449 - tmp448;
	       }
	       {
		    fftw_real tmp360;
		    fftw_real tmp367;
		    fftw_real tmp369;
		    fftw_real tmp376;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp360 = tmp114 - tmp137;
		    tmp367 = tmp363 - tmp366;
		    tmp368 = tmp360 - tmp367;
		    tmp381 = tmp360 + tmp367;
		    tmp369 = tmp161 - tmp184;
		    tmp376 = tmp372 - tmp375;
		    tmp377 = tmp369 + tmp376;
		    tmp380 = tmp376 - tmp369;
	       }
	       {
		    fftw_real tmp378;
		    fftw_real tmp451;
		    fftw_real tmp382;
		    fftw_real tmp447;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp378 = K707106781 * (tmp368 + tmp377);
		    c_re(inout[20 * iostride]) = tmp359 - tmp378;
		    c_re(inout[4 * iostride]) = tmp359 + tmp378;
		    tmp451 = K707106781 * (tmp368 - tmp377);
		    c_im(inout[12 * iostride]) = tmp451 + tmp452;
		    c_im(inout[28 * iostride]) = tmp452 - tmp451;
		    tmp382 = K707106781 * (tmp380 - tmp381);
		    c_re(inout[28 * iostride]) = tmp379 - tmp382;
		    c_re(inout[12 * iostride]) = tmp379 + tmp382;
		    tmp447 = K707106781 * (tmp381 + tmp380);
		    c_im(inout[4 * iostride]) = tmp447 + tmp450;
		    c_im(inout[20 * iostride]) = tmp450 - tmp447;
	       }
	  }
	  {
	       fftw_real tmp391;
	       fftw_real tmp419;
	       fftw_real tmp398;
	       fftw_real tmp454;
	       fftw_real tmp422;
	       fftw_real tmp462;
	       fftw_real tmp406;
	       fftw_real tmp417;
	       fftw_real tmp457;
	       fftw_real tmp463;
	       fftw_real tmp426;
	       fftw_real tmp433;
	       fftw_real tmp413;
	       fftw_real tmp416;
	       fftw_real tmp429;
	       fftw_real tmp432;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp394;
		    fftw_real tmp397;
		    fftw_real tmp424;
		    fftw_real tmp425;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp391 = tmp387 - tmp390;
		    tmp419 = tmp387 + tmp390;
		    tmp394 = tmp392 - tmp393;
		    tmp397 = tmp395 + tmp396;
		    tmp398 = K707106781 * (tmp394 - tmp397);
		    tmp454 = K707106781 * (tmp397 + tmp394);
		    {
			 fftw_real tmp420;
			 fftw_real tmp421;
			 fftw_real tmp402;
			 fftw_real tmp405;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp420 = tmp395 - tmp396;
			 tmp421 = tmp393 + tmp392;
			 tmp422 = K707106781 * (tmp420 + tmp421);
			 tmp462 = K707106781 * (tmp420 - tmp421);
			 tmp402 = tmp400 - tmp401;
			 tmp405 = tmp403 - tmp404;
			 tmp406 = (K382683432 * tmp402) - (K923879532 * tmp405);
			 tmp417 = (K923879532 * tmp402) + (K382683432 * tmp405);
		    }
		    tmp457 = tmp455 + tmp456;
		    tmp463 = tmp456 - tmp455;
		    tmp424 = tmp400 + tmp401;
		    tmp425 = tmp403 + tmp404;
		    tmp426 = (K923879532 * tmp424) - (K382683432 * tmp425);
		    tmp433 = (K382683432 * tmp424) + (K923879532 * tmp425);
		    {
			 fftw_real tmp409;
			 fftw_real tmp412;
			 fftw_real tmp427;
			 fftw_real tmp428;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp409 = tmp407 - tmp408;
			 tmp412 = tmp410 - tmp411;
			 tmp413 = (K382683432 * tmp409) + (K923879532 * tmp412);
			 tmp416 = (K382683432 * tmp412) - (K923879532 * tmp409);
			 tmp427 = tmp407 + tmp408;
			 tmp428 = tmp410 + tmp411;
			 tmp429 = (K923879532 * tmp427) + (K382683432 * tmp428);
			 tmp432 = (K923879532 * tmp428) - (K382683432 * tmp427);
		    }
	       }
	       {
		    fftw_real tmp399;
		    fftw_real tmp414;
		    fftw_real tmp415;
		    fftw_real tmp418;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp399 = tmp391 - tmp398;
		    tmp414 = tmp406 - tmp413;
		    c_re(inout[30 * iostride]) = tmp399 - tmp414;
		    c_re(inout[14 * iostride]) = tmp399 + tmp414;
		    tmp415 = tmp391 + tmp398;
		    tmp418 = tmp416 + tmp417;
		    c_re(inout[22 * iostride]) = tmp415 - tmp418;
		    c_re(inout[6 * iostride]) = tmp415 + tmp418;
	       }
	       {
		    fftw_real tmp465;
		    fftw_real tmp466;
		    fftw_real tmp461;
		    fftw_real tmp464;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp465 = tmp416 - tmp417;
		    tmp466 = tmp463 - tmp462;
		    c_im(inout[14 * iostride]) = tmp465 + tmp466;
		    c_im(inout[30 * iostride]) = tmp466 - tmp465;
		    tmp461 = tmp413 + tmp406;
		    tmp464 = tmp462 + tmp463;
		    c_im(inout[6 * iostride]) = tmp461 + tmp464;
		    c_im(inout[22 * iostride]) = tmp464 - tmp461;
	       }
	       {
		    fftw_real tmp423;
		    fftw_real tmp430;
		    fftw_real tmp431;
		    fftw_real tmp434;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp423 = tmp419 - tmp422;
		    tmp430 = tmp426 - tmp429;
		    c_re(inout[26 * iostride]) = tmp423 - tmp430;
		    c_re(inout[10 * iostride]) = tmp423 + tmp430;
		    tmp431 = tmp419 + tmp422;
		    tmp434 = tmp432 + tmp433;
		    c_re(inout[18 * iostride]) = tmp431 - tmp434;
		    c_re(inout[2 * iostride]) = tmp431 + tmp434;
	       }
	       {
		    fftw_real tmp459;
		    fftw_real tmp460;
		    fftw_real tmp453;
		    fftw_real tmp458;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp459 = tmp432 - tmp433;
		    tmp460 = tmp457 - tmp454;
		    c_im(inout[10 * iostride]) = tmp459 + tmp460;
		    c_im(inout[26 * iostride]) = tmp460 - tmp459;
		    tmp453 = tmp429 + tmp426;
		    tmp458 = tmp454 + tmp457;
		    c_im(inout[2 * iostride]) = tmp453 + tmp458;
		    c_im(inout[18 * iostride]) = tmp458 - tmp453;
	       }
	  }
	  {
	       fftw_real tmp307;
	       fftw_real tmp335;
	       fftw_real tmp338;
	       fftw_real tmp492;
	       fftw_real tmp487;
	       fftw_real tmp493;
	       fftw_real tmp314;
	       fftw_real tmp484;
	       fftw_real tmp322;
	       fftw_real tmp333;
	       fftw_real tmp342;
	       fftw_real tmp349;
	       fftw_real tmp329;
	       fftw_real tmp332;
	       fftw_real tmp345;
	       fftw_real tmp348;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp306;
		    fftw_real tmp336;
		    fftw_real tmp337;
		    fftw_real tmp485;
		    fftw_real tmp310;
		    fftw_real tmp313;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp306 = K707106781 * (tmp304 - tmp305);
		    tmp307 = tmp303 - tmp306;
		    tmp335 = tmp303 + tmp306;
		    tmp336 = (K382683432 * tmp312) - (K923879532 * tmp311);
		    tmp337 = (K923879532 * tmp308) + (K382683432 * tmp309);
		    tmp338 = tmp336 + tmp337;
		    tmp492 = tmp336 - tmp337;
		    tmp485 = K707106781 * (tmp196 - tmp201);
		    tmp487 = tmp485 + tmp486;
		    tmp493 = tmp486 - tmp485;
		    tmp310 = (K382683432 * tmp308) - (K923879532 * tmp309);
		    tmp313 = (K382683432 * tmp311) + (K923879532 * tmp312);
		    tmp314 = tmp310 - tmp313;
		    tmp484 = tmp313 + tmp310;
	       }
	       {
		    fftw_real tmp318;
		    fftw_real tmp321;
		    fftw_real tmp340;
		    fftw_real tmp341;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp318 = tmp316 - tmp317;
		    tmp321 = tmp319 - tmp320;
		    tmp322 = (K195090322 * tmp318) - (K980785280 * tmp321);
		    tmp333 = (K980785280 * tmp318) + (K195090322 * tmp321);
		    tmp340 = tmp316 + tmp317;
		    tmp341 = tmp319 + tmp320;
		    tmp342 = (K831469612 * tmp340) - (K555570233 * tmp341);
		    tmp349 = (K555570233 * tmp340) + (K831469612 * tmp341);
	       }
	       {
		    fftw_real tmp325;
		    fftw_real tmp328;
		    fftw_real tmp343;
		    fftw_real tmp344;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp325 = tmp323 - tmp324;
		    tmp328 = tmp326 - tmp327;
		    tmp329 = (K195090322 * tmp325) + (K980785280 * tmp328);
		    tmp332 = (K195090322 * tmp328) - (K980785280 * tmp325);
		    tmp343 = tmp323 + tmp324;
		    tmp344 = tmp326 + tmp327;
		    tmp345 = (K831469612 * tmp343) + (K555570233 * tmp344);
		    tmp348 = (K831469612 * tmp344) - (K555570233 * tmp343);
	       }
	       {
		    fftw_real tmp315;
		    fftw_real tmp330;
		    fftw_real tmp331;
		    fftw_real tmp334;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp315 = tmp307 - tmp314;
		    tmp330 = tmp322 - tmp329;
		    c_re(inout[31 * iostride]) = tmp315 - tmp330;
		    c_re(inout[15 * iostride]) = tmp315 + tmp330;
		    tmp331 = tmp307 + tmp314;
		    tmp334 = tmp332 + tmp333;
		    c_re(inout[23 * iostride]) = tmp331 - tmp334;
		    c_re(inout[7 * iostride]) = tmp331 + tmp334;
	       }
	       {
		    fftw_real tmp495;
		    fftw_real tmp496;
		    fftw_real tmp491;
		    fftw_real tmp494;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp495 = tmp332 - tmp333;
		    tmp496 = tmp493 - tmp492;
		    c_im(inout[15 * iostride]) = tmp495 + tmp496;
		    c_im(inout[31 * iostride]) = tmp496 - tmp495;
		    tmp491 = tmp329 + tmp322;
		    tmp494 = tmp492 + tmp493;
		    c_im(inout[7 * iostride]) = tmp491 + tmp494;
		    c_im(inout[23 * iostride]) = tmp494 - tmp491;
	       }
	       {
		    fftw_real tmp339;
		    fftw_real tmp346;
		    fftw_real tmp347;
		    fftw_real tmp350;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp339 = tmp335 - tmp338;
		    tmp346 = tmp342 - tmp345;
		    c_re(inout[27 * iostride]) = tmp339 - tmp346;
		    c_re(inout[11 * iostride]) = tmp339 + tmp346;
		    tmp347 = tmp335 + tmp338;
		    tmp350 = tmp348 + tmp349;
		    c_re(inout[19 * iostride]) = tmp347 - tmp350;
		    c_re(inout[3 * iostride]) = tmp347 + tmp350;
	       }
	       {
		    fftw_real tmp489;
		    fftw_real tmp490;
		    fftw_real tmp483;
		    fftw_real tmp488;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp489 = tmp348 - tmp349;
		    tmp490 = tmp487 - tmp484;
		    c_im(inout[11 * iostride]) = tmp489 + tmp490;
		    c_im(inout[27 * iostride]) = tmp490 - tmp489;
		    tmp483 = tmp345 + tmp342;
		    tmp488 = tmp484 + tmp487;
		    c_im(inout[3 * iostride]) = tmp483 + tmp488;
		    c_im(inout[19 * iostride]) = tmp488 - tmp483;
	       }
	  }
	  {
	       fftw_real tmp203;
	       fftw_real tmp287;
	       fftw_real tmp290;
	       fftw_real tmp478;
	       fftw_real tmp473;
	       fftw_real tmp479;
	       fftw_real tmp226;
	       fftw_real tmp468;
	       fftw_real tmp254;
	       fftw_real tmp285;
	       fftw_real tmp294;
	       fftw_real tmp301;
	       fftw_real tmp281;
	       fftw_real tmp284;
	       fftw_real tmp297;
	       fftw_real tmp300;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp202;
		    fftw_real tmp288;
		    fftw_real tmp289;
		    fftw_real tmp469;
		    fftw_real tmp214;
		    fftw_real tmp225;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp202 = K707106781 * (tmp196 + tmp201);
		    tmp203 = tmp191 - tmp202;
		    tmp287 = tmp191 + tmp202;
		    tmp288 = (K923879532 * tmp224) - (K382683432 * tmp219);
		    tmp289 = (K382683432 * tmp208) + (K923879532 * tmp213);
		    tmp290 = tmp288 + tmp289;
		    tmp478 = tmp288 - tmp289;
		    tmp469 = K707106781 * (tmp305 + tmp304);
		    tmp473 = tmp469 + tmp472;
		    tmp479 = tmp472 - tmp469;
		    tmp214 = (K923879532 * tmp208) - (K382683432 * tmp213);
		    tmp225 = (K923879532 * tmp219) + (K382683432 * tmp224);
		    tmp226 = tmp214 - tmp225;
		    tmp468 = tmp225 + tmp214;
	       }
	       {
		    fftw_real tmp244;
		    fftw_real tmp253;
		    fftw_real tmp292;
		    fftw_real tmp293;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp244 = tmp232 - tmp243;
		    tmp253 = tmp249 - tmp252;
		    tmp254 = (K555570233 * tmp244) - (K831469612 * tmp253);
		    tmp285 = (K831469612 * tmp244) + (K555570233 * tmp253);
		    tmp292 = tmp232 + tmp243;
		    tmp293 = tmp249 + tmp252;
		    tmp294 = (K980785280 * tmp292) - (K195090322 * tmp293);
		    tmp301 = (K195090322 * tmp292) + (K980785280 * tmp293);
	       }
	       {
		    fftw_real tmp271;
		    fftw_real tmp280;
		    fftw_real tmp295;
		    fftw_real tmp296;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp271 = tmp259 - tmp270;
		    tmp280 = tmp276 - tmp279;
		    tmp281 = (K555570233 * tmp271) + (K831469612 * tmp280);
		    tmp284 = (K555570233 * tmp280) - (K831469612 * tmp271);
		    tmp295 = tmp259 + tmp270;
		    tmp296 = tmp276 + tmp279;
		    tmp297 = (K980785280 * tmp295) + (K195090322 * tmp296);
		    tmp300 = (K980785280 * tmp296) - (K195090322 * tmp295);
	       }
	       {
		    fftw_real tmp227;
		    fftw_real tmp282;
		    fftw_real tmp283;
		    fftw_real tmp286;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp227 = tmp203 + tmp226;
		    tmp282 = tmp254 + tmp281;
		    c_re(inout[21 * iostride]) = tmp227 - tmp282;
		    c_re(inout[5 * iostride]) = tmp227 + tmp282;
		    tmp283 = tmp203 - tmp226;
		    tmp286 = tmp284 - tmp285;
		    c_re(inout[29 * iostride]) = tmp283 - tmp286;
		    c_re(inout[13 * iostride]) = tmp283 + tmp286;
	       }
	       {
		    fftw_real tmp477;
		    fftw_real tmp480;
		    fftw_real tmp481;
		    fftw_real tmp482;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp477 = tmp285 + tmp284;
		    tmp480 = tmp478 + tmp479;
		    c_im(inout[5 * iostride]) = tmp477 + tmp480;
		    c_im(inout[21 * iostride]) = tmp480 - tmp477;
		    tmp481 = tmp254 - tmp281;
		    tmp482 = tmp479 - tmp478;
		    c_im(inout[13 * iostride]) = tmp481 + tmp482;
		    c_im(inout[29 * iostride]) = tmp482 - tmp481;
	       }
	       {
		    fftw_real tmp291;
		    fftw_real tmp298;
		    fftw_real tmp299;
		    fftw_real tmp302;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp291 = tmp287 + tmp290;
		    tmp298 = tmp294 + tmp297;
		    c_re(inout[17 * iostride]) = tmp291 - tmp298;
		    c_re(inout[iostride]) = tmp291 + tmp298;
		    tmp299 = tmp287 - tmp290;
		    tmp302 = tmp300 - tmp301;
		    c_re(inout[25 * iostride]) = tmp299 - tmp302;
		    c_re(inout[9 * iostride]) = tmp299 + tmp302;
	       }
	       {
		    fftw_real tmp467;
		    fftw_real tmp474;
		    fftw_real tmp475;
		    fftw_real tmp476;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp467 = tmp301 + tmp300;
		    tmp474 = tmp468 + tmp473;
		    c_im(inout[iostride]) = tmp467 + tmp474;
		    c_im(inout[17 * iostride]) = tmp474 - tmp467;
		    tmp475 = tmp294 - tmp297;
		    tmp476 = tmp473 - tmp468;
		    c_im(inout[9 * iostride]) = tmp475 + tmp476;
		    c_im(inout[25 * iostride]) = tmp476 - tmp475;
	       }
	  }
     }
}

static const int twiddle_order[] =
{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31};
fftw_codelet_desc fftwi_twiddle_32_desc =
{
     "fftwi_twiddle_32",
     (void (*)()) fftwi_twiddle_32,
     32,
     FFTW_BACKWARD,
     FFTW_TWIDDLE,
     715,
     31,
     twiddle_order,
};
