/*
 * Copyright (c) 1997-1999 Massachusetts Institute of Technology
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 *
 */

/* This file was automatically generated --- DO NOT EDIT */
/* Generated on Sun Nov  7 20:45:18 EST 1999 */

#include "fftw-int.h"
#include "fftw.h"

/* Generated by: ./genfft -magic-alignment-check -magic-twiddle-load-all -magic-variables 4 -magic-loopi -hc2hc-backward 32 */

/*
 * This function contains 764 FP additions, 346 FP multiplications,
 * (or, 618 additions, 200 multiplications, 146 fused multiply/add),
 * 93 stack variables, and 256 memory accesses
 */
static const fftw_real K1_662939224 = FFTW_KONST(+1.662939224605090474157576755235811513477121624);
static const fftw_real K1_111140466 = FFTW_KONST(+1.111140466039204449485661627897065748749874382);
static const fftw_real K390180644 = FFTW_KONST(+0.390180644032256535696569736954044481855383236);
static const fftw_real K1_961570560 = FFTW_KONST(+1.961570560806460898252364472268478073947867462);
static const fftw_real K1_913880671 = FFTW_KONST(+1.913880671464417729871595773960539938965698411);
static const fftw_real K580569354 = FFTW_KONST(+0.580569354508924735272384751634790549382952557);
static const fftw_real K942793473 = FFTW_KONST(+0.942793473651995297112775251810508755314920638);
static const fftw_real K1_763842528 = FFTW_KONST(+1.763842528696710059425513727320776699016885241);
static const fftw_real K1_546020906 = FFTW_KONST(+1.546020906725473921621813219516939601942082586);
static const fftw_real K1_268786568 = FFTW_KONST(+1.268786568327290996430343226450986741351374190);
static const fftw_real K196034280 = FFTW_KONST(+0.196034280659121203988391127777283691722273346);
static const fftw_real K1_990369453 = FFTW_KONST(+1.990369453344393772489673906218959843150949737);
static const fftw_real K382683432 = FFTW_KONST(+0.382683432365089771728459984030398866761344562);
static const fftw_real K923879532 = FFTW_KONST(+0.923879532511286756128183189396788286822416626);
static const fftw_real K707106781 = FFTW_KONST(+0.707106781186547524400844362104849039284835938);
static const fftw_real K765366864 = FFTW_KONST(+0.765366864730179543456919968060797733522689125);
static const fftw_real K1_847759065 = FFTW_KONST(+1.847759065022573512256366378793576573644833252);
static const fftw_real K555570233 = FFTW_KONST(+0.555570233019602224742830813948532874374937191);
static const fftw_real K831469612 = FFTW_KONST(+0.831469612302545237078788377617905756738560812);
static const fftw_real K195090322 = FFTW_KONST(+0.195090322016128267848284868477022240927691618);
static const fftw_real K980785280 = FFTW_KONST(+0.980785280403230449126182236134239036973933731);
static const fftw_real K1_414213562 = FFTW_KONST(+1.414213562373095048801688724209698078569671875);
static const fftw_real K2_000000000 = FFTW_KONST(+2.000000000000000000000000000000000000000000000);

/*
 * Generator Id's : 
 * $Id: exprdag.ml,v 1.41 1999/05/26 15:44:14 fftw Exp $
 * $Id: fft.ml,v 1.43 1999/05/17 19:44:18 fftw Exp $
 * $Id: to_c.ml,v 1.25 1999/10/26 21:41:32 stevenj Exp $
 */

void fftw_hc2hc_backward_32(fftw_real *A, const fftw_complex *W, int iostride, int m, int dist)
{
     int i;
     fftw_real *X;
     fftw_real *Y;
     X = A;
     Y = A + (32 * iostride);
     {
	  fftw_real tmp679;
	  fftw_real tmp804;
	  fftw_real tmp766;
	  fftw_real tmp707;
	  fftw_real tmp702;
	  fftw_real tmp728;
	  fftw_real tmp726;
	  fftw_real tmp753;
	  fftw_real tmp815;
	  fftw_real tmp819;
	  fftw_real tmp789;
	  fftw_real tmp793;
	  fftw_real tmp676;
	  fftw_real tmp704;
	  fftw_real tmp763;
	  fftw_real tmp803;
	  fftw_real tmp687;
	  fftw_real tmp709;
	  fftw_real tmp716;
	  fftw_real tmp749;
	  fftw_real tmp770;
	  fftw_real tmp806;
	  fftw_real tmp773;
	  fftw_real tmp807;
	  fftw_real tmp695;
	  fftw_real tmp719;
	  fftw_real tmp735;
	  fftw_real tmp752;
	  fftw_real tmp812;
	  fftw_real tmp818;
	  fftw_real tmp782;
	  fftw_real tmp792;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp677;
	       fftw_real tmp678;
	       fftw_real tmp764;
	       fftw_real tmp705;
	       fftw_real tmp706;
	       fftw_real tmp765;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp677 = X[4 * iostride];
	       tmp678 = X[12 * iostride];
	       tmp764 = tmp677 - tmp678;
	       tmp705 = Y[-4 * iostride];
	       tmp706 = Y[-12 * iostride];
	       tmp765 = tmp706 + tmp705;
	       tmp679 = K2_000000000 * (tmp677 + tmp678);
	       tmp804 = K1_414213562 * (tmp764 + tmp765);
	       tmp766 = K1_414213562 * (tmp764 - tmp765);
	       tmp707 = K2_000000000 * (tmp705 - tmp706);
	  }
	  {
	       fftw_real tmp698;
	       fftw_real tmp783;
	       fftw_real tmp725;
	       fftw_real tmp787;
	       fftw_real tmp701;
	       fftw_real tmp786;
	       fftw_real tmp722;
	       fftw_real tmp784;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp696;
		    fftw_real tmp697;
		    fftw_real tmp723;
		    fftw_real tmp724;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp696 = X[3 * iostride];
		    tmp697 = X[13 * iostride];
		    tmp698 = tmp696 + tmp697;
		    tmp783 = tmp696 - tmp697;
		    tmp723 = Y[-3 * iostride];
		    tmp724 = Y[-13 * iostride];
		    tmp725 = tmp723 - tmp724;
		    tmp787 = tmp723 + tmp724;
	       }
	       {
		    fftw_real tmp699;
		    fftw_real tmp700;
		    fftw_real tmp720;
		    fftw_real tmp721;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp699 = X[5 * iostride];
		    tmp700 = X[11 * iostride];
		    tmp701 = tmp699 + tmp700;
		    tmp786 = tmp700 - tmp699;
		    tmp720 = Y[-5 * iostride];
		    tmp721 = Y[-11 * iostride];
		    tmp722 = tmp720 - tmp721;
		    tmp784 = tmp721 + tmp720;
	       }
	       tmp702 = tmp698 + tmp701;
	       tmp728 = tmp701 - tmp698;
	       tmp726 = tmp722 + tmp725;
	       tmp753 = tmp725 - tmp722;
	       {
		    fftw_real tmp813;
		    fftw_real tmp814;
		    fftw_real tmp785;
		    fftw_real tmp788;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp813 = tmp783 + tmp784;
		    tmp814 = tmp787 - tmp786;
		    tmp815 = (K980785280 * tmp813) - (K195090322 * tmp814);
		    tmp819 = (K195090322 * tmp813) + (K980785280 * tmp814);
		    tmp785 = tmp783 - tmp784;
		    tmp788 = tmp786 + tmp787;
		    tmp789 = (K831469612 * tmp785) - (K555570233 * tmp788);
		    tmp793 = (K555570233 * tmp785) + (K831469612 * tmp788);
	       }
	  }
	  {
	       fftw_real tmp675;
	       fftw_real tmp762;
	       fftw_real tmp673;
	       fftw_real tmp760;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp674;
		    fftw_real tmp761;
		    fftw_real tmp671;
		    fftw_real tmp672;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp674 = X[8 * iostride];
		    tmp675 = K2_000000000 * tmp674;
		    tmp761 = Y[-8 * iostride];
		    tmp762 = K2_000000000 * tmp761;
		    tmp671 = X[0];
		    tmp672 = X[16 * iostride];
		    tmp673 = tmp671 + tmp672;
		    tmp760 = tmp671 - tmp672;
	       }
	       tmp676 = tmp673 + tmp675;
	       tmp704 = tmp673 - tmp675;
	       tmp763 = tmp760 - tmp762;
	       tmp803 = tmp760 + tmp762;
	  }
	  {
	       fftw_real tmp683;
	       fftw_real tmp768;
	       fftw_real tmp715;
	       fftw_real tmp772;
	       fftw_real tmp686;
	       fftw_real tmp771;
	       fftw_real tmp712;
	       fftw_real tmp769;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp681;
		    fftw_real tmp682;
		    fftw_real tmp713;
		    fftw_real tmp714;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp681 = X[2 * iostride];
		    tmp682 = X[14 * iostride];
		    tmp683 = tmp681 + tmp682;
		    tmp768 = tmp681 - tmp682;
		    tmp713 = Y[-2 * iostride];
		    tmp714 = Y[-14 * iostride];
		    tmp715 = tmp713 - tmp714;
		    tmp772 = tmp713 + tmp714;
	       }
	       {
		    fftw_real tmp684;
		    fftw_real tmp685;
		    fftw_real tmp710;
		    fftw_real tmp711;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp684 = X[6 * iostride];
		    tmp685 = X[10 * iostride];
		    tmp686 = tmp684 + tmp685;
		    tmp771 = tmp685 - tmp684;
		    tmp710 = Y[-6 * iostride];
		    tmp711 = Y[-10 * iostride];
		    tmp712 = tmp710 - tmp711;
		    tmp769 = tmp711 + tmp710;
	       }
	       tmp687 = K2_000000000 * (tmp683 + tmp686);
	       tmp709 = tmp683 - tmp686;
	       tmp716 = tmp712 + tmp715;
	       tmp749 = K2_000000000 * (tmp715 - tmp712);
	       tmp770 = tmp768 - tmp769;
	       tmp806 = tmp768 + tmp769;
	       tmp773 = tmp771 + tmp772;
	       tmp807 = tmp772 - tmp771;
	  }
	  {
	       fftw_real tmp691;
	       fftw_real tmp776;
	       fftw_real tmp734;
	       fftw_real tmp780;
	       fftw_real tmp694;
	       fftw_real tmp779;
	       fftw_real tmp731;
	       fftw_real tmp777;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp689;
		    fftw_real tmp690;
		    fftw_real tmp732;
		    fftw_real tmp733;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp689 = X[iostride];
		    tmp690 = X[15 * iostride];
		    tmp691 = tmp689 + tmp690;
		    tmp776 = tmp689 - tmp690;
		    tmp732 = Y[-iostride];
		    tmp733 = Y[-15 * iostride];
		    tmp734 = tmp732 - tmp733;
		    tmp780 = tmp732 + tmp733;
	       }
	       {
		    fftw_real tmp692;
		    fftw_real tmp693;
		    fftw_real tmp729;
		    fftw_real tmp730;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp692 = X[7 * iostride];
		    tmp693 = X[9 * iostride];
		    tmp694 = tmp692 + tmp693;
		    tmp779 = tmp693 - tmp692;
		    tmp729 = Y[-7 * iostride];
		    tmp730 = Y[-9 * iostride];
		    tmp731 = tmp729 - tmp730;
		    tmp777 = tmp730 + tmp729;
	       }
	       tmp695 = tmp691 + tmp694;
	       tmp719 = tmp691 - tmp694;
	       tmp735 = tmp731 + tmp734;
	       tmp752 = tmp734 - tmp731;
	       {
		    fftw_real tmp810;
		    fftw_real tmp811;
		    fftw_real tmp778;
		    fftw_real tmp781;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp810 = tmp776 + tmp777;
		    tmp811 = tmp780 - tmp779;
		    tmp812 = (K555570233 * tmp810) + (K831469612 * tmp811);
		    tmp818 = (K831469612 * tmp810) - (K555570233 * tmp811);
		    tmp778 = tmp776 - tmp777;
		    tmp781 = tmp779 + tmp780;
		    tmp782 = (K980785280 * tmp778) - (K195090322 * tmp781);
		    tmp792 = (K195090322 * tmp778) + (K980785280 * tmp781);
	       }
	  }
	  {
	       fftw_real tmp703;
	       fftw_real tmp759;
	       fftw_real tmp688;
	       fftw_real tmp758;
	       fftw_real tmp680;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp703 = K2_000000000 * (tmp695 + tmp702);
	       tmp759 = K2_000000000 * (tmp752 - tmp753);
	       tmp680 = tmp676 + tmp679;
	       tmp688 = tmp680 + tmp687;
	       tmp758 = tmp680 - tmp687;
	       X[16 * iostride] = tmp688 - tmp703;
	       X[0] = tmp688 + tmp703;
	       X[24 * iostride] = tmp758 + tmp759;
	       X[8 * iostride] = tmp758 - tmp759;
	  }
	  {
	       fftw_real tmp750;
	       fftw_real tmp756;
	       fftw_real tmp755;
	       fftw_real tmp757;
	       fftw_real tmp748;
	       fftw_real tmp751;
	       fftw_real tmp754;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp748 = tmp676 - tmp679;
	       tmp750 = tmp748 - tmp749;
	       tmp756 = tmp748 + tmp749;
	       tmp751 = tmp695 - tmp702;
	       tmp754 = tmp752 + tmp753;
	       tmp755 = K1_414213562 * (tmp751 - tmp754);
	       tmp757 = K1_414213562 * (tmp751 + tmp754);
	       X[20 * iostride] = tmp750 - tmp755;
	       X[4 * iostride] = tmp750 + tmp755;
	       X[12 * iostride] = tmp756 - tmp757;
	       X[28 * iostride] = tmp756 + tmp757;
	  }
	  {
	       fftw_real tmp718;
	       fftw_real tmp738;
	       fftw_real tmp737;
	       fftw_real tmp739;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp708;
		    fftw_real tmp717;
		    fftw_real tmp727;
		    fftw_real tmp736;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp708 = tmp704 - tmp707;
		    tmp717 = K1_414213562 * (tmp709 - tmp716);
		    tmp718 = tmp708 + tmp717;
		    tmp738 = tmp708 - tmp717;
		    tmp727 = tmp719 - tmp726;
		    tmp736 = tmp728 + tmp735;
		    tmp737 = (K1_847759065 * tmp727) - (K765366864 * tmp736);
		    tmp739 = (K1_847759065 * tmp736) + (K765366864 * tmp727);
	       }
	       X[18 * iostride] = tmp718 - tmp737;
	       X[2 * iostride] = tmp718 + tmp737;
	       X[10 * iostride] = tmp738 - tmp739;
	       X[26 * iostride] = tmp738 + tmp739;
	  }
	  {
	       fftw_real tmp742;
	       fftw_real tmp746;
	       fftw_real tmp745;
	       fftw_real tmp747;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp740;
		    fftw_real tmp741;
		    fftw_real tmp743;
		    fftw_real tmp744;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp740 = tmp704 + tmp707;
		    tmp741 = K1_414213562 * (tmp709 + tmp716);
		    tmp742 = tmp740 - tmp741;
		    tmp746 = tmp740 + tmp741;
		    tmp743 = tmp719 + tmp726;
		    tmp744 = tmp735 - tmp728;
		    tmp745 = (K765366864 * tmp743) - (K1_847759065 * tmp744);
		    tmp747 = (K765366864 * tmp744) + (K1_847759065 * tmp743);
	       }
	       X[22 * iostride] = tmp742 - tmp745;
	       X[6 * iostride] = tmp742 + tmp745;
	       X[14 * iostride] = tmp746 - tmp747;
	       X[30 * iostride] = tmp746 + tmp747;
	  }
	  {
	       fftw_real tmp790;
	       fftw_real tmp794;
	       fftw_real tmp775;
	       fftw_real tmp791;
	       fftw_real tmp767;
	       fftw_real tmp774;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp790 = K2_000000000 * (tmp782 + tmp789);
	       tmp794 = K2_000000000 * (tmp792 - tmp793);
	       tmp767 = tmp763 + tmp766;
	       tmp774 = (K1_847759065 * tmp770) - (K765366864 * tmp773);
	       tmp775 = tmp767 + tmp774;
	       tmp791 = tmp767 - tmp774;
	       X[17 * iostride] = tmp775 - tmp790;
	       X[iostride] = tmp775 + tmp790;
	       X[9 * iostride] = tmp791 - tmp794;
	       X[25 * iostride] = tmp791 + tmp794;
	  }
	  {
	       fftw_real tmp797;
	       fftw_real tmp801;
	       fftw_real tmp800;
	       fftw_real tmp802;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp795;
		    fftw_real tmp796;
		    fftw_real tmp798;
		    fftw_real tmp799;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp795 = tmp763 - tmp766;
		    tmp796 = (K765366864 * tmp770) + (K1_847759065 * tmp773);
		    tmp797 = tmp795 - tmp796;
		    tmp801 = tmp795 + tmp796;
		    tmp798 = tmp782 - tmp789;
		    tmp799 = tmp792 + tmp793;
		    tmp800 = K1_414213562 * (tmp798 - tmp799);
		    tmp802 = K1_414213562 * (tmp798 + tmp799);
	       }
	       X[21 * iostride] = tmp797 - tmp800;
	       X[5 * iostride] = tmp797 + tmp800;
	       X[13 * iostride] = tmp801 - tmp802;
	       X[29 * iostride] = tmp801 + tmp802;
	  }
	  {
	       fftw_real tmp816;
	       fftw_real tmp820;
	       fftw_real tmp809;
	       fftw_real tmp817;
	       fftw_real tmp805;
	       fftw_real tmp808;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp816 = K2_000000000 * (tmp812 - tmp815);
	       tmp820 = K2_000000000 * (tmp818 - tmp819);
	       tmp805 = tmp803 - tmp804;
	       tmp808 = (K765366864 * tmp806) - (K1_847759065 * tmp807);
	       tmp809 = tmp805 - tmp808;
	       tmp817 = tmp805 + tmp808;
	       X[11 * iostride] = tmp809 - tmp816;
	       X[27 * iostride] = tmp809 + tmp816;
	       X[19 * iostride] = tmp817 - tmp820;
	       X[3 * iostride] = tmp817 + tmp820;
	  }
	  {
	       fftw_real tmp823;
	       fftw_real tmp827;
	       fftw_real tmp826;
	       fftw_real tmp828;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp821;
		    fftw_real tmp822;
		    fftw_real tmp824;
		    fftw_real tmp825;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp821 = tmp803 + tmp804;
		    tmp822 = (K1_847759065 * tmp806) + (K765366864 * tmp807);
		    tmp823 = tmp821 - tmp822;
		    tmp827 = tmp821 + tmp822;
		    tmp824 = tmp818 + tmp819;
		    tmp825 = tmp812 + tmp815;
		    tmp826 = K1_414213562 * (tmp824 - tmp825);
		    tmp828 = K1_414213562 * (tmp825 + tmp824);
	       }
	       X[23 * iostride] = tmp823 - tmp826;
	       X[7 * iostride] = tmp823 + tmp826;
	       X[15 * iostride] = tmp827 - tmp828;
	       X[31 * iostride] = tmp827 + tmp828;
	  }
     }
     X = X + dist;
     Y = Y - dist;
     for (i = 2; i < m; i = i + 2, X = X + dist, Y = Y - dist, W = W + 31) {
	  fftw_real tmp446;
	  fftw_real tmp580;
	  fftw_real tmp189;
	  fftw_real tmp238;
	  fftw_real tmp382;
	  fftw_real tmp420;
	  fftw_real tmp307;
	  fftw_real tmp332;
	  fftw_real tmp514;
	  fftw_real tmp608;
	  fftw_real tmp354;
	  fftw_real tmp408;
	  fftw_real tmp453;
	  fftw_real tmp609;
	  fftw_real tmp517;
	  fftw_real tmp581;
	  fftw_real tmp235;
	  fftw_real tmp272;
	  fftw_real tmp365;
	  fftw_real tmp411;
	  fftw_real tmp287;
	  fftw_real tmp336;
	  fftw_real tmp368;
	  fftw_real tmp412;
	  fftw_real tmp500;
	  fftw_real tmp550;
	  fftw_real tmp593;
	  fftw_real tmp643;
	  fftw_real tmp507;
	  fftw_real tmp551;
	  fftw_real tmp596;
	  fftw_real tmp642;
	  fftw_real tmp204;
	  fftw_real tmp292;
	  fftw_real tmp357;
	  fftw_real tmp384;
	  fftw_real tmp253;
	  fftw_real tmp333;
	  fftw_real tmp360;
	  fftw_real tmp383;
	  fftw_real tmp461;
	  fftw_real tmp520;
	  fftw_real tmp588;
	  fftw_real tmp612;
	  fftw_real tmp468;
	  fftw_real tmp519;
	  fftw_real tmp585;
	  fftw_real tmp611;
	  fftw_real tmp220;
	  fftw_real tmp255;
	  fftw_real tmp372;
	  fftw_real tmp414;
	  fftw_real tmp270;
	  fftw_real tmp335;
	  fftw_real tmp375;
	  fftw_real tmp415;
	  fftw_real tmp481;
	  fftw_real tmp547;
	  fftw_real tmp600;
	  fftw_real tmp640;
	  fftw_real tmp488;
	  fftw_real tmp548;
	  fftw_real tmp603;
	  fftw_real tmp639;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp177;
	       fftw_real tmp444;
	       fftw_real tmp295;
	       fftw_real tmp513;
	       fftw_real tmp180;
	       fftw_real tmp512;
	       fftw_real tmp298;
	       fftw_real tmp445;
	       fftw_real tmp184;
	       fftw_real tmp447;
	       fftw_real tmp302;
	       fftw_real tmp448;
	       fftw_real tmp187;
	       fftw_real tmp450;
	       fftw_real tmp305;
	       fftw_real tmp451;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp175;
		    fftw_real tmp176;
		    fftw_real tmp293;
		    fftw_real tmp294;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp175 = X[0];
		    tmp176 = Y[-16 * iostride];
		    tmp177 = tmp175 + tmp176;
		    tmp444 = tmp175 - tmp176;
		    tmp293 = Y[0];
		    tmp294 = X[16 * iostride];
		    tmp295 = tmp293 - tmp294;
		    tmp513 = tmp293 + tmp294;
	       }
	       {
		    fftw_real tmp178;
		    fftw_real tmp179;
		    fftw_real tmp296;
		    fftw_real tmp297;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp178 = X[8 * iostride];
		    tmp179 = Y[-24 * iostride];
		    tmp180 = tmp178 + tmp179;
		    tmp512 = tmp178 - tmp179;
		    tmp296 = Y[-8 * iostride];
		    tmp297 = X[24 * iostride];
		    tmp298 = tmp296 - tmp297;
		    tmp445 = tmp296 + tmp297;
	       }
	       {
		    fftw_real tmp182;
		    fftw_real tmp183;
		    fftw_real tmp300;
		    fftw_real tmp301;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp182 = X[4 * iostride];
		    tmp183 = Y[-20 * iostride];
		    tmp184 = tmp182 + tmp183;
		    tmp447 = tmp182 - tmp183;
		    tmp300 = Y[-4 * iostride];
		    tmp301 = X[20 * iostride];
		    tmp302 = tmp300 - tmp301;
		    tmp448 = tmp300 + tmp301;
	       }
	       {
		    fftw_real tmp185;
		    fftw_real tmp186;
		    fftw_real tmp303;
		    fftw_real tmp304;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp185 = Y[-28 * iostride];
		    tmp186 = X[12 * iostride];
		    tmp187 = tmp185 + tmp186;
		    tmp450 = tmp185 - tmp186;
		    tmp303 = Y[-12 * iostride];
		    tmp304 = X[28 * iostride];
		    tmp305 = tmp303 - tmp304;
		    tmp451 = tmp303 + tmp304;
	       }
	       {
		    fftw_real tmp181;
		    fftw_real tmp188;
		    fftw_real tmp352;
		    fftw_real tmp353;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp446 = tmp444 - tmp445;
		    tmp580 = tmp444 + tmp445;
		    tmp181 = tmp177 + tmp180;
		    tmp188 = tmp184 + tmp187;
		    tmp189 = tmp181 + tmp188;
		    tmp238 = tmp181 - tmp188;
		    {
			 fftw_real tmp380;
			 fftw_real tmp381;
			 fftw_real tmp299;
			 fftw_real tmp306;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp380 = tmp295 - tmp298;
			 tmp381 = tmp184 - tmp187;
			 tmp382 = tmp380 - tmp381;
			 tmp420 = tmp381 + tmp380;
			 tmp299 = tmp295 + tmp298;
			 tmp306 = tmp302 + tmp305;
			 tmp307 = tmp299 - tmp306;
			 tmp332 = tmp299 + tmp306;
		    }
		    tmp514 = tmp512 + tmp513;
		    tmp608 = tmp513 - tmp512;
		    tmp352 = tmp177 - tmp180;
		    tmp353 = tmp305 - tmp302;
		    tmp354 = tmp352 - tmp353;
		    tmp408 = tmp352 + tmp353;
		    {
			 fftw_real tmp449;
			 fftw_real tmp452;
			 fftw_real tmp515;
			 fftw_real tmp516;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp449 = tmp447 - tmp448;
			 tmp452 = tmp450 - tmp451;
			 tmp453 = K707106781 * (tmp449 + tmp452);
			 tmp609 = K707106781 * (tmp449 - tmp452);
			 tmp515 = tmp447 + tmp448;
			 tmp516 = tmp450 + tmp451;
			 tmp517 = K707106781 * (tmp515 - tmp516);
			 tmp581 = K707106781 * (tmp515 + tmp516);
		    }
	       }
	  }
	  {
	       fftw_real tmp223;
	       fftw_real tmp490;
	       fftw_real tmp275;
	       fftw_real tmp502;
	       fftw_real tmp226;
	       fftw_real tmp501;
	       fftw_real tmp278;
	       fftw_real tmp491;
	       fftw_real tmp233;
	       fftw_real tmp505;
	       fftw_real tmp498;
	       fftw_real tmp285;
	       fftw_real tmp230;
	       fftw_real tmp504;
	       fftw_real tmp495;
	       fftw_real tmp282;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp221;
		    fftw_real tmp222;
		    fftw_real tmp276;
		    fftw_real tmp277;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp221 = Y[-31 * iostride];
		    tmp222 = X[15 * iostride];
		    tmp223 = tmp221 + tmp222;
		    tmp490 = tmp221 - tmp222;
		    {
			 fftw_real tmp273;
			 fftw_real tmp274;
			 fftw_real tmp224;
			 fftw_real tmp225;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp273 = Y[-15 * iostride];
			 tmp274 = X[31 * iostride];
			 tmp275 = tmp273 - tmp274;
			 tmp502 = tmp273 + tmp274;
			 tmp224 = X[7 * iostride];
			 tmp225 = Y[-23 * iostride];
			 tmp226 = tmp224 + tmp225;
			 tmp501 = tmp224 - tmp225;
		    }
		    tmp276 = Y[-7 * iostride];
		    tmp277 = X[23 * iostride];
		    tmp278 = tmp276 - tmp277;
		    tmp491 = tmp276 + tmp277;
		    {
			 fftw_real tmp231;
			 fftw_real tmp232;
			 fftw_real tmp496;
			 fftw_real tmp283;
			 fftw_real tmp284;
			 fftw_real tmp497;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp231 = Y[-27 * iostride];
			 tmp232 = X[11 * iostride];
			 tmp496 = tmp231 - tmp232;
			 tmp283 = Y[-11 * iostride];
			 tmp284 = X[27 * iostride];
			 tmp497 = tmp283 + tmp284;
			 tmp233 = tmp231 + tmp232;
			 tmp505 = tmp496 + tmp497;
			 tmp498 = tmp496 - tmp497;
			 tmp285 = tmp283 - tmp284;
		    }
		    {
			 fftw_real tmp228;
			 fftw_real tmp229;
			 fftw_real tmp493;
			 fftw_real tmp280;
			 fftw_real tmp281;
			 fftw_real tmp494;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp228 = X[3 * iostride];
			 tmp229 = Y[-19 * iostride];
			 tmp493 = tmp228 - tmp229;
			 tmp280 = Y[-3 * iostride];
			 tmp281 = X[19 * iostride];
			 tmp494 = tmp280 + tmp281;
			 tmp230 = tmp228 + tmp229;
			 tmp504 = tmp493 + tmp494;
			 tmp495 = tmp493 - tmp494;
			 tmp282 = tmp280 - tmp281;
		    }
	       }
	       {
		    fftw_real tmp227;
		    fftw_real tmp234;
		    fftw_real tmp363;
		    fftw_real tmp364;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp227 = tmp223 + tmp226;
		    tmp234 = tmp230 + tmp233;
		    tmp235 = tmp227 + tmp234;
		    tmp272 = tmp227 - tmp234;
		    tmp363 = tmp275 - tmp278;
		    tmp364 = tmp230 - tmp233;
		    tmp365 = tmp363 - tmp364;
		    tmp411 = tmp364 + tmp363;
	       }
	       {
		    fftw_real tmp279;
		    fftw_real tmp286;
		    fftw_real tmp366;
		    fftw_real tmp367;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp279 = tmp275 + tmp278;
		    tmp286 = tmp282 + tmp285;
		    tmp287 = tmp279 - tmp286;
		    tmp336 = tmp279 + tmp286;
		    tmp366 = tmp223 - tmp226;
		    tmp367 = tmp285 - tmp282;
		    tmp368 = tmp366 - tmp367;
		    tmp412 = tmp366 + tmp367;
	       }
	       {
		    fftw_real tmp492;
		    fftw_real tmp499;
		    fftw_real tmp591;
		    fftw_real tmp592;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp492 = tmp490 - tmp491;
		    tmp499 = K707106781 * (tmp495 + tmp498);
		    tmp500 = tmp492 - tmp499;
		    tmp550 = tmp492 + tmp499;
		    tmp591 = K707106781 * (tmp495 - tmp498);
		    tmp592 = tmp501 + tmp502;
		    tmp593 = tmp591 - tmp592;
		    tmp643 = tmp592 + tmp591;
	       }
	       {
		    fftw_real tmp503;
		    fftw_real tmp506;
		    fftw_real tmp594;
		    fftw_real tmp595;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp503 = tmp501 - tmp502;
		    tmp506 = K707106781 * (tmp504 - tmp505);
		    tmp507 = tmp503 - tmp506;
		    tmp551 = tmp503 + tmp506;
		    tmp594 = tmp490 + tmp491;
		    tmp595 = K707106781 * (tmp504 + tmp505);
		    tmp596 = tmp594 - tmp595;
		    tmp642 = tmp594 + tmp595;
	       }
	  }
	  {
	       fftw_real tmp192;
	       fftw_real tmp465;
	       fftw_real tmp248;
	       fftw_real tmp463;
	       fftw_real tmp195;
	       fftw_real tmp462;
	       fftw_real tmp251;
	       fftw_real tmp466;
	       fftw_real tmp199;
	       fftw_real tmp458;
	       fftw_real tmp241;
	       fftw_real tmp456;
	       fftw_real tmp202;
	       fftw_real tmp455;
	       fftw_real tmp244;
	       fftw_real tmp459;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp190;
		    fftw_real tmp191;
		    fftw_real tmp246;
		    fftw_real tmp247;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp190 = X[2 * iostride];
		    tmp191 = Y[-18 * iostride];
		    tmp192 = tmp190 + tmp191;
		    tmp465 = tmp190 - tmp191;
		    tmp246 = Y[-2 * iostride];
		    tmp247 = X[18 * iostride];
		    tmp248 = tmp246 - tmp247;
		    tmp463 = tmp246 + tmp247;
	       }
	       {
		    fftw_real tmp193;
		    fftw_real tmp194;
		    fftw_real tmp249;
		    fftw_real tmp250;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp193 = X[10 * iostride];
		    tmp194 = Y[-26 * iostride];
		    tmp195 = tmp193 + tmp194;
		    tmp462 = tmp193 - tmp194;
		    tmp249 = Y[-10 * iostride];
		    tmp250 = X[26 * iostride];
		    tmp251 = tmp249 - tmp250;
		    tmp466 = tmp249 + tmp250;
	       }
	       {
		    fftw_real tmp197;
		    fftw_real tmp198;
		    fftw_real tmp239;
		    fftw_real tmp240;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp197 = Y[-30 * iostride];
		    tmp198 = X[14 * iostride];
		    tmp199 = tmp197 + tmp198;
		    tmp458 = tmp197 - tmp198;
		    tmp239 = Y[-14 * iostride];
		    tmp240 = X[30 * iostride];
		    tmp241 = tmp239 - tmp240;
		    tmp456 = tmp239 + tmp240;
	       }
	       {
		    fftw_real tmp200;
		    fftw_real tmp201;
		    fftw_real tmp242;
		    fftw_real tmp243;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp200 = X[6 * iostride];
		    tmp201 = Y[-22 * iostride];
		    tmp202 = tmp200 + tmp201;
		    tmp455 = tmp200 - tmp201;
		    tmp242 = Y[-6 * iostride];
		    tmp243 = X[22 * iostride];
		    tmp244 = tmp242 - tmp243;
		    tmp459 = tmp242 + tmp243;
	       }
	       {
		    fftw_real tmp196;
		    fftw_real tmp203;
		    fftw_real tmp355;
		    fftw_real tmp356;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp196 = tmp192 + tmp195;
		    tmp203 = tmp199 + tmp202;
		    tmp204 = tmp196 + tmp203;
		    tmp292 = tmp196 - tmp203;
		    tmp355 = tmp241 - tmp244;
		    tmp356 = tmp199 - tmp202;
		    tmp357 = tmp355 - tmp356;
		    tmp384 = tmp356 + tmp355;
	       }
	       {
		    fftw_real tmp245;
		    fftw_real tmp252;
		    fftw_real tmp358;
		    fftw_real tmp359;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp245 = tmp241 + tmp244;
		    tmp252 = tmp248 + tmp251;
		    tmp253 = tmp245 - tmp252;
		    tmp333 = tmp252 + tmp245;
		    tmp358 = tmp192 - tmp195;
		    tmp359 = tmp248 - tmp251;
		    tmp360 = tmp358 + tmp359;
		    tmp383 = tmp358 - tmp359;
	       }
	       {
		    fftw_real tmp457;
		    fftw_real tmp460;
		    fftw_real tmp586;
		    fftw_real tmp587;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp457 = tmp455 - tmp456;
		    tmp460 = tmp458 - tmp459;
		    tmp461 = (K923879532 * tmp457) - (K382683432 * tmp460);
		    tmp520 = (K382683432 * tmp457) + (K923879532 * tmp460);
		    tmp586 = tmp458 + tmp459;
		    tmp587 = tmp455 + tmp456;
		    tmp588 = (K382683432 * tmp586) - (K923879532 * tmp587);
		    tmp612 = (K382683432 * tmp587) + (K923879532 * tmp586);
	       }
	       {
		    fftw_real tmp464;
		    fftw_real tmp467;
		    fftw_real tmp583;
		    fftw_real tmp584;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp464 = tmp462 + tmp463;
		    tmp467 = tmp465 - tmp466;
		    tmp468 = (K923879532 * tmp464) + (K382683432 * tmp467);
		    tmp519 = (K923879532 * tmp467) - (K382683432 * tmp464);
		    tmp583 = tmp465 + tmp466;
		    tmp584 = tmp463 - tmp462;
		    tmp585 = (K382683432 * tmp583) - (K923879532 * tmp584);
		    tmp611 = (K382683432 * tmp584) + (K923879532 * tmp583);
	       }
	  }
	  {
	       fftw_real tmp208;
	       fftw_real tmp471;
	       fftw_real tmp258;
	       fftw_real tmp483;
	       fftw_real tmp211;
	       fftw_real tmp482;
	       fftw_real tmp261;
	       fftw_real tmp472;
	       fftw_real tmp218;
	       fftw_real tmp486;
	       fftw_real tmp479;
	       fftw_real tmp268;
	       fftw_real tmp215;
	       fftw_real tmp485;
	       fftw_real tmp476;
	       fftw_real tmp265;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp206;
		    fftw_real tmp207;
		    fftw_real tmp259;
		    fftw_real tmp260;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp206 = X[iostride];
		    tmp207 = Y[-17 * iostride];
		    tmp208 = tmp206 + tmp207;
		    tmp471 = tmp206 - tmp207;
		    {
			 fftw_real tmp256;
			 fftw_real tmp257;
			 fftw_real tmp209;
			 fftw_real tmp210;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp256 = Y[-iostride];
			 tmp257 = X[17 * iostride];
			 tmp258 = tmp256 - tmp257;
			 tmp483 = tmp256 + tmp257;
			 tmp209 = X[9 * iostride];
			 tmp210 = Y[-25 * iostride];
			 tmp211 = tmp209 + tmp210;
			 tmp482 = tmp209 - tmp210;
		    }
		    tmp259 = Y[-9 * iostride];
		    tmp260 = X[25 * iostride];
		    tmp261 = tmp259 - tmp260;
		    tmp472 = tmp259 + tmp260;
		    {
			 fftw_real tmp216;
			 fftw_real tmp217;
			 fftw_real tmp477;
			 fftw_real tmp266;
			 fftw_real tmp267;
			 fftw_real tmp478;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp216 = Y[-29 * iostride];
			 tmp217 = X[13 * iostride];
			 tmp477 = tmp216 - tmp217;
			 tmp266 = Y[-13 * iostride];
			 tmp267 = X[29 * iostride];
			 tmp478 = tmp266 + tmp267;
			 tmp218 = tmp216 + tmp217;
			 tmp486 = tmp477 + tmp478;
			 tmp479 = tmp477 - tmp478;
			 tmp268 = tmp266 - tmp267;
		    }
		    {
			 fftw_real tmp213;
			 fftw_real tmp214;
			 fftw_real tmp474;
			 fftw_real tmp263;
			 fftw_real tmp264;
			 fftw_real tmp475;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp213 = X[5 * iostride];
			 tmp214 = Y[-21 * iostride];
			 tmp474 = tmp213 - tmp214;
			 tmp263 = Y[-5 * iostride];
			 tmp264 = X[21 * iostride];
			 tmp475 = tmp263 + tmp264;
			 tmp215 = tmp213 + tmp214;
			 tmp485 = tmp474 + tmp475;
			 tmp476 = tmp474 - tmp475;
			 tmp265 = tmp263 - tmp264;
		    }
	       }
	       {
		    fftw_real tmp212;
		    fftw_real tmp219;
		    fftw_real tmp370;
		    fftw_real tmp371;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp212 = tmp208 + tmp211;
		    tmp219 = tmp215 + tmp218;
		    tmp220 = tmp212 + tmp219;
		    tmp255 = tmp212 - tmp219;
		    tmp370 = tmp258 - tmp261;
		    tmp371 = tmp215 - tmp218;
		    tmp372 = tmp370 - tmp371;
		    tmp414 = tmp371 + tmp370;
	       }
	       {
		    fftw_real tmp262;
		    fftw_real tmp269;
		    fftw_real tmp373;
		    fftw_real tmp374;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp262 = tmp258 + tmp261;
		    tmp269 = tmp265 + tmp268;
		    tmp270 = tmp262 - tmp269;
		    tmp335 = tmp262 + tmp269;
		    tmp373 = tmp208 - tmp211;
		    tmp374 = tmp268 - tmp265;
		    tmp375 = tmp373 - tmp374;
		    tmp415 = tmp373 + tmp374;
	       }
	       {
		    fftw_real tmp473;
		    fftw_real tmp480;
		    fftw_real tmp598;
		    fftw_real tmp599;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp473 = tmp471 - tmp472;
		    tmp480 = K707106781 * (tmp476 + tmp479);
		    tmp481 = tmp473 - tmp480;
		    tmp547 = tmp473 + tmp480;
		    tmp598 = tmp483 - tmp482;
		    tmp599 = K707106781 * (tmp476 - tmp479);
		    tmp600 = tmp598 + tmp599;
		    tmp640 = tmp598 - tmp599;
	       }
	       {
		    fftw_real tmp484;
		    fftw_real tmp487;
		    fftw_real tmp601;
		    fftw_real tmp602;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp484 = tmp482 + tmp483;
		    tmp487 = K707106781 * (tmp485 - tmp486);
		    tmp488 = tmp484 - tmp487;
		    tmp548 = tmp484 + tmp487;
		    tmp601 = tmp471 + tmp472;
		    tmp602 = K707106781 * (tmp485 + tmp486);
		    tmp603 = tmp601 - tmp602;
		    tmp639 = tmp601 + tmp602;
	       }
	  }
	  {
	       fftw_real tmp205;
	       fftw_real tmp236;
	       fftw_real tmp330;
	       fftw_real tmp334;
	       fftw_real tmp337;
	       fftw_real tmp338;
	       fftw_real tmp329;
	       fftw_real tmp331;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp205 = tmp189 + tmp204;
	       tmp236 = tmp220 + tmp235;
	       tmp330 = tmp205 - tmp236;
	       tmp334 = tmp332 + tmp333;
	       tmp337 = tmp335 + tmp336;
	       tmp338 = tmp334 - tmp337;
	       X[0] = tmp205 + tmp236;
	       Y[-31 * iostride] = tmp334 + tmp337;
	       tmp329 = c_re(W[15]);
	       tmp331 = c_im(W[15]);
	       X[16 * iostride] = (tmp329 * tmp330) + (tmp331 * tmp338);
	       Y[-15 * iostride] = (tmp329 * tmp338) - (tmp331 * tmp330);
	  }
	  {
	       fftw_real tmp342;
	       fftw_real tmp348;
	       fftw_real tmp346;
	       fftw_real tmp350;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp340;
		    fftw_real tmp341;
		    fftw_real tmp344;
		    fftw_real tmp345;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp340 = tmp332 - tmp333;
		    tmp341 = tmp220 - tmp235;
		    tmp342 = tmp340 - tmp341;
		    tmp348 = tmp341 + tmp340;
		    tmp344 = tmp189 - tmp204;
		    tmp345 = tmp336 - tmp335;
		    tmp346 = tmp344 - tmp345;
		    tmp350 = tmp344 + tmp345;
	       }
	       {
		    fftw_real tmp339;
		    fftw_real tmp343;
		    fftw_real tmp347;
		    fftw_real tmp349;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp339 = c_re(W[23]);
		    tmp343 = c_im(W[23]);
		    Y[-7 * iostride] = (tmp339 * tmp342) - (tmp343 * tmp346);
		    X[24 * iostride] = (tmp343 * tmp342) + (tmp339 * tmp346);
		    tmp347 = c_re(W[7]);
		    tmp349 = c_im(W[7]);
		    Y[-23 * iostride] = (tmp347 * tmp348) - (tmp349 * tmp350);
		    X[8 * iostride] = (tmp349 * tmp348) + (tmp347 * tmp350);
	       }
	  }
	  {
	       fftw_real tmp254;
	       fftw_real tmp322;
	       fftw_real tmp289;
	       fftw_real tmp319;
	       fftw_real tmp308;
	       fftw_real tmp318;
	       fftw_real tmp311;
	       fftw_real tmp323;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp271;
		    fftw_real tmp288;
		    fftw_real tmp309;
		    fftw_real tmp310;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp254 = tmp238 + tmp253;
		    tmp322 = tmp238 - tmp253;
		    tmp271 = tmp255 - tmp270;
		    tmp288 = tmp272 + tmp287;
		    tmp289 = K707106781 * (tmp271 + tmp288);
		    tmp319 = K707106781 * (tmp271 - tmp288);
		    tmp308 = tmp292 + tmp307;
		    tmp318 = tmp307 - tmp292;
		    tmp309 = tmp255 + tmp270;
		    tmp310 = tmp287 - tmp272;
		    tmp311 = K707106781 * (tmp309 + tmp310);
		    tmp323 = K707106781 * (tmp310 - tmp309);
	       }
	       {
		    fftw_real tmp290;
		    fftw_real tmp312;
		    fftw_real tmp237;
		    fftw_real tmp291;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp290 = tmp254 - tmp289;
		    tmp312 = tmp308 - tmp311;
		    tmp237 = c_re(W[19]);
		    tmp291 = c_im(W[19]);
		    X[20 * iostride] = (tmp237 * tmp290) + (tmp291 * tmp312);
		    Y[-11 * iostride] = (tmp237 * tmp312) - (tmp291 * tmp290);
	       }
	       {
		    fftw_real tmp314;
		    fftw_real tmp316;
		    fftw_real tmp313;
		    fftw_real tmp315;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp314 = tmp254 + tmp289;
		    tmp316 = tmp308 + tmp311;
		    tmp313 = c_re(W[3]);
		    tmp315 = c_im(W[3]);
		    X[4 * iostride] = (tmp313 * tmp314) + (tmp315 * tmp316);
		    Y[-27 * iostride] = (tmp313 * tmp316) - (tmp315 * tmp314);
	       }
	       {
		    fftw_real tmp320;
		    fftw_real tmp324;
		    fftw_real tmp317;
		    fftw_real tmp321;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp320 = tmp318 - tmp319;
		    tmp324 = tmp322 - tmp323;
		    tmp317 = c_re(W[27]);
		    tmp321 = c_im(W[27]);
		    Y[-3 * iostride] = (tmp317 * tmp320) - (tmp321 * tmp324);
		    X[28 * iostride] = (tmp321 * tmp320) + (tmp317 * tmp324);
	       }
	       {
		    fftw_real tmp326;
		    fftw_real tmp328;
		    fftw_real tmp325;
		    fftw_real tmp327;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp326 = tmp318 + tmp319;
		    tmp328 = tmp322 + tmp323;
		    tmp325 = c_re(W[11]);
		    tmp327 = c_im(W[11]);
		    Y[-19 * iostride] = (tmp325 * tmp326) - (tmp327 * tmp328);
		    X[12 * iostride] = (tmp327 * tmp326) + (tmp325 * tmp328);
	       }
	  }
	  {
	       fftw_real tmp638;
	       fftw_real tmp664;
	       fftw_real tmp653;
	       fftw_real tmp665;
	       fftw_real tmp645;
	       fftw_real tmp661;
	       fftw_real tmp650;
	       fftw_real tmp660;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp636;
		    fftw_real tmp637;
		    fftw_real tmp651;
		    fftw_real tmp652;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp636 = tmp580 + tmp581;
		    tmp637 = tmp611 + tmp612;
		    tmp638 = tmp636 - tmp637;
		    tmp664 = tmp636 + tmp637;
		    tmp651 = (K195090322 * tmp640) + (K980785280 * tmp639);
		    tmp652 = (K195090322 * tmp643) + (K980785280 * tmp642);
		    tmp653 = tmp651 - tmp652;
		    tmp665 = tmp651 + tmp652;
	       }
	       {
		    fftw_real tmp641;
		    fftw_real tmp644;
		    fftw_real tmp648;
		    fftw_real tmp649;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp641 = (K195090322 * tmp639) - (K980785280 * tmp640);
		    tmp644 = (K195090322 * tmp642) - (K980785280 * tmp643);
		    tmp645 = tmp641 + tmp644;
		    tmp661 = tmp641 - tmp644;
		    tmp648 = tmp608 - tmp609;
		    tmp649 = tmp585 - tmp588;
		    tmp650 = tmp648 + tmp649;
		    tmp660 = tmp648 - tmp649;
	       }
	       {
		    fftw_real tmp646;
		    fftw_real tmp654;
		    fftw_real tmp635;
		    fftw_real tmp647;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp646 = tmp638 - tmp645;
		    tmp654 = tmp650 - tmp653;
		    tmp635 = c_re(W[22]);
		    tmp647 = c_im(W[22]);
		    X[23 * iostride] = (tmp635 * tmp646) + (tmp647 * tmp654);
		    Y[-8 * iostride] = (tmp635 * tmp654) - (tmp647 * tmp646);
	       }
	       {
		    fftw_real tmp656;
		    fftw_real tmp658;
		    fftw_real tmp655;
		    fftw_real tmp657;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp656 = tmp638 + tmp645;
		    tmp658 = tmp650 + tmp653;
		    tmp655 = c_re(W[6]);
		    tmp657 = c_im(W[6]);
		    X[7 * iostride] = (tmp655 * tmp656) + (tmp657 * tmp658);
		    Y[-24 * iostride] = (tmp655 * tmp658) - (tmp657 * tmp656);
	       }
	       {
		    fftw_real tmp662;
		    fftw_real tmp666;
		    fftw_real tmp659;
		    fftw_real tmp663;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp662 = tmp660 + tmp661;
		    tmp666 = tmp664 - tmp665;
		    tmp659 = c_re(W[14]);
		    tmp663 = c_im(W[14]);
		    Y[-16 * iostride] = (tmp659 * tmp662) - (tmp663 * tmp666);
		    X[15 * iostride] = (tmp663 * tmp662) + (tmp659 * tmp666);
	       }
	       {
		    fftw_real tmp668;
		    fftw_real tmp670;
		    fftw_real tmp667;
		    fftw_real tmp669;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp668 = tmp660 - tmp661;
		    tmp670 = tmp664 + tmp665;
		    tmp667 = c_re(W[30]);
		    tmp669 = c_im(W[30]);
		    Y[0] = (tmp667 * tmp668) - (tmp669 * tmp670);
		    X[31 * iostride] = (tmp669 * tmp668) + (tmp667 * tmp670);
	       }
	  }
	  {
	       fftw_real tmp410;
	       fftw_real tmp436;
	       fftw_real tmp425;
	       fftw_real tmp437;
	       fftw_real tmp422;
	       fftw_real tmp432;
	       fftw_real tmp417;
	       fftw_real tmp433;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp409;
		    fftw_real tmp423;
		    fftw_real tmp424;
		    fftw_real tmp421;
		    fftw_real tmp413;
		    fftw_real tmp416;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp409 = K707106781 * (tmp383 + tmp384);
		    tmp410 = tmp408 - tmp409;
		    tmp436 = tmp408 + tmp409;
		    tmp423 = (K923879532 * tmp415) - (K382683432 * tmp414);
		    tmp424 = (K382683432 * tmp411) + (K923879532 * tmp412);
		    tmp425 = tmp423 - tmp424;
		    tmp437 = tmp423 + tmp424;
		    tmp421 = K707106781 * (tmp360 + tmp357);
		    tmp422 = tmp420 - tmp421;
		    tmp432 = tmp420 + tmp421;
		    tmp413 = (K923879532 * tmp411) - (K382683432 * tmp412);
		    tmp416 = (K923879532 * tmp414) + (K382683432 * tmp415);
		    tmp417 = tmp413 - tmp416;
		    tmp433 = tmp416 + tmp413;
	       }
	       {
		    fftw_real tmp418;
		    fftw_real tmp426;
		    fftw_real tmp407;
		    fftw_real tmp419;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp418 = tmp410 - tmp417;
		    tmp426 = tmp422 - tmp425;
		    tmp407 = c_re(W[25]);
		    tmp419 = c_im(W[25]);
		    X[26 * iostride] = (tmp407 * tmp418) + (tmp419 * tmp426);
		    Y[-5 * iostride] = (tmp407 * tmp426) - (tmp419 * tmp418);
	       }
	       {
		    fftw_real tmp428;
		    fftw_real tmp430;
		    fftw_real tmp427;
		    fftw_real tmp429;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp428 = tmp410 + tmp417;
		    tmp430 = tmp422 + tmp425;
		    tmp427 = c_re(W[9]);
		    tmp429 = c_im(W[9]);
		    X[10 * iostride] = (tmp427 * tmp428) + (tmp429 * tmp430);
		    Y[-21 * iostride] = (tmp427 * tmp430) - (tmp429 * tmp428);
	       }
	       {
		    fftw_real tmp434;
		    fftw_real tmp438;
		    fftw_real tmp431;
		    fftw_real tmp435;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp434 = tmp432 - tmp433;
		    tmp438 = tmp436 - tmp437;
		    tmp431 = c_re(W[17]);
		    tmp435 = c_im(W[17]);
		    Y[-13 * iostride] = (tmp431 * tmp434) - (tmp435 * tmp438);
		    X[18 * iostride] = (tmp435 * tmp434) + (tmp431 * tmp438);
	       }
	       {
		    fftw_real tmp440;
		    fftw_real tmp442;
		    fftw_real tmp439;
		    fftw_real tmp441;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp440 = tmp432 + tmp433;
		    tmp442 = tmp436 + tmp437;
		    tmp439 = c_re(W[1]);
		    tmp441 = c_im(W[1]);
		    Y[-29 * iostride] = (tmp439 * tmp440) - (tmp441 * tmp442);
		    X[2 * iostride] = (tmp441 * tmp440) + (tmp439 * tmp442);
	       }
	  }
	  {
	       fftw_real tmp362;
	       fftw_real tmp400;
	       fftw_real tmp389;
	       fftw_real tmp401;
	       fftw_real tmp386;
	       fftw_real tmp396;
	       fftw_real tmp377;
	       fftw_real tmp397;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp361;
		    fftw_real tmp387;
		    fftw_real tmp388;
		    fftw_real tmp385;
		    fftw_real tmp369;
		    fftw_real tmp376;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp361 = K707106781 * (tmp357 - tmp360);
		    tmp362 = tmp354 - tmp361;
		    tmp400 = tmp354 + tmp361;
		    tmp387 = (K382683432 * tmp375) - (K923879532 * tmp372);
		    tmp388 = (K923879532 * tmp365) + (K382683432 * tmp368);
		    tmp389 = tmp387 - tmp388;
		    tmp401 = tmp387 + tmp388;
		    tmp385 = K707106781 * (tmp383 - tmp384);
		    tmp386 = tmp382 - tmp385;
		    tmp396 = tmp382 + tmp385;
		    tmp369 = (K382683432 * tmp365) - (K923879532 * tmp368);
		    tmp376 = (K382683432 * tmp372) + (K923879532 * tmp375);
		    tmp377 = tmp369 - tmp376;
		    tmp397 = tmp376 + tmp369;
	       }
	       {
		    fftw_real tmp378;
		    fftw_real tmp390;
		    fftw_real tmp351;
		    fftw_real tmp379;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp378 = tmp362 - tmp377;
		    tmp390 = tmp386 - tmp389;
		    tmp351 = c_re(W[29]);
		    tmp379 = c_im(W[29]);
		    X[30 * iostride] = (tmp351 * tmp378) + (tmp379 * tmp390);
		    Y[-iostride] = (tmp351 * tmp390) - (tmp379 * tmp378);
	       }
	       {
		    fftw_real tmp392;
		    fftw_real tmp394;
		    fftw_real tmp391;
		    fftw_real tmp393;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp392 = tmp362 + tmp377;
		    tmp394 = tmp386 + tmp389;
		    tmp391 = c_re(W[13]);
		    tmp393 = c_im(W[13]);
		    X[14 * iostride] = (tmp391 * tmp392) + (tmp393 * tmp394);
		    Y[-17 * iostride] = (tmp391 * tmp394) - (tmp393 * tmp392);
	       }
	       {
		    fftw_real tmp398;
		    fftw_real tmp402;
		    fftw_real tmp395;
		    fftw_real tmp399;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp398 = tmp396 - tmp397;
		    tmp402 = tmp400 - tmp401;
		    tmp395 = c_re(W[21]);
		    tmp399 = c_im(W[21]);
		    Y[-9 * iostride] = (tmp395 * tmp398) - (tmp399 * tmp402);
		    X[22 * iostride] = (tmp399 * tmp398) + (tmp395 * tmp402);
	       }
	       {
		    fftw_real tmp404;
		    fftw_real tmp406;
		    fftw_real tmp403;
		    fftw_real tmp405;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp404 = tmp396 + tmp397;
		    tmp406 = tmp400 + tmp401;
		    tmp403 = c_re(W[5]);
		    tmp405 = c_im(W[5]);
		    Y[-25 * iostride] = (tmp403 * tmp404) - (tmp405 * tmp406);
		    X[6 * iostride] = (tmp405 * tmp404) + (tmp403 * tmp406);
	       }
	  }
	  {
	       fftw_real tmp590;
	       fftw_real tmp628;
	       fftw_real tmp617;
	       fftw_real tmp629;
	       fftw_real tmp605;
	       fftw_real tmp625;
	       fftw_real tmp614;
	       fftw_real tmp624;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp582;
		    fftw_real tmp589;
		    fftw_real tmp615;
		    fftw_real tmp616;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp582 = tmp580 - tmp581;
		    tmp589 = tmp585 + tmp588;
		    tmp590 = tmp582 - tmp589;
		    tmp628 = tmp582 + tmp589;
		    tmp615 = (K831469612 * tmp603) - (K555570233 * tmp600);
		    tmp616 = (K555570233 * tmp593) + (K831469612 * tmp596);
		    tmp617 = tmp615 - tmp616;
		    tmp629 = tmp615 + tmp616;
	       }
	       {
		    fftw_real tmp597;
		    fftw_real tmp604;
		    fftw_real tmp610;
		    fftw_real tmp613;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp597 = (K831469612 * tmp593) - (K555570233 * tmp596);
		    tmp604 = (K831469612 * tmp600) + (K555570233 * tmp603);
		    tmp605 = tmp597 - tmp604;
		    tmp625 = tmp604 + tmp597;
		    tmp610 = tmp608 + tmp609;
		    tmp613 = tmp611 - tmp612;
		    tmp614 = tmp610 - tmp613;
		    tmp624 = tmp610 + tmp613;
	       }
	       {
		    fftw_real tmp606;
		    fftw_real tmp618;
		    fftw_real tmp579;
		    fftw_real tmp607;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp606 = tmp590 - tmp605;
		    tmp618 = tmp614 - tmp617;
		    tmp579 = c_re(W[26]);
		    tmp607 = c_im(W[26]);
		    X[27 * iostride] = (tmp579 * tmp606) + (tmp607 * tmp618);
		    Y[-4 * iostride] = (tmp579 * tmp618) - (tmp607 * tmp606);
	       }
	       {
		    fftw_real tmp620;
		    fftw_real tmp622;
		    fftw_real tmp619;
		    fftw_real tmp621;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp620 = tmp590 + tmp605;
		    tmp622 = tmp614 + tmp617;
		    tmp619 = c_re(W[10]);
		    tmp621 = c_im(W[10]);
		    X[11 * iostride] = (tmp619 * tmp620) + (tmp621 * tmp622);
		    Y[-20 * iostride] = (tmp619 * tmp622) - (tmp621 * tmp620);
	       }
	       {
		    fftw_real tmp626;
		    fftw_real tmp630;
		    fftw_real tmp623;
		    fftw_real tmp627;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp626 = tmp624 - tmp625;
		    tmp630 = tmp628 - tmp629;
		    tmp623 = c_re(W[18]);
		    tmp627 = c_im(W[18]);
		    Y[-12 * iostride] = (tmp623 * tmp626) - (tmp627 * tmp630);
		    X[19 * iostride] = (tmp627 * tmp626) + (tmp623 * tmp630);
	       }
	       {
		    fftw_real tmp632;
		    fftw_real tmp634;
		    fftw_real tmp631;
		    fftw_real tmp633;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp632 = tmp624 + tmp625;
		    tmp634 = tmp628 + tmp629;
		    tmp631 = c_re(W[2]);
		    tmp633 = c_im(W[2]);
		    Y[-28 * iostride] = (tmp631 * tmp632) - (tmp633 * tmp634);
		    X[3 * iostride] = (tmp633 * tmp632) + (tmp631 * tmp634);
	       }
	  }
	  {
	       fftw_real tmp546;
	       fftw_real tmp572;
	       fftw_real tmp561;
	       fftw_real tmp573;
	       fftw_real tmp553;
	       fftw_real tmp569;
	       fftw_real tmp558;
	       fftw_real tmp568;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp544;
		    fftw_real tmp545;
		    fftw_real tmp559;
		    fftw_real tmp560;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp544 = tmp446 + tmp453;
		    tmp545 = tmp519 + tmp520;
		    tmp546 = tmp544 + tmp545;
		    tmp572 = tmp544 - tmp545;
		    tmp559 = (K195090322 * tmp547) + (K980785280 * tmp548);
		    tmp560 = (K980785280 * tmp551) - (K195090322 * tmp550);
		    tmp561 = tmp559 + tmp560;
		    tmp573 = tmp560 - tmp559;
	       }
	       {
		    fftw_real tmp549;
		    fftw_real tmp552;
		    fftw_real tmp556;
		    fftw_real tmp557;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp549 = (K980785280 * tmp547) - (K195090322 * tmp548);
		    tmp552 = (K980785280 * tmp550) + (K195090322 * tmp551);
		    tmp553 = tmp549 + tmp552;
		    tmp569 = tmp549 - tmp552;
		    tmp556 = tmp514 + tmp517;
		    tmp557 = tmp468 + tmp461;
		    tmp558 = tmp556 + tmp557;
		    tmp568 = tmp556 - tmp557;
	       }
	       {
		    fftw_real tmp554;
		    fftw_real tmp562;
		    fftw_real tmp543;
		    fftw_real tmp555;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp554 = tmp546 - tmp553;
		    tmp562 = tmp558 - tmp561;
		    tmp543 = c_re(W[16]);
		    tmp555 = c_im(W[16]);
		    X[17 * iostride] = (tmp543 * tmp554) + (tmp555 * tmp562);
		    Y[-14 * iostride] = (tmp543 * tmp562) - (tmp555 * tmp554);
	       }
	       {
		    fftw_real tmp564;
		    fftw_real tmp566;
		    fftw_real tmp563;
		    fftw_real tmp565;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp564 = tmp546 + tmp553;
		    tmp566 = tmp558 + tmp561;
		    tmp563 = c_re(W[0]);
		    tmp565 = c_im(W[0]);
		    X[iostride] = (tmp563 * tmp564) + (tmp565 * tmp566);
		    Y[-30 * iostride] = (tmp563 * tmp566) - (tmp565 * tmp564);
	       }
	       {
		    fftw_real tmp570;
		    fftw_real tmp574;
		    fftw_real tmp567;
		    fftw_real tmp571;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp570 = tmp568 - tmp569;
		    tmp574 = tmp572 - tmp573;
		    tmp567 = c_re(W[24]);
		    tmp571 = c_im(W[24]);
		    Y[-6 * iostride] = (tmp567 * tmp570) - (tmp571 * tmp574);
		    X[25 * iostride] = (tmp571 * tmp570) + (tmp567 * tmp574);
	       }
	       {
		    fftw_real tmp576;
		    fftw_real tmp578;
		    fftw_real tmp575;
		    fftw_real tmp577;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp576 = tmp568 + tmp569;
		    tmp578 = tmp572 + tmp573;
		    tmp575 = c_re(W[8]);
		    tmp577 = c_im(W[8]);
		    Y[-22 * iostride] = (tmp575 * tmp576) - (tmp577 * tmp578);
		    X[9 * iostride] = (tmp577 * tmp576) + (tmp575 * tmp578);
	       }
	  }
	  {
	       fftw_real tmp470;
	       fftw_real tmp536;
	       fftw_real tmp525;
	       fftw_real tmp537;
	       fftw_real tmp509;
	       fftw_real tmp533;
	       fftw_real tmp522;
	       fftw_real tmp532;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp454;
		    fftw_real tmp469;
		    fftw_real tmp523;
		    fftw_real tmp524;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp454 = tmp446 - tmp453;
		    tmp469 = tmp461 - tmp468;
		    tmp470 = tmp454 + tmp469;
		    tmp536 = tmp454 - tmp469;
		    tmp523 = (K831469612 * tmp481) + (K555570233 * tmp488);
		    tmp524 = (K555570233 * tmp507) - (K831469612 * tmp500);
		    tmp525 = tmp523 + tmp524;
		    tmp537 = tmp524 - tmp523;
	       }
	       {
		    fftw_real tmp489;
		    fftw_real tmp508;
		    fftw_real tmp518;
		    fftw_real tmp521;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp489 = (K555570233 * tmp481) - (K831469612 * tmp488);
		    tmp508 = (K555570233 * tmp500) + (K831469612 * tmp507);
		    tmp509 = tmp489 + tmp508;
		    tmp533 = tmp489 - tmp508;
		    tmp518 = tmp514 - tmp517;
		    tmp521 = tmp519 - tmp520;
		    tmp522 = tmp518 + tmp521;
		    tmp532 = tmp518 - tmp521;
	       }
	       {
		    fftw_real tmp510;
		    fftw_real tmp526;
		    fftw_real tmp443;
		    fftw_real tmp511;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp510 = tmp470 - tmp509;
		    tmp526 = tmp522 - tmp525;
		    tmp443 = c_re(W[20]);
		    tmp511 = c_im(W[20]);
		    X[21 * iostride] = (tmp443 * tmp510) + (tmp511 * tmp526);
		    Y[-10 * iostride] = (tmp443 * tmp526) - (tmp511 * tmp510);
	       }
	       {
		    fftw_real tmp528;
		    fftw_real tmp530;
		    fftw_real tmp527;
		    fftw_real tmp529;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp528 = tmp470 + tmp509;
		    tmp530 = tmp522 + tmp525;
		    tmp527 = c_re(W[4]);
		    tmp529 = c_im(W[4]);
		    X[5 * iostride] = (tmp527 * tmp528) + (tmp529 * tmp530);
		    Y[-26 * iostride] = (tmp527 * tmp530) - (tmp529 * tmp528);
	       }
	       {
		    fftw_real tmp534;
		    fftw_real tmp538;
		    fftw_real tmp531;
		    fftw_real tmp535;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp534 = tmp532 - tmp533;
		    tmp538 = tmp536 - tmp537;
		    tmp531 = c_re(W[28]);
		    tmp535 = c_im(W[28]);
		    Y[-2 * iostride] = (tmp531 * tmp534) - (tmp535 * tmp538);
		    X[29 * iostride] = (tmp535 * tmp534) + (tmp531 * tmp538);
	       }
	       {
		    fftw_real tmp540;
		    fftw_real tmp542;
		    fftw_real tmp539;
		    fftw_real tmp541;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp540 = tmp532 + tmp533;
		    tmp542 = tmp536 + tmp537;
		    tmp539 = c_re(W[12]);
		    tmp541 = c_im(W[12]);
		    Y[-18 * iostride] = (tmp539 * tmp540) - (tmp541 * tmp542);
		    X[13 * iostride] = (tmp541 * tmp540) + (tmp539 * tmp542);
	       }
	  }
     }
     if (i == m) {
	  fftw_real tmp7;
	  fftw_real tmp123;
	  fftw_real tmp35;
	  fftw_real tmp95;
	  fftw_real tmp82;
	  fftw_real tmp110;
	  fftw_real tmp146;
	  fftw_real tmp165;
	  fftw_real tmp29;
	  fftw_real tmp137;
	  fftw_real tmp141;
	  fftw_real tmp160;
	  fftw_real tmp69;
	  fftw_real tmp73;
	  fftw_real tmp103;
	  fftw_real tmp107;
	  fftw_real tmp14;
	  fftw_real tmp143;
	  fftw_real tmp46;
	  fftw_real tmp109;
	  fftw_real tmp126;
	  fftw_real tmp166;
	  fftw_real tmp77;
	  fftw_real tmp96;
	  fftw_real tmp22;
	  fftw_real tmp132;
	  fftw_real tmp140;
	  fftw_real tmp161;
	  fftw_real tmp58;
	  fftw_real tmp72;
	  fftw_real tmp100;
	  fftw_real tmp106;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp3;
	       fftw_real tmp31;
	       fftw_real tmp81;
	       fftw_real tmp144;
	       fftw_real tmp6;
	       fftw_real tmp78;
	       fftw_real tmp34;
	       fftw_real tmp145;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp1;
		    fftw_real tmp2;
		    fftw_real tmp79;
		    fftw_real tmp80;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp1 = X[0];
		    tmp2 = X[15 * iostride];
		    tmp3 = tmp1 + tmp2;
		    tmp31 = tmp1 - tmp2;
		    tmp79 = Y[0];
		    tmp80 = Y[-15 * iostride];
		    tmp81 = tmp79 + tmp80;
		    tmp144 = tmp79 - tmp80;
	       }
	       {
		    fftw_real tmp4;
		    fftw_real tmp5;
		    fftw_real tmp32;
		    fftw_real tmp33;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp4 = X[8 * iostride];
		    tmp5 = X[7 * iostride];
		    tmp6 = tmp4 + tmp5;
		    tmp78 = tmp4 - tmp5;
		    tmp32 = Y[-8 * iostride];
		    tmp33 = Y[-7 * iostride];
		    tmp34 = tmp32 + tmp33;
		    tmp145 = tmp32 - tmp33;
	       }
	       tmp7 = tmp3 + tmp6;
	       tmp123 = tmp3 - tmp6;
	       tmp35 = tmp31 - tmp34;
	       tmp95 = tmp31 + tmp34;
	       tmp82 = tmp78 + tmp81;
	       tmp110 = tmp81 - tmp78;
	       tmp146 = tmp144 - tmp145;
	       tmp165 = tmp145 + tmp144;
	  }
	  {
	       fftw_real tmp25;
	       fftw_real tmp59;
	       fftw_real tmp67;
	       fftw_real tmp134;
	       fftw_real tmp28;
	       fftw_real tmp64;
	       fftw_real tmp62;
	       fftw_real tmp135;
	       fftw_real tmp133;
	       fftw_real tmp136;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp23;
		    fftw_real tmp24;
		    fftw_real tmp65;
		    fftw_real tmp66;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp23 = X[iostride];
		    tmp24 = X[14 * iostride];
		    tmp25 = tmp23 + tmp24;
		    tmp59 = tmp23 - tmp24;
		    tmp65 = Y[-iostride];
		    tmp66 = Y[-14 * iostride];
		    tmp67 = tmp65 + tmp66;
		    tmp134 = tmp66 - tmp65;
	       }
	       {
		    fftw_real tmp26;
		    fftw_real tmp27;
		    fftw_real tmp60;
		    fftw_real tmp61;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp26 = X[6 * iostride];
		    tmp27 = X[9 * iostride];
		    tmp28 = tmp26 + tmp27;
		    tmp64 = tmp26 - tmp27;
		    tmp60 = Y[-6 * iostride];
		    tmp61 = Y[-9 * iostride];
		    tmp62 = tmp60 + tmp61;
		    tmp135 = tmp60 - tmp61;
	       }
	       tmp29 = tmp25 + tmp28;
	       tmp133 = tmp25 - tmp28;
	       tmp136 = tmp134 - tmp135;
	       tmp137 = tmp133 + tmp136;
	       tmp141 = tmp136 - tmp133;
	       tmp160 = tmp135 + tmp134;
	       {
		    fftw_real tmp63;
		    fftw_real tmp68;
		    fftw_real tmp101;
		    fftw_real tmp102;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp63 = tmp59 - tmp62;
		    tmp68 = tmp64 - tmp67;
		    tmp69 = (K923879532 * tmp63) + (K382683432 * tmp68);
		    tmp73 = (K923879532 * tmp68) - (K382683432 * tmp63);
		    tmp101 = tmp59 + tmp62;
		    tmp102 = tmp64 + tmp67;
		    tmp103 = (K382683432 * tmp101) - (K923879532 * tmp102);
		    tmp107 = (K923879532 * tmp101) + (K382683432 * tmp102);
	       }
	  }
	  {
	       fftw_real tmp10;
	       fftw_real tmp36;
	       fftw_real tmp39;
	       fftw_real tmp125;
	       fftw_real tmp13;
	       fftw_real tmp41;
	       fftw_real tmp44;
	       fftw_real tmp124;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp8;
		    fftw_real tmp9;
		    fftw_real tmp37;
		    fftw_real tmp38;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp8 = X[4 * iostride];
		    tmp9 = X[11 * iostride];
		    tmp10 = tmp8 + tmp9;
		    tmp36 = tmp8 - tmp9;
		    tmp37 = Y[-4 * iostride];
		    tmp38 = Y[-11 * iostride];
		    tmp39 = tmp37 + tmp38;
		    tmp125 = tmp37 - tmp38;
	       }
	       {
		    fftw_real tmp11;
		    fftw_real tmp12;
		    fftw_real tmp42;
		    fftw_real tmp43;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp11 = X[3 * iostride];
		    tmp12 = X[12 * iostride];
		    tmp13 = tmp11 + tmp12;
		    tmp41 = tmp11 - tmp12;
		    tmp42 = Y[-3 * iostride];
		    tmp43 = Y[-12 * iostride];
		    tmp44 = tmp42 + tmp43;
		    tmp124 = tmp43 - tmp42;
	       }
	       {
		    fftw_real tmp40;
		    fftw_real tmp45;
		    fftw_real tmp75;
		    fftw_real tmp76;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp14 = tmp10 + tmp13;
		    tmp143 = tmp10 - tmp13;
		    tmp40 = tmp36 - tmp39;
		    tmp45 = tmp41 - tmp44;
		    tmp46 = K707106781 * (tmp40 + tmp45);
		    tmp109 = K707106781 * (tmp40 - tmp45);
		    tmp126 = tmp124 - tmp125;
		    tmp166 = tmp125 + tmp124;
		    tmp75 = tmp36 + tmp39;
		    tmp76 = tmp41 + tmp44;
		    tmp77 = K707106781 * (tmp75 - tmp76);
		    tmp96 = K707106781 * (tmp75 + tmp76);
	       }
	  }
	  {
	       fftw_real tmp18;
	       fftw_real tmp48;
	       fftw_real tmp56;
	       fftw_real tmp129;
	       fftw_real tmp21;
	       fftw_real tmp53;
	       fftw_real tmp51;
	       fftw_real tmp130;
	       fftw_real tmp128;
	       fftw_real tmp131;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp16;
		    fftw_real tmp17;
		    fftw_real tmp54;
		    fftw_real tmp55;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp16 = X[2 * iostride];
		    tmp17 = X[13 * iostride];
		    tmp18 = tmp16 + tmp17;
		    tmp48 = tmp16 - tmp17;
		    tmp54 = Y[-2 * iostride];
		    tmp55 = Y[-13 * iostride];
		    tmp56 = tmp54 + tmp55;
		    tmp129 = tmp54 - tmp55;
	       }
	       {
		    fftw_real tmp19;
		    fftw_real tmp20;
		    fftw_real tmp49;
		    fftw_real tmp50;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp19 = X[10 * iostride];
		    tmp20 = X[5 * iostride];
		    tmp21 = tmp19 + tmp20;
		    tmp53 = tmp19 - tmp20;
		    tmp49 = Y[-10 * iostride];
		    tmp50 = Y[-5 * iostride];
		    tmp51 = tmp49 + tmp50;
		    tmp130 = tmp49 - tmp50;
	       }
	       tmp22 = tmp18 + tmp21;
	       tmp128 = tmp18 - tmp21;
	       tmp131 = tmp129 - tmp130;
	       tmp132 = tmp128 - tmp131;
	       tmp140 = tmp128 + tmp131;
	       tmp161 = tmp130 + tmp129;
	       {
		    fftw_real tmp52;
		    fftw_real tmp57;
		    fftw_real tmp98;
		    fftw_real tmp99;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp52 = tmp48 - tmp51;
		    tmp57 = tmp53 + tmp56;
		    tmp58 = (K923879532 * tmp52) - (K382683432 * tmp57);
		    tmp72 = (K382683432 * tmp52) + (K923879532 * tmp57);
		    tmp98 = tmp48 + tmp51;
		    tmp99 = tmp56 - tmp53;
		    tmp100 = (K382683432 * tmp98) - (K923879532 * tmp99);
		    tmp106 = (K923879532 * tmp98) + (K382683432 * tmp99);
	       }
	  }
	  {
	       fftw_real tmp15;
	       fftw_real tmp30;
	       fftw_real tmp171;
	       fftw_real tmp172;
	       fftw_real tmp173;
	       fftw_real tmp174;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp15 = tmp7 + tmp14;
	       tmp30 = tmp22 + tmp29;
	       tmp171 = tmp15 - tmp30;
	       tmp172 = tmp166 + tmp165;
	       tmp173 = tmp161 + tmp160;
	       tmp174 = tmp172 - tmp173;
	       X[0] = K2_000000000 * (tmp15 + tmp30);
	       X[16 * iostride] = -(K2_000000000 * (tmp173 + tmp172));
	       X[8 * iostride] = K1_414213562 * (tmp171 - tmp174);
	       X[24 * iostride] = -(K1_414213562 * (tmp171 + tmp174));
	  }
	  {
	       fftw_real tmp163;
	       fftw_real tmp169;
	       fftw_real tmp168;
	       fftw_real tmp170;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp159;
		    fftw_real tmp162;
		    fftw_real tmp164;
		    fftw_real tmp167;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp159 = tmp7 - tmp14;
		    tmp162 = tmp160 - tmp161;
		    tmp163 = tmp159 + tmp162;
		    tmp169 = tmp159 - tmp162;
		    tmp164 = tmp22 - tmp29;
		    tmp167 = tmp165 - tmp166;
		    tmp168 = tmp164 + tmp167;
		    tmp170 = tmp167 - tmp164;
	       }
	       X[4 * iostride] = (K1_847759065 * tmp163) - (K765366864 * tmp168);
	       X[20 * iostride] = -((K765366864 * tmp163) + (K1_847759065 * tmp168));
	       X[12 * iostride] = (K765366864 * tmp169) - (K1_847759065 * tmp170);
	       X[28 * iostride] = -((K1_847759065 * tmp169) + (K765366864 * tmp170));
	  }
	  {
	       fftw_real tmp71;
	       fftw_real tmp85;
	       fftw_real tmp84;
	       fftw_real tmp86;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp47;
		    fftw_real tmp70;
		    fftw_real tmp74;
		    fftw_real tmp83;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp47 = tmp35 + tmp46;
		    tmp70 = tmp58 + tmp69;
		    tmp71 = tmp47 + tmp70;
		    tmp85 = tmp47 - tmp70;
		    tmp74 = tmp72 + tmp73;
		    tmp83 = tmp77 + tmp82;
		    tmp84 = tmp74 + tmp83;
		    tmp86 = tmp83 - tmp74;
	       }
	       X[iostride] = (K1_990369453 * tmp71) - (K196034280 * tmp84);
	       X[17 * iostride] = -((K196034280 * tmp71) + (K1_990369453 * tmp84));
	       X[9 * iostride] = (K1_268786568 * tmp85) - (K1_546020906 * tmp86);
	       X[25 * iostride] = -((K1_546020906 * tmp85) + (K1_268786568 * tmp86));
	  }
	  {
	       fftw_real tmp89;
	       fftw_real tmp93;
	       fftw_real tmp92;
	       fftw_real tmp94;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp87;
		    fftw_real tmp88;
		    fftw_real tmp90;
		    fftw_real tmp91;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp87 = tmp35 - tmp46;
		    tmp88 = tmp73 - tmp72;
		    tmp89 = tmp87 + tmp88;
		    tmp93 = tmp87 - tmp88;
		    tmp90 = tmp58 - tmp69;
		    tmp91 = tmp82 - tmp77;
		    tmp92 = tmp90 + tmp91;
		    tmp94 = tmp91 - tmp90;
	       }
	       X[5 * iostride] = (K1_763842528 * tmp89) - (K942793473 * tmp92);
	       X[21 * iostride] = -((K942793473 * tmp89) + (K1_763842528 * tmp92));
	       X[13 * iostride] = (K580569354 * tmp93) - (K1_913880671 * tmp94);
	       X[29 * iostride] = -((K1_913880671 * tmp93) + (K580569354 * tmp94));
	  }
	  {
	       fftw_real tmp105;
	       fftw_real tmp113;
	       fftw_real tmp112;
	       fftw_real tmp114;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp97;
		    fftw_real tmp104;
		    fftw_real tmp108;
		    fftw_real tmp111;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp97 = tmp95 - tmp96;
		    tmp104 = tmp100 + tmp103;
		    tmp105 = tmp97 + tmp104;
		    tmp113 = tmp97 - tmp104;
		    tmp108 = tmp106 - tmp107;
		    tmp111 = tmp109 + tmp110;
		    tmp112 = tmp108 + tmp111;
		    tmp114 = tmp111 - tmp108;
	       }
	       X[3 * iostride] = (K1_913880671 * tmp105) - (K580569354 * tmp112);
	       X[19 * iostride] = -((K580569354 * tmp105) + (K1_913880671 * tmp112));
	       X[11 * iostride] = (K942793473 * tmp113) - (K1_763842528 * tmp114);
	       X[27 * iostride] = -((K1_763842528 * tmp113) + (K942793473 * tmp114));
	  }
	  {
	       fftw_real tmp117;
	       fftw_real tmp121;
	       fftw_real tmp120;
	       fftw_real tmp122;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp115;
		    fftw_real tmp116;
		    fftw_real tmp118;
		    fftw_real tmp119;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp115 = tmp95 + tmp96;
		    tmp116 = tmp106 + tmp107;
		    tmp117 = tmp115 - tmp116;
		    tmp121 = tmp115 + tmp116;
		    tmp118 = tmp100 - tmp103;
		    tmp119 = tmp110 - tmp109;
		    tmp120 = tmp118 + tmp119;
		    tmp122 = tmp119 - tmp118;
	       }
	       X[7 * iostride] = (K1_546020906 * tmp117) - (K1_268786568 * tmp120);
	       X[23 * iostride] = -((K1_268786568 * tmp117) + (K1_546020906 * tmp120));
	       X[15 * iostride] = (K196034280 * tmp121) - (K1_990369453 * tmp122);
	       X[31 * iostride] = -((K1_990369453 * tmp121) + (K196034280 * tmp122));
	  }
	  {
	       fftw_real tmp139;
	       fftw_real tmp149;
	       fftw_real tmp148;
	       fftw_real tmp150;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp127;
		    fftw_real tmp138;
		    fftw_real tmp142;
		    fftw_real tmp147;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp127 = tmp123 + tmp126;
		    tmp138 = K707106781 * (tmp132 + tmp137);
		    tmp139 = tmp127 + tmp138;
		    tmp149 = tmp127 - tmp138;
		    tmp142 = K707106781 * (tmp140 + tmp141);
		    tmp147 = tmp143 + tmp146;
		    tmp148 = tmp142 + tmp147;
		    tmp150 = tmp147 - tmp142;
	       }
	       X[2 * iostride] = (K1_961570560 * tmp139) - (K390180644 * tmp148);
	       X[18 * iostride] = -((K390180644 * tmp139) + (K1_961570560 * tmp148));
	       X[10 * iostride] = (K1_111140466 * tmp149) - (K1_662939224 * tmp150);
	       X[26 * iostride] = -((K1_662939224 * tmp149) + (K1_111140466 * tmp150));
	  }
	  {
	       fftw_real tmp153;
	       fftw_real tmp157;
	       fftw_real tmp156;
	       fftw_real tmp158;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp151;
		    fftw_real tmp152;
		    fftw_real tmp154;
		    fftw_real tmp155;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp151 = tmp123 - tmp126;
		    tmp152 = K707106781 * (tmp141 - tmp140);
		    tmp153 = tmp151 + tmp152;
		    tmp157 = tmp151 - tmp152;
		    tmp154 = K707106781 * (tmp132 - tmp137);
		    tmp155 = tmp146 - tmp143;
		    tmp156 = tmp154 + tmp155;
		    tmp158 = tmp155 - tmp154;
	       }
	       X[6 * iostride] = (K1_662939224 * tmp153) - (K1_111140466 * tmp156);
	       X[22 * iostride] = -((K1_111140466 * tmp153) + (K1_662939224 * tmp156));
	       X[14 * iostride] = (K390180644 * tmp157) - (K1_961570560 * tmp158);
	       X[30 * iostride] = -((K1_961570560 * tmp157) + (K390180644 * tmp158));
	  }
     }
}

static const int twiddle_order[] =
{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31};
fftw_codelet_desc fftw_hc2hc_backward_32_desc =
{
     "fftw_hc2hc_backward_32",
     (void (*)()) fftw_hc2hc_backward_32,
     32,
     FFTW_BACKWARD,
     FFTW_HC2HC,
     718,
     31,
     twiddle_order,
};
