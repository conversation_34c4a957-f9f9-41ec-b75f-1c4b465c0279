/*
 * Copyright (c) 1997-1999 Massachusetts Institute of Technology
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 *
 */

/* This file was automatically generated --- DO NOT EDIT */
/* Generated on Sun Nov  7 20:44:52 EST 1999 */

#include "fftw-int.h"
#include "fftw.h"

/* Generated by: ./genfft -magic-alignment-check -magic-twiddle-load-all -magic-variables 4 -magic-loopi -hc2hc-forward 32 */

/*
 * This function contains 764 FP additions, 340 FP multiplications,
 * (or, 618 additions, 194 multiplications, 146 fused multiply/add),
 * 91 stack variables, and 256 memory accesses
 */
static const fftw_real K145142338 = FFTW_KONST(+0.145142338627231183818096187908697637345738139);
static const fftw_real K478470167 = FFTW_KONST(+0.478470167866104432467898943490134984741424603);
static const fftw_real K235698368 = FFTW_KONST(+0.235698368412998824278193812952627188828730159);
static const fftw_real K440960632 = FFTW_KONST(+0.440960632174177514856378431830194174754221310);
static const fftw_real K317196642 = FFTW_KONST(+0.317196642081822749107585806612746685337843547);
static const fftw_real K386505226 = FFTW_KONST(+0.386505226681368480405453304879234900485520646);
static const fftw_real K497592363 = FFTW_KONST(+0.497592363336098443122418476554739960787737434);
static const fftw_real K049008570 = FFTW_KONST(+0.049008570164780300997097781944320922930568337);
static const fftw_real K277785116 = FFTW_KONST(+0.277785116509801112371415406974266437187468595);
static const fftw_real K415734806 = FFTW_KONST(+0.415734806151272618539394188808952878369280406);
static const fftw_real K097545161 = FFTW_KONST(+0.097545161008064133924142434238511120463845809);
static const fftw_real K490392640 = FFTW_KONST(+0.490392640201615224563091118067119518486966865);
static const fftw_real K765366864 = FFTW_KONST(+0.765366864730179543456919968060797733522689125);
static const fftw_real K1_847759065 = FFTW_KONST(+1.847759065022573512256366378793576573644833252);
static const fftw_real K1_414213562 = FFTW_KONST(+1.414213562373095048801688724209698078569671875);
static const fftw_real K2_000000000 = FFTW_KONST(+2.000000000000000000000000000000000000000000000);
static const fftw_real K195090322 = FFTW_KONST(+0.195090322016128267848284868477022240927691618);
static const fftw_real K980785280 = FFTW_KONST(+0.980785280403230449126182236134239036973933731);
static const fftw_real K555570233 = FFTW_KONST(+0.555570233019602224742830813948532874374937191);
static const fftw_real K831469612 = FFTW_KONST(+0.831469612302545237078788377617905756738560812);
static const fftw_real K382683432 = FFTW_KONST(+0.382683432365089771728459984030398866761344562);
static const fftw_real K923879532 = FFTW_KONST(+0.923879532511286756128183189396788286822416626);
static const fftw_real K707106781 = FFTW_KONST(+0.707106781186547524400844362104849039284835938);

/*
 * Generator Id's : 
 * $Id: exprdag.ml,v 1.41 1999/05/26 15:44:14 fftw Exp $
 * $Id: fft.ml,v 1.43 1999/05/17 19:44:18 fftw Exp $
 * $Id: to_c.ml,v 1.25 1999/10/26 21:41:32 stevenj Exp $
 */

void fftw_hc2hc_forward_32(fftw_real *A, const fftw_complex *W, int iostride, int m, int dist)
{
     int i;
     fftw_real *X;
     fftw_real *Y;
     X = A;
     Y = A + (32 * iostride);
     {
	  fftw_real tmp685;
	  fftw_real tmp813;
	  fftw_real tmp709;
	  fftw_real tmp761;
	  fftw_real tmp692;
	  fftw_real tmp826;
	  fftw_real tmp712;
	  fftw_real tmp760;
	  fftw_real tmp801;
	  fftw_real tmp821;
	  fftw_real tmp749;
	  fftw_real tmp777;
	  fftw_real tmp804;
	  fftw_real tmp822;
	  fftw_real tmp754;
	  fftw_real tmp778;
	  fftw_real tmp700;
	  fftw_real tmp814;
	  fftw_real tmp716;
	  fftw_real tmp758;
	  fftw_real tmp707;
	  fftw_real tmp815;
	  fftw_real tmp719;
	  fftw_real tmp757;
	  fftw_real tmp794;
	  fftw_real tmp818;
	  fftw_real tmp732;
	  fftw_real tmp774;
	  fftw_real tmp797;
	  fftw_real tmp819;
	  fftw_real tmp737;
	  fftw_real tmp775;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp679;
	       fftw_real tmp680;
	       fftw_real tmp681;
	       fftw_real tmp682;
	       fftw_real tmp683;
	       fftw_real tmp684;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp679 = X[0];
	       tmp680 = X[16 * iostride];
	       tmp681 = tmp679 + tmp680;
	       tmp682 = X[8 * iostride];
	       tmp683 = X[24 * iostride];
	       tmp684 = tmp682 + tmp683;
	       tmp685 = tmp681 + tmp684;
	       tmp813 = tmp681 - tmp684;
	       tmp709 = tmp679 - tmp680;
	       tmp761 = tmp682 - tmp683;
	  }
	  {
	       fftw_real tmp688;
	       fftw_real tmp710;
	       fftw_real tmp691;
	       fftw_real tmp711;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp686;
		    fftw_real tmp687;
		    fftw_real tmp689;
		    fftw_real tmp690;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp686 = X[4 * iostride];
		    tmp687 = X[20 * iostride];
		    tmp688 = tmp686 + tmp687;
		    tmp710 = tmp686 - tmp687;
		    tmp689 = X[28 * iostride];
		    tmp690 = X[12 * iostride];
		    tmp691 = tmp689 + tmp690;
		    tmp711 = tmp689 - tmp690;
	       }
	       tmp692 = tmp688 + tmp691;
	       tmp826 = tmp691 - tmp688;
	       tmp712 = K707106781 * (tmp710 + tmp711);
	       tmp760 = K707106781 * (tmp711 - tmp710);
	  }
	  {
	       fftw_real tmp741;
	       fftw_real tmp799;
	       fftw_real tmp753;
	       fftw_real tmp800;
	       fftw_real tmp744;
	       fftw_real tmp802;
	       fftw_real tmp747;
	       fftw_real tmp803;
	       fftw_real tmp748;
	       fftw_real tmp750;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp739;
		    fftw_real tmp740;
		    fftw_real tmp751;
		    fftw_real tmp752;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp739 = X[31 * iostride];
		    tmp740 = X[15 * iostride];
		    tmp741 = tmp739 - tmp740;
		    tmp799 = tmp739 + tmp740;
		    tmp751 = X[7 * iostride];
		    tmp752 = X[23 * iostride];
		    tmp753 = tmp751 - tmp752;
		    tmp800 = tmp751 + tmp752;
	       }
	       {
		    fftw_real tmp742;
		    fftw_real tmp743;
		    fftw_real tmp745;
		    fftw_real tmp746;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp742 = X[3 * iostride];
		    tmp743 = X[19 * iostride];
		    tmp744 = tmp742 - tmp743;
		    tmp802 = tmp742 + tmp743;
		    tmp745 = X[27 * iostride];
		    tmp746 = X[11 * iostride];
		    tmp747 = tmp745 - tmp746;
		    tmp803 = tmp745 + tmp746;
	       }
	       tmp801 = tmp799 + tmp800;
	       tmp821 = tmp799 - tmp800;
	       tmp748 = K707106781 * (tmp744 + tmp747);
	       tmp749 = tmp741 + tmp748;
	       tmp777 = tmp741 - tmp748;
	       tmp804 = tmp802 + tmp803;
	       tmp822 = tmp803 - tmp802;
	       tmp750 = K707106781 * (tmp747 - tmp744);
	       tmp754 = tmp750 - tmp753;
	       tmp778 = tmp753 + tmp750;
	  }
	  {
	       fftw_real tmp696;
	       fftw_real tmp714;
	       fftw_real tmp699;
	       fftw_real tmp715;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp694;
		    fftw_real tmp695;
		    fftw_real tmp697;
		    fftw_real tmp698;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp694 = X[2 * iostride];
		    tmp695 = X[18 * iostride];
		    tmp696 = tmp694 + tmp695;
		    tmp714 = tmp694 - tmp695;
		    tmp697 = X[10 * iostride];
		    tmp698 = X[26 * iostride];
		    tmp699 = tmp697 + tmp698;
		    tmp715 = tmp697 - tmp698;
	       }
	       tmp700 = tmp696 + tmp699;
	       tmp814 = tmp696 - tmp699;
	       tmp716 = (K923879532 * tmp714) - (K382683432 * tmp715);
	       tmp758 = (K382683432 * tmp714) + (K923879532 * tmp715);
	  }
	  {
	       fftw_real tmp703;
	       fftw_real tmp717;
	       fftw_real tmp706;
	       fftw_real tmp718;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp701;
		    fftw_real tmp702;
		    fftw_real tmp704;
		    fftw_real tmp705;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp701 = X[30 * iostride];
		    tmp702 = X[14 * iostride];
		    tmp703 = tmp701 + tmp702;
		    tmp717 = tmp701 - tmp702;
		    tmp704 = X[6 * iostride];
		    tmp705 = X[22 * iostride];
		    tmp706 = tmp704 + tmp705;
		    tmp718 = tmp704 - tmp705;
	       }
	       tmp707 = tmp703 + tmp706;
	       tmp815 = tmp703 - tmp706;
	       tmp719 = (K923879532 * tmp717) + (K382683432 * tmp718);
	       tmp757 = (K382683432 * tmp717) - (K923879532 * tmp718);
	  }
	  {
	       fftw_real tmp724;
	       fftw_real tmp792;
	       fftw_real tmp736;
	       fftw_real tmp793;
	       fftw_real tmp727;
	       fftw_real tmp795;
	       fftw_real tmp730;
	       fftw_real tmp796;
	       fftw_real tmp731;
	       fftw_real tmp733;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp722;
		    fftw_real tmp723;
		    fftw_real tmp734;
		    fftw_real tmp735;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp722 = X[iostride];
		    tmp723 = X[17 * iostride];
		    tmp724 = tmp722 - tmp723;
		    tmp792 = tmp722 + tmp723;
		    tmp734 = X[9 * iostride];
		    tmp735 = X[25 * iostride];
		    tmp736 = tmp734 - tmp735;
		    tmp793 = tmp734 + tmp735;
	       }
	       {
		    fftw_real tmp725;
		    fftw_real tmp726;
		    fftw_real tmp728;
		    fftw_real tmp729;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp725 = X[5 * iostride];
		    tmp726 = X[21 * iostride];
		    tmp727 = tmp725 - tmp726;
		    tmp795 = tmp725 + tmp726;
		    tmp728 = X[29 * iostride];
		    tmp729 = X[13 * iostride];
		    tmp730 = tmp728 - tmp729;
		    tmp796 = tmp728 + tmp729;
	       }
	       tmp794 = tmp792 + tmp793;
	       tmp818 = tmp792 - tmp793;
	       tmp731 = K707106781 * (tmp727 + tmp730);
	       tmp732 = tmp724 + tmp731;
	       tmp774 = tmp724 - tmp731;
	       tmp797 = tmp795 + tmp796;
	       tmp819 = tmp796 - tmp795;
	       tmp733 = K707106781 * (tmp730 - tmp727);
	       tmp737 = tmp733 - tmp736;
	       tmp775 = tmp736 + tmp733;
	  }
	  {
	       fftw_real tmp693;
	       fftw_real tmp708;
	       fftw_real tmp809;
	       fftw_real tmp810;
	       fftw_real tmp811;
	       fftw_real tmp812;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp693 = tmp685 + tmp692;
	       tmp708 = tmp700 + tmp707;
	       tmp809 = tmp693 + tmp708;
	       tmp810 = tmp794 + tmp797;
	       tmp811 = tmp801 + tmp804;
	       tmp812 = tmp810 + tmp811;
	       X[8 * iostride] = tmp693 - tmp708;
	       Y[-8 * iostride] = tmp811 - tmp810;
	       X[16 * iostride] = tmp809 - tmp812;
	       X[0] = tmp809 + tmp812;
	  }
	  {
	       fftw_real tmp791;
	       fftw_real tmp807;
	       fftw_real tmp806;
	       fftw_real tmp808;
	       fftw_real tmp798;
	       fftw_real tmp805;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp791 = tmp685 - tmp692;
	       tmp807 = tmp707 - tmp700;
	       tmp798 = tmp794 - tmp797;
	       tmp805 = tmp801 - tmp804;
	       tmp806 = K707106781 * (tmp798 + tmp805);
	       tmp808 = K707106781 * (tmp805 - tmp798);
	       X[12 * iostride] = tmp791 - tmp806;
	       X[4 * iostride] = tmp791 + tmp806;
	       Y[-4 * iostride] = tmp807 + tmp808;
	       Y[-12 * iostride] = tmp808 - tmp807;
	  }
	  {
	       fftw_real tmp817;
	       fftw_real tmp833;
	       fftw_real tmp827;
	       fftw_real tmp829;
	       fftw_real tmp824;
	       fftw_real tmp828;
	       fftw_real tmp832;
	       fftw_real tmp834;
	       fftw_real tmp816;
	       fftw_real tmp825;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp816 = K707106781 * (tmp814 + tmp815);
	       tmp817 = tmp813 + tmp816;
	       tmp833 = tmp813 - tmp816;
	       tmp825 = K707106781 * (tmp815 - tmp814);
	       tmp827 = tmp825 - tmp826;
	       tmp829 = tmp826 + tmp825;
	       {
		    fftw_real tmp820;
		    fftw_real tmp823;
		    fftw_real tmp830;
		    fftw_real tmp831;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp820 = (K923879532 * tmp818) + (K382683432 * tmp819);
		    tmp823 = (K923879532 * tmp821) - (K382683432 * tmp822);
		    tmp824 = tmp820 + tmp823;
		    tmp828 = tmp823 - tmp820;
		    tmp830 = (K923879532 * tmp819) - (K382683432 * tmp818);
		    tmp831 = (K382683432 * tmp821) + (K923879532 * tmp822);
		    tmp832 = tmp830 + tmp831;
		    tmp834 = tmp831 - tmp830;
	       }
	       X[14 * iostride] = tmp817 - tmp824;
	       X[2 * iostride] = tmp817 + tmp824;
	       Y[-6 * iostride] = tmp827 + tmp828;
	       Y[-10 * iostride] = tmp828 - tmp827;
	       Y[-2 * iostride] = tmp829 + tmp832;
	       Y[-14 * iostride] = tmp832 - tmp829;
	       X[10 * iostride] = tmp833 - tmp834;
	       X[6 * iostride] = tmp833 + tmp834;
	  }
	  {
	       fftw_real tmp773;
	       fftw_real tmp789;
	       fftw_real tmp788;
	       fftw_real tmp790;
	       fftw_real tmp780;
	       fftw_real tmp784;
	       fftw_real tmp783;
	       fftw_real tmp785;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp771;
		    fftw_real tmp772;
		    fftw_real tmp786;
		    fftw_real tmp787;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp771 = tmp709 - tmp712;
		    tmp772 = tmp758 + tmp757;
		    tmp773 = tmp771 + tmp772;
		    tmp789 = tmp771 - tmp772;
		    tmp786 = (K831469612 * tmp775) - (K555570233 * tmp774);
		    tmp787 = (K555570233 * tmp777) + (K831469612 * tmp778);
		    tmp788 = tmp786 + tmp787;
		    tmp790 = tmp787 - tmp786;
	       }
	       {
		    fftw_real tmp776;
		    fftw_real tmp779;
		    fftw_real tmp781;
		    fftw_real tmp782;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp776 = (K831469612 * tmp774) + (K555570233 * tmp775);
		    tmp779 = (K831469612 * tmp777) - (K555570233 * tmp778);
		    tmp780 = tmp776 + tmp779;
		    tmp784 = tmp779 - tmp776;
		    tmp781 = tmp719 - tmp716;
		    tmp782 = tmp761 + tmp760;
		    tmp783 = tmp781 - tmp782;
		    tmp785 = tmp782 + tmp781;
	       }
	       X[13 * iostride] = tmp773 - tmp780;
	       X[3 * iostride] = tmp773 + tmp780;
	       Y[-5 * iostride] = tmp783 + tmp784;
	       Y[-11 * iostride] = tmp784 - tmp783;
	       Y[-3 * iostride] = tmp785 + tmp788;
	       Y[-13 * iostride] = tmp788 - tmp785;
	       X[11 * iostride] = tmp789 - tmp790;
	       X[5 * iostride] = tmp789 + tmp790;
	  }
	  {
	       fftw_real tmp721;
	       fftw_real tmp769;
	       fftw_real tmp768;
	       fftw_real tmp770;
	       fftw_real tmp756;
	       fftw_real tmp764;
	       fftw_real tmp763;
	       fftw_real tmp765;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp713;
		    fftw_real tmp720;
		    fftw_real tmp766;
		    fftw_real tmp767;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp713 = tmp709 + tmp712;
		    tmp720 = tmp716 + tmp719;
		    tmp721 = tmp713 + tmp720;
		    tmp769 = tmp713 - tmp720;
		    tmp766 = (K980785280 * tmp737) - (K195090322 * tmp732);
		    tmp767 = (K195090322 * tmp749) + (K980785280 * tmp754);
		    tmp768 = tmp766 + tmp767;
		    tmp770 = tmp767 - tmp766;
	       }
	       {
		    fftw_real tmp738;
		    fftw_real tmp755;
		    fftw_real tmp759;
		    fftw_real tmp762;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp738 = (K980785280 * tmp732) + (K195090322 * tmp737);
		    tmp755 = (K980785280 * tmp749) - (K195090322 * tmp754);
		    tmp756 = tmp738 + tmp755;
		    tmp764 = tmp755 - tmp738;
		    tmp759 = tmp757 - tmp758;
		    tmp762 = tmp760 - tmp761;
		    tmp763 = tmp759 - tmp762;
		    tmp765 = tmp762 + tmp759;
	       }
	       X[15 * iostride] = tmp721 - tmp756;
	       X[iostride] = tmp721 + tmp756;
	       Y[-7 * iostride] = tmp763 + tmp764;
	       Y[-9 * iostride] = tmp764 - tmp763;
	       Y[-iostride] = tmp765 + tmp768;
	       Y[-15 * iostride] = tmp768 - tmp765;
	       X[9 * iostride] = tmp769 - tmp770;
	       X[7 * iostride] = tmp769 + tmp770;
	  }
     }
     X = X + dist;
     Y = Y - dist;
     for (i = 2; i < m; i = i + 2, X = X + dist, Y = Y - dist, W = W + 31) {
	  fftw_real tmp201;
	  fftw_real tmp533;
	  fftw_real tmp653;
	  fftw_real tmp667;
	  fftw_real tmp623;
	  fftw_real tmp637;
	  fftw_real tmp373;
	  fftw_real tmp485;
	  fftw_real tmp343;
	  fftw_real tmp561;
	  fftw_real tmp458;
	  fftw_real tmp508;
	  fftw_real tmp568;
	  fftw_real tmp604;
	  fftw_real tmp441;
	  fftw_real tmp505;
	  fftw_real tmp224;
	  fftw_real tmp636;
	  fftw_real tmp383;
	  fftw_real tmp487;
	  fftw_real tmp536;
	  fftw_real tmp618;
	  fftw_real tmp378;
	  fftw_real tmp486;
	  fftw_real tmp366;
	  fftw_real tmp569;
	  fftw_real tmp564;
	  fftw_real tmp605;
	  fftw_real tmp452;
	  fftw_real tmp509;
	  fftw_real tmp461;
	  fftw_real tmp506;
	  fftw_real tmp248;
	  fftw_real tmp541;
	  fftw_real tmp395;
	  fftw_real tmp491;
	  fftw_real tmp540;
	  fftw_real tmp594;
	  fftw_real tmp390;
	  fftw_real tmp490;
	  fftw_real tmp296;
	  fftw_real tmp555;
	  fftw_real tmp431;
	  fftw_real tmp498;
	  fftw_real tmp552;
	  fftw_real tmp599;
	  fftw_real tmp414;
	  fftw_real tmp501;
	  fftw_real tmp271;
	  fftw_real tmp543;
	  fftw_real tmp406;
	  fftw_real tmp494;
	  fftw_real tmp546;
	  fftw_real tmp595;
	  fftw_real tmp401;
	  fftw_real tmp493;
	  fftw_real tmp319;
	  fftw_real tmp553;
	  fftw_real tmp558;
	  fftw_real tmp600;
	  fftw_real tmp425;
	  fftw_real tmp499;
	  fftw_real tmp434;
	  fftw_real tmp502;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp183;
	       fftw_real tmp621;
	       fftw_real tmp188;
	       fftw_real tmp620;
	       fftw_real tmp194;
	       fftw_real tmp370;
	       fftw_real tmp199;
	       fftw_real tmp371;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp183 = X[0];
	       tmp621 = Y[-31 * iostride];
	       {
		    fftw_real tmp185;
		    fftw_real tmp187;
		    fftw_real tmp184;
		    fftw_real tmp186;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp185 = X[16 * iostride];
		    tmp187 = Y[-15 * iostride];
		    tmp184 = c_re(W[15]);
		    tmp186 = c_im(W[15]);
		    tmp188 = (tmp184 * tmp185) - (tmp186 * tmp187);
		    tmp620 = (tmp186 * tmp185) + (tmp184 * tmp187);
	       }
	       {
		    fftw_real tmp191;
		    fftw_real tmp193;
		    fftw_real tmp190;
		    fftw_real tmp192;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp191 = X[8 * iostride];
		    tmp193 = Y[-23 * iostride];
		    tmp190 = c_re(W[7]);
		    tmp192 = c_im(W[7]);
		    tmp194 = (tmp190 * tmp191) - (tmp192 * tmp193);
		    tmp370 = (tmp192 * tmp191) + (tmp190 * tmp193);
	       }
	       {
		    fftw_real tmp196;
		    fftw_real tmp198;
		    fftw_real tmp195;
		    fftw_real tmp197;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp196 = X[24 * iostride];
		    tmp198 = Y[-7 * iostride];
		    tmp195 = c_re(W[23]);
		    tmp197 = c_im(W[23]);
		    tmp199 = (tmp195 * tmp196) - (tmp197 * tmp198);
		    tmp371 = (tmp197 * tmp196) + (tmp195 * tmp198);
	       }
	       {
		    fftw_real tmp189;
		    fftw_real tmp200;
		    fftw_real tmp651;
		    fftw_real tmp652;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp189 = tmp183 + tmp188;
		    tmp200 = tmp194 + tmp199;
		    tmp201 = tmp189 + tmp200;
		    tmp533 = tmp189 - tmp200;
		    tmp651 = tmp621 - tmp620;
		    tmp652 = tmp194 - tmp199;
		    tmp653 = tmp651 - tmp652;
		    tmp667 = tmp652 + tmp651;
	       }
	       {
		    fftw_real tmp619;
		    fftw_real tmp622;
		    fftw_real tmp369;
		    fftw_real tmp372;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp619 = tmp370 + tmp371;
		    tmp622 = tmp620 + tmp621;
		    tmp623 = tmp619 + tmp622;
		    tmp637 = tmp622 - tmp619;
		    tmp369 = tmp183 - tmp188;
		    tmp372 = tmp370 - tmp371;
		    tmp373 = tmp369 - tmp372;
		    tmp485 = tmp369 + tmp372;
	       }
	  }
	  {
	       fftw_real tmp325;
	       fftw_real tmp454;
	       fftw_real tmp341;
	       fftw_real tmp439;
	       fftw_real tmp330;
	       fftw_real tmp455;
	       fftw_real tmp336;
	       fftw_real tmp438;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp322;
		    fftw_real tmp324;
		    fftw_real tmp321;
		    fftw_real tmp323;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp322 = X[31 * iostride];
		    tmp324 = Y[0];
		    tmp321 = c_re(W[30]);
		    tmp323 = c_im(W[30]);
		    tmp325 = (tmp321 * tmp322) - (tmp323 * tmp324);
		    tmp454 = (tmp323 * tmp322) + (tmp321 * tmp324);
	       }
	       {
		    fftw_real tmp338;
		    fftw_real tmp340;
		    fftw_real tmp337;
		    fftw_real tmp339;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp338 = X[23 * iostride];
		    tmp340 = Y[-8 * iostride];
		    tmp337 = c_re(W[22]);
		    tmp339 = c_im(W[22]);
		    tmp341 = (tmp337 * tmp338) - (tmp339 * tmp340);
		    tmp439 = (tmp339 * tmp338) + (tmp337 * tmp340);
	       }
	       {
		    fftw_real tmp327;
		    fftw_real tmp329;
		    fftw_real tmp326;
		    fftw_real tmp328;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp327 = X[15 * iostride];
		    tmp329 = Y[-16 * iostride];
		    tmp326 = c_re(W[14]);
		    tmp328 = c_im(W[14]);
		    tmp330 = (tmp326 * tmp327) - (tmp328 * tmp329);
		    tmp455 = (tmp328 * tmp327) + (tmp326 * tmp329);
	       }
	       {
		    fftw_real tmp333;
		    fftw_real tmp335;
		    fftw_real tmp332;
		    fftw_real tmp334;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp333 = X[7 * iostride];
		    tmp335 = Y[-24 * iostride];
		    tmp332 = c_re(W[6]);
		    tmp334 = c_im(W[6]);
		    tmp336 = (tmp332 * tmp333) - (tmp334 * tmp335);
		    tmp438 = (tmp334 * tmp333) + (tmp332 * tmp335);
	       }
	       {
		    fftw_real tmp331;
		    fftw_real tmp342;
		    fftw_real tmp456;
		    fftw_real tmp457;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp331 = tmp325 + tmp330;
		    tmp342 = tmp336 + tmp341;
		    tmp343 = tmp331 + tmp342;
		    tmp561 = tmp331 - tmp342;
		    tmp456 = tmp454 - tmp455;
		    tmp457 = tmp336 - tmp341;
		    tmp458 = tmp456 + tmp457;
		    tmp508 = tmp456 - tmp457;
	       }
	       {
		    fftw_real tmp566;
		    fftw_real tmp567;
		    fftw_real tmp437;
		    fftw_real tmp440;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp566 = tmp454 + tmp455;
		    tmp567 = tmp438 + tmp439;
		    tmp568 = tmp566 - tmp567;
		    tmp604 = tmp566 + tmp567;
		    tmp437 = tmp325 - tmp330;
		    tmp440 = tmp438 - tmp439;
		    tmp441 = tmp437 - tmp440;
		    tmp505 = tmp437 + tmp440;
	       }
	  }
	  {
	       fftw_real tmp206;
	       fftw_real tmp374;
	       fftw_real tmp222;
	       fftw_real tmp381;
	       fftw_real tmp211;
	       fftw_real tmp375;
	       fftw_real tmp217;
	       fftw_real tmp380;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp203;
		    fftw_real tmp205;
		    fftw_real tmp202;
		    fftw_real tmp204;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp203 = X[4 * iostride];
		    tmp205 = Y[-27 * iostride];
		    tmp202 = c_re(W[3]);
		    tmp204 = c_im(W[3]);
		    tmp206 = (tmp202 * tmp203) - (tmp204 * tmp205);
		    tmp374 = (tmp204 * tmp203) + (tmp202 * tmp205);
	       }
	       {
		    fftw_real tmp219;
		    fftw_real tmp221;
		    fftw_real tmp218;
		    fftw_real tmp220;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp219 = X[12 * iostride];
		    tmp221 = Y[-19 * iostride];
		    tmp218 = c_re(W[11]);
		    tmp220 = c_im(W[11]);
		    tmp222 = (tmp218 * tmp219) - (tmp220 * tmp221);
		    tmp381 = (tmp220 * tmp219) + (tmp218 * tmp221);
	       }
	       {
		    fftw_real tmp208;
		    fftw_real tmp210;
		    fftw_real tmp207;
		    fftw_real tmp209;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp208 = X[20 * iostride];
		    tmp210 = Y[-11 * iostride];
		    tmp207 = c_re(W[19]);
		    tmp209 = c_im(W[19]);
		    tmp211 = (tmp207 * tmp208) - (tmp209 * tmp210);
		    tmp375 = (tmp209 * tmp208) + (tmp207 * tmp210);
	       }
	       {
		    fftw_real tmp214;
		    fftw_real tmp216;
		    fftw_real tmp213;
		    fftw_real tmp215;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp214 = X[28 * iostride];
		    tmp216 = Y[-3 * iostride];
		    tmp213 = c_re(W[27]);
		    tmp215 = c_im(W[27]);
		    tmp217 = (tmp213 * tmp214) - (tmp215 * tmp216);
		    tmp380 = (tmp215 * tmp214) + (tmp213 * tmp216);
	       }
	       {
		    fftw_real tmp212;
		    fftw_real tmp223;
		    fftw_real tmp379;
		    fftw_real tmp382;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp212 = tmp206 + tmp211;
		    tmp223 = tmp217 + tmp222;
		    tmp224 = tmp212 + tmp223;
		    tmp636 = tmp223 - tmp212;
		    tmp379 = tmp217 - tmp222;
		    tmp382 = tmp380 - tmp381;
		    tmp383 = tmp379 + tmp382;
		    tmp487 = tmp379 - tmp382;
	       }
	       {
		    fftw_real tmp534;
		    fftw_real tmp535;
		    fftw_real tmp376;
		    fftw_real tmp377;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp534 = tmp374 + tmp375;
		    tmp535 = tmp380 + tmp381;
		    tmp536 = tmp534 - tmp535;
		    tmp618 = tmp534 + tmp535;
		    tmp376 = tmp374 - tmp375;
		    tmp377 = tmp206 - tmp211;
		    tmp378 = tmp376 - tmp377;
		    tmp486 = tmp377 + tmp376;
	       }
	  }
	  {
	       fftw_real tmp348;
	       fftw_real tmp442;
	       fftw_real tmp353;
	       fftw_real tmp443;
	       fftw_real tmp444;
	       fftw_real tmp445;
	       fftw_real tmp359;
	       fftw_real tmp448;
	       fftw_real tmp364;
	       fftw_real tmp449;
	       fftw_real tmp447;
	       fftw_real tmp450;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp345;
		    fftw_real tmp347;
		    fftw_real tmp344;
		    fftw_real tmp346;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp345 = X[3 * iostride];
		    tmp347 = Y[-28 * iostride];
		    tmp344 = c_re(W[2]);
		    tmp346 = c_im(W[2]);
		    tmp348 = (tmp344 * tmp345) - (tmp346 * tmp347);
		    tmp442 = (tmp346 * tmp345) + (tmp344 * tmp347);
	       }
	       {
		    fftw_real tmp350;
		    fftw_real tmp352;
		    fftw_real tmp349;
		    fftw_real tmp351;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp350 = X[19 * iostride];
		    tmp352 = Y[-12 * iostride];
		    tmp349 = c_re(W[18]);
		    tmp351 = c_im(W[18]);
		    tmp353 = (tmp349 * tmp350) - (tmp351 * tmp352);
		    tmp443 = (tmp351 * tmp350) + (tmp349 * tmp352);
	       }
	       tmp444 = tmp442 - tmp443;
	       tmp445 = tmp348 - tmp353;
	       {
		    fftw_real tmp356;
		    fftw_real tmp358;
		    fftw_real tmp355;
		    fftw_real tmp357;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp356 = X[27 * iostride];
		    tmp358 = Y[-4 * iostride];
		    tmp355 = c_re(W[26]);
		    tmp357 = c_im(W[26]);
		    tmp359 = (tmp355 * tmp356) - (tmp357 * tmp358);
		    tmp448 = (tmp357 * tmp356) + (tmp355 * tmp358);
	       }
	       {
		    fftw_real tmp361;
		    fftw_real tmp363;
		    fftw_real tmp360;
		    fftw_real tmp362;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp361 = X[11 * iostride];
		    tmp363 = Y[-20 * iostride];
		    tmp360 = c_re(W[10]);
		    tmp362 = c_im(W[10]);
		    tmp364 = (tmp360 * tmp361) - (tmp362 * tmp363);
		    tmp449 = (tmp362 * tmp361) + (tmp360 * tmp363);
	       }
	       tmp447 = tmp359 - tmp364;
	       tmp450 = tmp448 - tmp449;
	       {
		    fftw_real tmp354;
		    fftw_real tmp365;
		    fftw_real tmp562;
		    fftw_real tmp563;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp354 = tmp348 + tmp353;
		    tmp365 = tmp359 + tmp364;
		    tmp366 = tmp354 + tmp365;
		    tmp569 = tmp365 - tmp354;
		    tmp562 = tmp442 + tmp443;
		    tmp563 = tmp448 + tmp449;
		    tmp564 = tmp562 - tmp563;
		    tmp605 = tmp562 + tmp563;
	       }
	       {
		    fftw_real tmp446;
		    fftw_real tmp451;
		    fftw_real tmp459;
		    fftw_real tmp460;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp446 = tmp444 - tmp445;
		    tmp451 = tmp447 + tmp450;
		    tmp452 = K707106781 * (tmp446 - tmp451);
		    tmp509 = K707106781 * (tmp446 + tmp451);
		    tmp459 = tmp447 - tmp450;
		    tmp460 = tmp445 + tmp444;
		    tmp461 = K707106781 * (tmp459 - tmp460);
		    tmp506 = K707106781 * (tmp460 + tmp459);
	       }
	  }
	  {
	       fftw_real tmp230;
	       fftw_real tmp386;
	       fftw_real tmp246;
	       fftw_real tmp393;
	       fftw_real tmp235;
	       fftw_real tmp387;
	       fftw_real tmp241;
	       fftw_real tmp392;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp227;
		    fftw_real tmp229;
		    fftw_real tmp226;
		    fftw_real tmp228;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp227 = X[2 * iostride];
		    tmp229 = Y[-29 * iostride];
		    tmp226 = c_re(W[1]);
		    tmp228 = c_im(W[1]);
		    tmp230 = (tmp226 * tmp227) - (tmp228 * tmp229);
		    tmp386 = (tmp228 * tmp227) + (tmp226 * tmp229);
	       }
	       {
		    fftw_real tmp243;
		    fftw_real tmp245;
		    fftw_real tmp242;
		    fftw_real tmp244;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp243 = X[26 * iostride];
		    tmp245 = Y[-5 * iostride];
		    tmp242 = c_re(W[25]);
		    tmp244 = c_im(W[25]);
		    tmp246 = (tmp242 * tmp243) - (tmp244 * tmp245);
		    tmp393 = (tmp244 * tmp243) + (tmp242 * tmp245);
	       }
	       {
		    fftw_real tmp232;
		    fftw_real tmp234;
		    fftw_real tmp231;
		    fftw_real tmp233;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp232 = X[18 * iostride];
		    tmp234 = Y[-13 * iostride];
		    tmp231 = c_re(W[17]);
		    tmp233 = c_im(W[17]);
		    tmp235 = (tmp231 * tmp232) - (tmp233 * tmp234);
		    tmp387 = (tmp233 * tmp232) + (tmp231 * tmp234);
	       }
	       {
		    fftw_real tmp238;
		    fftw_real tmp240;
		    fftw_real tmp237;
		    fftw_real tmp239;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp238 = X[10 * iostride];
		    tmp240 = Y[-21 * iostride];
		    tmp237 = c_re(W[9]);
		    tmp239 = c_im(W[9]);
		    tmp241 = (tmp237 * tmp238) - (tmp239 * tmp240);
		    tmp392 = (tmp239 * tmp238) + (tmp237 * tmp240);
	       }
	       {
		    fftw_real tmp236;
		    fftw_real tmp247;
		    fftw_real tmp391;
		    fftw_real tmp394;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp236 = tmp230 + tmp235;
		    tmp247 = tmp241 + tmp246;
		    tmp248 = tmp236 + tmp247;
		    tmp541 = tmp236 - tmp247;
		    tmp391 = tmp230 - tmp235;
		    tmp394 = tmp392 - tmp393;
		    tmp395 = tmp391 - tmp394;
		    tmp491 = tmp391 + tmp394;
	       }
	       {
		    fftw_real tmp538;
		    fftw_real tmp539;
		    fftw_real tmp388;
		    fftw_real tmp389;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp538 = tmp386 + tmp387;
		    tmp539 = tmp392 + tmp393;
		    tmp540 = tmp538 - tmp539;
		    tmp594 = tmp538 + tmp539;
		    tmp388 = tmp386 - tmp387;
		    tmp389 = tmp241 - tmp246;
		    tmp390 = tmp388 + tmp389;
		    tmp490 = tmp388 - tmp389;
	       }
	  }
	  {
	       fftw_real tmp278;
	       fftw_real tmp410;
	       fftw_real tmp294;
	       fftw_real tmp429;
	       fftw_real tmp283;
	       fftw_real tmp411;
	       fftw_real tmp289;
	       fftw_real tmp428;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp275;
		    fftw_real tmp277;
		    fftw_real tmp274;
		    fftw_real tmp276;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp275 = X[iostride];
		    tmp277 = Y[-30 * iostride];
		    tmp274 = c_re(W[0]);
		    tmp276 = c_im(W[0]);
		    tmp278 = (tmp274 * tmp275) - (tmp276 * tmp277);
		    tmp410 = (tmp276 * tmp275) + (tmp274 * tmp277);
	       }
	       {
		    fftw_real tmp291;
		    fftw_real tmp293;
		    fftw_real tmp290;
		    fftw_real tmp292;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp291 = X[25 * iostride];
		    tmp293 = Y[-6 * iostride];
		    tmp290 = c_re(W[24]);
		    tmp292 = c_im(W[24]);
		    tmp294 = (tmp290 * tmp291) - (tmp292 * tmp293);
		    tmp429 = (tmp292 * tmp291) + (tmp290 * tmp293);
	       }
	       {
		    fftw_real tmp280;
		    fftw_real tmp282;
		    fftw_real tmp279;
		    fftw_real tmp281;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp280 = X[17 * iostride];
		    tmp282 = Y[-14 * iostride];
		    tmp279 = c_re(W[16]);
		    tmp281 = c_im(W[16]);
		    tmp283 = (tmp279 * tmp280) - (tmp281 * tmp282);
		    tmp411 = (tmp281 * tmp280) + (tmp279 * tmp282);
	       }
	       {
		    fftw_real tmp286;
		    fftw_real tmp288;
		    fftw_real tmp285;
		    fftw_real tmp287;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp286 = X[9 * iostride];
		    tmp288 = Y[-22 * iostride];
		    tmp285 = c_re(W[8]);
		    tmp287 = c_im(W[8]);
		    tmp289 = (tmp285 * tmp286) - (tmp287 * tmp288);
		    tmp428 = (tmp287 * tmp286) + (tmp285 * tmp288);
	       }
	       {
		    fftw_real tmp284;
		    fftw_real tmp295;
		    fftw_real tmp427;
		    fftw_real tmp430;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp284 = tmp278 + tmp283;
		    tmp295 = tmp289 + tmp294;
		    tmp296 = tmp284 + tmp295;
		    tmp555 = tmp284 - tmp295;
		    tmp427 = tmp278 - tmp283;
		    tmp430 = tmp428 - tmp429;
		    tmp431 = tmp427 - tmp430;
		    tmp498 = tmp427 + tmp430;
	       }
	       {
		    fftw_real tmp550;
		    fftw_real tmp551;
		    fftw_real tmp412;
		    fftw_real tmp413;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp550 = tmp410 + tmp411;
		    tmp551 = tmp428 + tmp429;
		    tmp552 = tmp550 - tmp551;
		    tmp599 = tmp550 + tmp551;
		    tmp412 = tmp410 - tmp411;
		    tmp413 = tmp289 - tmp294;
		    tmp414 = tmp412 + tmp413;
		    tmp501 = tmp412 - tmp413;
	       }
	  }
	  {
	       fftw_real tmp253;
	       fftw_real tmp397;
	       fftw_real tmp269;
	       fftw_real tmp404;
	       fftw_real tmp258;
	       fftw_real tmp398;
	       fftw_real tmp264;
	       fftw_real tmp403;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp250;
		    fftw_real tmp252;
		    fftw_real tmp249;
		    fftw_real tmp251;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp250 = X[30 * iostride];
		    tmp252 = Y[-iostride];
		    tmp249 = c_re(W[29]);
		    tmp251 = c_im(W[29]);
		    tmp253 = (tmp249 * tmp250) - (tmp251 * tmp252);
		    tmp397 = (tmp251 * tmp250) + (tmp249 * tmp252);
	       }
	       {
		    fftw_real tmp266;
		    fftw_real tmp268;
		    fftw_real tmp265;
		    fftw_real tmp267;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp266 = X[22 * iostride];
		    tmp268 = Y[-9 * iostride];
		    tmp265 = c_re(W[21]);
		    tmp267 = c_im(W[21]);
		    tmp269 = (tmp265 * tmp266) - (tmp267 * tmp268);
		    tmp404 = (tmp267 * tmp266) + (tmp265 * tmp268);
	       }
	       {
		    fftw_real tmp255;
		    fftw_real tmp257;
		    fftw_real tmp254;
		    fftw_real tmp256;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp255 = X[14 * iostride];
		    tmp257 = Y[-17 * iostride];
		    tmp254 = c_re(W[13]);
		    tmp256 = c_im(W[13]);
		    tmp258 = (tmp254 * tmp255) - (tmp256 * tmp257);
		    tmp398 = (tmp256 * tmp255) + (tmp254 * tmp257);
	       }
	       {
		    fftw_real tmp261;
		    fftw_real tmp263;
		    fftw_real tmp260;
		    fftw_real tmp262;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp261 = X[6 * iostride];
		    tmp263 = Y[-25 * iostride];
		    tmp260 = c_re(W[5]);
		    tmp262 = c_im(W[5]);
		    tmp264 = (tmp260 * tmp261) - (tmp262 * tmp263);
		    tmp403 = (tmp262 * tmp261) + (tmp260 * tmp263);
	       }
	       {
		    fftw_real tmp259;
		    fftw_real tmp270;
		    fftw_real tmp402;
		    fftw_real tmp405;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp259 = tmp253 + tmp258;
		    tmp270 = tmp264 + tmp269;
		    tmp271 = tmp259 + tmp270;
		    tmp543 = tmp259 - tmp270;
		    tmp402 = tmp253 - tmp258;
		    tmp405 = tmp403 - tmp404;
		    tmp406 = tmp402 - tmp405;
		    tmp494 = tmp402 + tmp405;
	       }
	       {
		    fftw_real tmp544;
		    fftw_real tmp545;
		    fftw_real tmp399;
		    fftw_real tmp400;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp544 = tmp397 + tmp398;
		    tmp545 = tmp403 + tmp404;
		    tmp546 = tmp544 - tmp545;
		    tmp595 = tmp544 + tmp545;
		    tmp399 = tmp397 - tmp398;
		    tmp400 = tmp264 - tmp269;
		    tmp401 = tmp399 + tmp400;
		    tmp493 = tmp399 - tmp400;
	       }
	  }
	  {
	       fftw_real tmp301;
	       fftw_real tmp421;
	       fftw_real tmp306;
	       fftw_real tmp422;
	       fftw_real tmp420;
	       fftw_real tmp423;
	       fftw_real tmp312;
	       fftw_real tmp416;
	       fftw_real tmp317;
	       fftw_real tmp417;
	       fftw_real tmp415;
	       fftw_real tmp418;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp298;
		    fftw_real tmp300;
		    fftw_real tmp297;
		    fftw_real tmp299;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp298 = X[5 * iostride];
		    tmp300 = Y[-26 * iostride];
		    tmp297 = c_re(W[4]);
		    tmp299 = c_im(W[4]);
		    tmp301 = (tmp297 * tmp298) - (tmp299 * tmp300);
		    tmp421 = (tmp299 * tmp298) + (tmp297 * tmp300);
	       }
	       {
		    fftw_real tmp303;
		    fftw_real tmp305;
		    fftw_real tmp302;
		    fftw_real tmp304;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp303 = X[21 * iostride];
		    tmp305 = Y[-10 * iostride];
		    tmp302 = c_re(W[20]);
		    tmp304 = c_im(W[20]);
		    tmp306 = (tmp302 * tmp303) - (tmp304 * tmp305);
		    tmp422 = (tmp304 * tmp303) + (tmp302 * tmp305);
	       }
	       tmp420 = tmp301 - tmp306;
	       tmp423 = tmp421 - tmp422;
	       {
		    fftw_real tmp309;
		    fftw_real tmp311;
		    fftw_real tmp308;
		    fftw_real tmp310;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp309 = X[29 * iostride];
		    tmp311 = Y[-2 * iostride];
		    tmp308 = c_re(W[28]);
		    tmp310 = c_im(W[28]);
		    tmp312 = (tmp308 * tmp309) - (tmp310 * tmp311);
		    tmp416 = (tmp310 * tmp309) + (tmp308 * tmp311);
	       }
	       {
		    fftw_real tmp314;
		    fftw_real tmp316;
		    fftw_real tmp313;
		    fftw_real tmp315;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp314 = X[13 * iostride];
		    tmp316 = Y[-18 * iostride];
		    tmp313 = c_re(W[12]);
		    tmp315 = c_im(W[12]);
		    tmp317 = (tmp313 * tmp314) - (tmp315 * tmp316);
		    tmp417 = (tmp315 * tmp314) + (tmp313 * tmp316);
	       }
	       tmp415 = tmp312 - tmp317;
	       tmp418 = tmp416 - tmp417;
	       {
		    fftw_real tmp307;
		    fftw_real tmp318;
		    fftw_real tmp556;
		    fftw_real tmp557;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp307 = tmp301 + tmp306;
		    tmp318 = tmp312 + tmp317;
		    tmp319 = tmp307 + tmp318;
		    tmp553 = tmp318 - tmp307;
		    tmp556 = tmp421 + tmp422;
		    tmp557 = tmp416 + tmp417;
		    tmp558 = tmp556 - tmp557;
		    tmp600 = tmp556 + tmp557;
	       }
	       {
		    fftw_real tmp419;
		    fftw_real tmp424;
		    fftw_real tmp432;
		    fftw_real tmp433;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp419 = tmp415 - tmp418;
		    tmp424 = tmp420 + tmp423;
		    tmp425 = K707106781 * (tmp419 - tmp424);
		    tmp499 = K707106781 * (tmp424 + tmp419);
		    tmp432 = tmp423 - tmp420;
		    tmp433 = tmp415 + tmp418;
		    tmp434 = K707106781 * (tmp432 - tmp433);
		    tmp502 = K707106781 * (tmp432 + tmp433);
	       }
	  }
	  {
	       fftw_real tmp273;
	       fftw_real tmp613;
	       fftw_real tmp625;
	       fftw_real tmp627;
	       fftw_real tmp368;
	       fftw_real tmp628;
	       fftw_real tmp616;
	       fftw_real tmp626;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp225;
		    fftw_real tmp272;
		    fftw_real tmp617;
		    fftw_real tmp624;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp225 = tmp201 + tmp224;
		    tmp272 = tmp248 + tmp271;
		    tmp273 = tmp225 + tmp272;
		    tmp613 = tmp225 - tmp272;
		    tmp617 = tmp594 + tmp595;
		    tmp624 = tmp618 + tmp623;
		    tmp625 = tmp617 + tmp624;
		    tmp627 = tmp624 - tmp617;
	       }
	       {
		    fftw_real tmp320;
		    fftw_real tmp367;
		    fftw_real tmp614;
		    fftw_real tmp615;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp320 = tmp296 + tmp319;
		    tmp367 = tmp343 + tmp366;
		    tmp368 = tmp320 + tmp367;
		    tmp628 = tmp367 - tmp320;
		    tmp614 = tmp599 + tmp600;
		    tmp615 = tmp604 + tmp605;
		    tmp616 = tmp614 - tmp615;
		    tmp626 = tmp614 + tmp615;
	       }
	       Y[-16 * iostride] = tmp273 - tmp368;
	       X[0] = tmp273 + tmp368;
	       Y[-24 * iostride] = tmp613 - tmp616;
	       X[8 * iostride] = tmp613 + tmp616;
	       X[16 * iostride] = -(tmp625 - tmp626);
	       Y[0] = tmp626 + tmp625;
	       X[24 * iostride] = -(tmp627 - tmp628);
	       Y[-8 * iostride] = tmp628 + tmp627;
	  }
	  {
	       fftw_real tmp597;
	       fftw_real tmp609;
	       fftw_real tmp631;
	       fftw_real tmp633;
	       fftw_real tmp602;
	       fftw_real tmp610;
	       fftw_real tmp607;
	       fftw_real tmp611;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp593;
		    fftw_real tmp596;
		    fftw_real tmp629;
		    fftw_real tmp630;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp593 = tmp201 - tmp224;
		    tmp596 = tmp594 - tmp595;
		    tmp597 = tmp593 + tmp596;
		    tmp609 = tmp593 - tmp596;
		    tmp629 = tmp271 - tmp248;
		    tmp630 = tmp623 - tmp618;
		    tmp631 = tmp629 + tmp630;
		    tmp633 = tmp630 - tmp629;
	       }
	       {
		    fftw_real tmp598;
		    fftw_real tmp601;
		    fftw_real tmp603;
		    fftw_real tmp606;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp598 = tmp296 - tmp319;
		    tmp601 = tmp599 - tmp600;
		    tmp602 = tmp598 + tmp601;
		    tmp610 = tmp601 - tmp598;
		    tmp603 = tmp343 - tmp366;
		    tmp606 = tmp604 - tmp605;
		    tmp607 = tmp603 - tmp606;
		    tmp611 = tmp603 + tmp606;
	       }
	       {
		    fftw_real tmp608;
		    fftw_real tmp634;
		    fftw_real tmp612;
		    fftw_real tmp632;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp608 = K707106781 * (tmp602 + tmp607);
		    Y[-20 * iostride] = tmp597 - tmp608;
		    X[4 * iostride] = tmp597 + tmp608;
		    tmp634 = K707106781 * (tmp607 - tmp602);
		    X[28 * iostride] = -(tmp633 - tmp634);
		    Y[-12 * iostride] = tmp634 + tmp633;
		    tmp612 = K707106781 * (tmp610 - tmp611);
		    Y[-28 * iostride] = tmp609 - tmp612;
		    X[12 * iostride] = tmp609 + tmp612;
		    tmp632 = K707106781 * (tmp610 + tmp611);
		    X[20 * iostride] = -(tmp631 - tmp632);
		    Y[-4 * iostride] = tmp632 + tmp631;
	       }
	  }
	  {
	       fftw_real tmp537;
	       fftw_real tmp577;
	       fftw_real tmp548;
	       fftw_real tmp635;
	       fftw_real tmp580;
	       fftw_real tmp643;
	       fftw_real tmp560;
	       fftw_real tmp574;
	       fftw_real tmp638;
	       fftw_real tmp644;
	       fftw_real tmp584;
	       fftw_real tmp590;
	       fftw_real tmp571;
	       fftw_real tmp575;
	       fftw_real tmp587;
	       fftw_real tmp591;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp542;
		    fftw_real tmp547;
		    fftw_real tmp582;
		    fftw_real tmp583;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp537 = tmp533 - tmp536;
		    tmp577 = tmp533 + tmp536;
		    tmp542 = tmp540 - tmp541;
		    tmp547 = tmp543 + tmp546;
		    tmp548 = K707106781 * (tmp542 - tmp547);
		    tmp635 = K707106781 * (tmp542 + tmp547);
		    {
			 fftw_real tmp578;
			 fftw_real tmp579;
			 fftw_real tmp554;
			 fftw_real tmp559;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp578 = tmp541 + tmp540;
			 tmp579 = tmp543 - tmp546;
			 tmp580 = K707106781 * (tmp578 + tmp579);
			 tmp643 = K707106781 * (tmp579 - tmp578);
			 tmp554 = tmp552 - tmp553;
			 tmp559 = tmp555 - tmp558;
			 tmp560 = (K923879532 * tmp554) + (K382683432 * tmp559);
			 tmp574 = (K382683432 * tmp554) - (K923879532 * tmp559);
		    }
		    tmp638 = tmp636 + tmp637;
		    tmp644 = tmp637 - tmp636;
		    tmp582 = tmp552 + tmp553;
		    tmp583 = tmp555 + tmp558;
		    tmp584 = (K382683432 * tmp582) + (K923879532 * tmp583);
		    tmp590 = (K923879532 * tmp582) - (K382683432 * tmp583);
		    {
			 fftw_real tmp565;
			 fftw_real tmp570;
			 fftw_real tmp585;
			 fftw_real tmp586;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp565 = tmp561 - tmp564;
			 tmp570 = tmp568 - tmp569;
			 tmp571 = (K382683432 * tmp565) - (K923879532 * tmp570);
			 tmp575 = (K382683432 * tmp570) + (K923879532 * tmp565);
			 tmp585 = tmp561 + tmp564;
			 tmp586 = tmp568 + tmp569;
			 tmp587 = (K923879532 * tmp585) - (K382683432 * tmp586);
			 tmp591 = (K923879532 * tmp586) + (K382683432 * tmp585);
		    }
	       }
	       {
		    fftw_real tmp549;
		    fftw_real tmp572;
		    fftw_real tmp573;
		    fftw_real tmp576;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp549 = tmp537 + tmp548;
		    tmp572 = tmp560 + tmp571;
		    Y[-22 * iostride] = tmp549 - tmp572;
		    X[6 * iostride] = tmp549 + tmp572;
		    tmp573 = tmp537 - tmp548;
		    tmp576 = tmp574 - tmp575;
		    Y[-30 * iostride] = tmp573 - tmp576;
		    X[14 * iostride] = tmp573 + tmp576;
	       }
	       {
		    fftw_real tmp645;
		    fftw_real tmp646;
		    fftw_real tmp647;
		    fftw_real tmp648;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp645 = tmp643 + tmp644;
		    tmp646 = tmp574 + tmp575;
		    X[22 * iostride] = -(tmp645 - tmp646);
		    Y[-6 * iostride] = tmp646 + tmp645;
		    tmp647 = tmp644 - tmp643;
		    tmp648 = tmp571 - tmp560;
		    X[30 * iostride] = -(tmp647 - tmp648);
		    Y[-14 * iostride] = tmp648 + tmp647;
	       }
	       {
		    fftw_real tmp581;
		    fftw_real tmp588;
		    fftw_real tmp589;
		    fftw_real tmp592;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp581 = tmp577 + tmp580;
		    tmp588 = tmp584 + tmp587;
		    Y[-18 * iostride] = tmp581 - tmp588;
		    X[2 * iostride] = tmp581 + tmp588;
		    tmp589 = tmp577 - tmp580;
		    tmp592 = tmp590 - tmp591;
		    Y[-26 * iostride] = tmp589 - tmp592;
		    X[10 * iostride] = tmp589 + tmp592;
	       }
	       {
		    fftw_real tmp639;
		    fftw_real tmp640;
		    fftw_real tmp641;
		    fftw_real tmp642;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp639 = tmp635 + tmp638;
		    tmp640 = tmp590 + tmp591;
		    X[18 * iostride] = -(tmp639 - tmp640);
		    Y[-2 * iostride] = tmp640 + tmp639;
		    tmp641 = tmp638 - tmp635;
		    tmp642 = tmp587 - tmp584;
		    X[26 * iostride] = -(tmp641 - tmp642);
		    Y[-10 * iostride] = tmp642 + tmp641;
	       }
	  }
	  {
	       fftw_real tmp489;
	       fftw_real tmp517;
	       fftw_real tmp520;
	       fftw_real tmp659;
	       fftw_real tmp654;
	       fftw_real tmp660;
	       fftw_real tmp496;
	       fftw_real tmp649;
	       fftw_real tmp504;
	       fftw_real tmp514;
	       fftw_real tmp524;
	       fftw_real tmp530;
	       fftw_real tmp511;
	       fftw_real tmp515;
	       fftw_real tmp527;
	       fftw_real tmp531;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp488;
		    fftw_real tmp518;
		    fftw_real tmp519;
		    fftw_real tmp650;
		    fftw_real tmp492;
		    fftw_real tmp495;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp488 = K707106781 * (tmp486 + tmp487);
		    tmp489 = tmp485 - tmp488;
		    tmp517 = tmp485 + tmp488;
		    tmp518 = (K382683432 * tmp490) + (K923879532 * tmp491);
		    tmp519 = (K923879532 * tmp494) - (K382683432 * tmp493);
		    tmp520 = tmp518 + tmp519;
		    tmp659 = tmp519 - tmp518;
		    tmp650 = K707106781 * (tmp378 + tmp383);
		    tmp654 = tmp650 + tmp653;
		    tmp660 = tmp653 - tmp650;
		    tmp492 = (K923879532 * tmp490) - (K382683432 * tmp491);
		    tmp495 = (K923879532 * tmp493) + (K382683432 * tmp494);
		    tmp496 = tmp492 - tmp495;
		    tmp649 = tmp492 + tmp495;
	       }
	       {
		    fftw_real tmp500;
		    fftw_real tmp503;
		    fftw_real tmp522;
		    fftw_real tmp523;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp500 = tmp498 - tmp499;
		    tmp503 = tmp501 - tmp502;
		    tmp504 = (K555570233 * tmp500) + (K831469612 * tmp503);
		    tmp514 = (K555570233 * tmp503) - (K831469612 * tmp500);
		    tmp522 = tmp498 + tmp499;
		    tmp523 = tmp501 + tmp502;
		    tmp524 = (K980785280 * tmp522) + (K195090322 * tmp523);
		    tmp530 = (K980785280 * tmp523) - (K195090322 * tmp522);
	       }
	       {
		    fftw_real tmp507;
		    fftw_real tmp510;
		    fftw_real tmp525;
		    fftw_real tmp526;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp507 = tmp505 - tmp506;
		    tmp510 = tmp508 - tmp509;
		    tmp511 = (K555570233 * tmp507) - (K831469612 * tmp510);
		    tmp515 = (K831469612 * tmp507) + (K555570233 * tmp510);
		    tmp525 = tmp505 + tmp506;
		    tmp526 = tmp508 + tmp509;
		    tmp527 = (K980785280 * tmp525) - (K195090322 * tmp526);
		    tmp531 = (K195090322 * tmp525) + (K980785280 * tmp526);
	       }
	       {
		    fftw_real tmp497;
		    fftw_real tmp512;
		    fftw_real tmp513;
		    fftw_real tmp516;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp497 = tmp489 + tmp496;
		    tmp512 = tmp504 + tmp511;
		    Y[-21 * iostride] = tmp497 - tmp512;
		    X[5 * iostride] = tmp497 + tmp512;
		    tmp513 = tmp489 - tmp496;
		    tmp516 = tmp514 - tmp515;
		    Y[-29 * iostride] = tmp513 - tmp516;
		    X[13 * iostride] = tmp513 + tmp516;
	       }
	       {
		    fftw_real tmp661;
		    fftw_real tmp662;
		    fftw_real tmp663;
		    fftw_real tmp664;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp661 = tmp659 + tmp660;
		    tmp662 = tmp514 + tmp515;
		    X[21 * iostride] = -(tmp661 - tmp662);
		    Y[-5 * iostride] = tmp662 + tmp661;
		    tmp663 = tmp660 - tmp659;
		    tmp664 = tmp511 - tmp504;
		    X[29 * iostride] = -(tmp663 - tmp664);
		    Y[-13 * iostride] = tmp664 + tmp663;
	       }
	       {
		    fftw_real tmp521;
		    fftw_real tmp528;
		    fftw_real tmp529;
		    fftw_real tmp532;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp521 = tmp517 + tmp520;
		    tmp528 = tmp524 + tmp527;
		    Y[-17 * iostride] = tmp521 - tmp528;
		    X[iostride] = tmp521 + tmp528;
		    tmp529 = tmp517 - tmp520;
		    tmp532 = tmp530 - tmp531;
		    Y[-25 * iostride] = tmp529 - tmp532;
		    X[9 * iostride] = tmp529 + tmp532;
	       }
	       {
		    fftw_real tmp655;
		    fftw_real tmp656;
		    fftw_real tmp657;
		    fftw_real tmp658;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp655 = tmp649 + tmp654;
		    tmp656 = tmp530 + tmp531;
		    X[17 * iostride] = -(tmp655 - tmp656);
		    Y[-iostride] = tmp656 + tmp655;
		    tmp657 = tmp654 - tmp649;
		    tmp658 = tmp527 - tmp524;
		    X[25 * iostride] = -(tmp657 - tmp658);
		    Y[-9 * iostride] = tmp658 + tmp657;
	       }
	  }
	  {
	       fftw_real tmp385;
	       fftw_real tmp469;
	       fftw_real tmp472;
	       fftw_real tmp673;
	       fftw_real tmp668;
	       fftw_real tmp674;
	       fftw_real tmp408;
	       fftw_real tmp665;
	       fftw_real tmp436;
	       fftw_real tmp466;
	       fftw_real tmp476;
	       fftw_real tmp482;
	       fftw_real tmp463;
	       fftw_real tmp467;
	       fftw_real tmp479;
	       fftw_real tmp483;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp384;
		    fftw_real tmp470;
		    fftw_real tmp471;
		    fftw_real tmp666;
		    fftw_real tmp396;
		    fftw_real tmp407;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp384 = K707106781 * (tmp378 - tmp383);
		    tmp385 = tmp373 - tmp384;
		    tmp469 = tmp373 + tmp384;
		    tmp470 = (K923879532 * tmp390) + (K382683432 * tmp395);
		    tmp471 = (K382683432 * tmp406) - (K923879532 * tmp401);
		    tmp472 = tmp470 + tmp471;
		    tmp673 = tmp471 - tmp470;
		    tmp666 = K707106781 * (tmp487 - tmp486);
		    tmp668 = tmp666 + tmp667;
		    tmp674 = tmp667 - tmp666;
		    tmp396 = (K382683432 * tmp390) - (K923879532 * tmp395);
		    tmp407 = (K382683432 * tmp401) + (K923879532 * tmp406);
		    tmp408 = tmp396 - tmp407;
		    tmp665 = tmp396 + tmp407;
	       }
	       {
		    fftw_real tmp426;
		    fftw_real tmp435;
		    fftw_real tmp474;
		    fftw_real tmp475;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp426 = tmp414 - tmp425;
		    tmp435 = tmp431 - tmp434;
		    tmp436 = (K980785280 * tmp426) + (K195090322 * tmp435);
		    tmp466 = (K195090322 * tmp426) - (K980785280 * tmp435);
		    tmp474 = tmp414 + tmp425;
		    tmp475 = tmp431 + tmp434;
		    tmp476 = (K555570233 * tmp474) + (K831469612 * tmp475);
		    tmp482 = (K831469612 * tmp474) - (K555570233 * tmp475);
	       }
	       {
		    fftw_real tmp453;
		    fftw_real tmp462;
		    fftw_real tmp477;
		    fftw_real tmp478;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp453 = tmp441 - tmp452;
		    tmp462 = tmp458 - tmp461;
		    tmp463 = (K195090322 * tmp453) - (K980785280 * tmp462);
		    tmp467 = (K195090322 * tmp462) + (K980785280 * tmp453);
		    tmp477 = tmp441 + tmp452;
		    tmp478 = tmp458 + tmp461;
		    tmp479 = (K831469612 * tmp477) - (K555570233 * tmp478);
		    tmp483 = (K831469612 * tmp478) + (K555570233 * tmp477);
	       }
	       {
		    fftw_real tmp409;
		    fftw_real tmp464;
		    fftw_real tmp465;
		    fftw_real tmp468;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp409 = tmp385 + tmp408;
		    tmp464 = tmp436 + tmp463;
		    Y[-23 * iostride] = tmp409 - tmp464;
		    X[7 * iostride] = tmp409 + tmp464;
		    tmp465 = tmp385 - tmp408;
		    tmp468 = tmp466 - tmp467;
		    Y[-31 * iostride] = tmp465 - tmp468;
		    X[15 * iostride] = tmp465 + tmp468;
	       }
	       {
		    fftw_real tmp675;
		    fftw_real tmp676;
		    fftw_real tmp677;
		    fftw_real tmp678;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp675 = tmp673 + tmp674;
		    tmp676 = tmp466 + tmp467;
		    X[23 * iostride] = -(tmp675 - tmp676);
		    Y[-7 * iostride] = tmp676 + tmp675;
		    tmp677 = tmp674 - tmp673;
		    tmp678 = tmp463 - tmp436;
		    X[31 * iostride] = -(tmp677 - tmp678);
		    Y[-15 * iostride] = tmp678 + tmp677;
	       }
	       {
		    fftw_real tmp473;
		    fftw_real tmp480;
		    fftw_real tmp481;
		    fftw_real tmp484;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp473 = tmp469 + tmp472;
		    tmp480 = tmp476 + tmp479;
		    Y[-19 * iostride] = tmp473 - tmp480;
		    X[3 * iostride] = tmp473 + tmp480;
		    tmp481 = tmp469 - tmp472;
		    tmp484 = tmp482 - tmp483;
		    Y[-27 * iostride] = tmp481 - tmp484;
		    X[11 * iostride] = tmp481 + tmp484;
	       }
	       {
		    fftw_real tmp669;
		    fftw_real tmp670;
		    fftw_real tmp671;
		    fftw_real tmp672;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp669 = tmp665 + tmp668;
		    tmp670 = tmp482 + tmp483;
		    X[19 * iostride] = -(tmp669 - tmp670);
		    Y[-3 * iostride] = tmp670 + tmp669;
		    tmp671 = tmp668 - tmp665;
		    tmp672 = tmp479 - tmp476;
		    X[27 * iostride] = -(tmp671 - tmp672);
		    Y[-11 * iostride] = tmp672 + tmp671;
	       }
	  }
     }
     if (i == m) {
	  fftw_real tmp5;
	  fftw_real tmp105;
	  fftw_real tmp158;
	  fftw_real tmp171;
	  fftw_real tmp12;
	  fftw_real tmp170;
	  fftw_real tmp108;
	  fftw_real tmp155;
	  fftw_real tmp74;
	  fftw_real tmp97;
	  fftw_real tmp130;
	  fftw_real tmp146;
	  fftw_real tmp82;
	  fftw_real tmp98;
	  fftw_real tmp127;
	  fftw_real tmp145;
	  fftw_real tmp24;
	  fftw_real tmp90;
	  fftw_real tmp115;
	  fftw_real tmp138;
	  fftw_real tmp35;
	  fftw_real tmp91;
	  fftw_real tmp112;
	  fftw_real tmp139;
	  fftw_real tmp51;
	  fftw_real tmp94;
	  fftw_real tmp123;
	  fftw_real tmp143;
	  fftw_real tmp59;
	  fftw_real tmp95;
	  fftw_real tmp120;
	  fftw_real tmp142;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp1;
	       fftw_real tmp157;
	       fftw_real tmp4;
	       fftw_real tmp156;
	       fftw_real tmp2;
	       fftw_real tmp3;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp1 = X[0];
	       tmp157 = X[16 * iostride];
	       tmp2 = X[8 * iostride];
	       tmp3 = X[24 * iostride];
	       tmp4 = K707106781 * (tmp2 - tmp3);
	       tmp156 = K707106781 * (tmp2 + tmp3);
	       tmp5 = tmp1 + tmp4;
	       tmp105 = tmp1 - tmp4;
	       tmp158 = tmp156 + tmp157;
	       tmp171 = tmp157 - tmp156;
	  }
	  {
	       fftw_real tmp8;
	       fftw_real tmp106;
	       fftw_real tmp11;
	       fftw_real tmp107;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp6;
		    fftw_real tmp7;
		    fftw_real tmp9;
		    fftw_real tmp10;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp6 = X[4 * iostride];
		    tmp7 = X[20 * iostride];
		    tmp8 = (K923879532 * tmp6) - (K382683432 * tmp7);
		    tmp106 = (K382683432 * tmp6) + (K923879532 * tmp7);
		    tmp9 = X[12 * iostride];
		    tmp10 = X[28 * iostride];
		    tmp11 = (K382683432 * tmp9) - (K923879532 * tmp10);
		    tmp107 = (K923879532 * tmp9) + (K382683432 * tmp10);
	       }
	       tmp12 = tmp8 + tmp11;
	       tmp170 = tmp11 - tmp8;
	       tmp108 = tmp106 - tmp107;
	       tmp155 = tmp106 + tmp107;
	  }
	  {
	       fftw_real tmp65;
	       fftw_real tmp80;
	       fftw_real tmp63;
	       fftw_real tmp78;
	       fftw_real tmp69;
	       fftw_real tmp75;
	       fftw_real tmp72;
	       fftw_real tmp76;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp64;
		    fftw_real tmp79;
		    fftw_real tmp61;
		    fftw_real tmp62;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp64 = X[31 * iostride];
		    tmp65 = K2_000000000 * tmp64;
		    tmp79 = X[15 * iostride];
		    tmp80 = K2_000000000 * tmp79;
		    tmp61 = X[7 * iostride];
		    tmp62 = X[23 * iostride];
		    tmp63 = K1_414213562 * (tmp61 - tmp62);
		    tmp78 = K1_414213562 * (tmp61 + tmp62);
		    {
			 fftw_real tmp67;
			 fftw_real tmp68;
			 fftw_real tmp70;
			 fftw_real tmp71;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp67 = X[3 * iostride];
			 tmp68 = X[19 * iostride];
			 tmp69 = (K1_847759065 * tmp67) - (K765366864 * tmp68);
			 tmp75 = (K765366864 * tmp67) + (K1_847759065 * tmp68);
			 tmp70 = X[11 * iostride];
			 tmp71 = X[27 * iostride];
			 tmp72 = (K765366864 * tmp70) - (K1_847759065 * tmp71);
			 tmp76 = (K1_847759065 * tmp70) + (K765366864 * tmp71);
		    }
	       }
	       {
		    fftw_real tmp66;
		    fftw_real tmp73;
		    fftw_real tmp128;
		    fftw_real tmp129;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp66 = tmp63 - tmp65;
		    tmp73 = tmp69 + tmp72;
		    tmp74 = tmp66 + tmp73;
		    tmp97 = tmp66 - tmp73;
		    tmp128 = tmp72 - tmp69;
		    tmp129 = tmp80 - tmp78;
		    tmp130 = tmp128 - tmp129;
		    tmp146 = tmp128 + tmp129;
	       }
	       {
		    fftw_real tmp77;
		    fftw_real tmp81;
		    fftw_real tmp125;
		    fftw_real tmp126;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp77 = tmp75 + tmp76;
		    tmp81 = tmp78 + tmp80;
		    tmp82 = tmp77 + tmp81;
		    tmp98 = tmp81 - tmp77;
		    tmp125 = tmp63 + tmp65;
		    tmp126 = tmp75 - tmp76;
		    tmp127 = tmp125 + tmp126;
		    tmp145 = tmp126 - tmp125;
	       }
	  }
	  {
	       fftw_real tmp15;
	       fftw_real tmp22;
	       fftw_real tmp18;
	       fftw_real tmp20;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp14;
		    fftw_real tmp21;
		    fftw_real tmp16;
		    fftw_real tmp17;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp14 = X[2 * iostride];
		    tmp15 = K2_000000000 * tmp14;
		    tmp21 = X[18 * iostride];
		    tmp22 = K2_000000000 * tmp21;
		    tmp16 = X[10 * iostride];
		    tmp17 = X[26 * iostride];
		    tmp18 = K1_414213562 * (tmp16 - tmp17);
		    tmp20 = K1_414213562 * (tmp16 + tmp17);
	       }
	       {
		    fftw_real tmp19;
		    fftw_real tmp23;
		    fftw_real tmp113;
		    fftw_real tmp114;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp19 = tmp15 + tmp18;
		    tmp23 = tmp20 + tmp22;
		    tmp24 = (K490392640 * tmp19) - (K097545161 * tmp23);
		    tmp90 = (K097545161 * tmp19) + (K490392640 * tmp23);
		    tmp113 = tmp22 - tmp20;
		    tmp114 = tmp15 - tmp18;
		    tmp115 = (K415734806 * tmp113) - (K277785116 * tmp114);
		    tmp138 = (K415734806 * tmp114) + (K277785116 * tmp113);
	       }
	  }
	  {
	       fftw_real tmp29;
	       fftw_real tmp33;
	       fftw_real tmp27;
	       fftw_real tmp31;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp28;
		    fftw_real tmp32;
		    fftw_real tmp25;
		    fftw_real tmp26;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp28 = X[30 * iostride];
		    tmp29 = K2_000000000 * tmp28;
		    tmp32 = X[14 * iostride];
		    tmp33 = K2_000000000 * tmp32;
		    tmp25 = X[6 * iostride];
		    tmp26 = X[22 * iostride];
		    tmp27 = K1_414213562 * (tmp25 - tmp26);
		    tmp31 = K1_414213562 * (tmp25 + tmp26);
	       }
	       {
		    fftw_real tmp30;
		    fftw_real tmp34;
		    fftw_real tmp110;
		    fftw_real tmp111;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp30 = tmp27 - tmp29;
		    tmp34 = tmp31 + tmp33;
		    tmp35 = (K490392640 * tmp30) + (K097545161 * tmp34);
		    tmp91 = (K097545161 * tmp30) - (K490392640 * tmp34);
		    tmp110 = tmp33 - tmp31;
		    tmp111 = tmp27 + tmp29;
		    tmp112 = (K415734806 * tmp110) - (K277785116 * tmp111);
		    tmp139 = (K415734806 * tmp111) + (K277785116 * tmp110);
	       }
	  }
	  {
	       fftw_real tmp39;
	       fftw_real tmp57;
	       fftw_real tmp42;
	       fftw_real tmp55;
	       fftw_real tmp46;
	       fftw_real tmp52;
	       fftw_real tmp49;
	       fftw_real tmp53;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp38;
		    fftw_real tmp56;
		    fftw_real tmp40;
		    fftw_real tmp41;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp38 = X[iostride];
		    tmp39 = K2_000000000 * tmp38;
		    tmp56 = X[17 * iostride];
		    tmp57 = K2_000000000 * tmp56;
		    tmp40 = X[9 * iostride];
		    tmp41 = X[25 * iostride];
		    tmp42 = K1_414213562 * (tmp40 - tmp41);
		    tmp55 = K1_414213562 * (tmp40 + tmp41);
		    {
			 fftw_real tmp44;
			 fftw_real tmp45;
			 fftw_real tmp47;
			 fftw_real tmp48;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp44 = X[5 * iostride];
			 tmp45 = X[21 * iostride];
			 tmp46 = (K1_847759065 * tmp44) - (K765366864 * tmp45);
			 tmp52 = (K765366864 * tmp44) + (K1_847759065 * tmp45);
			 tmp47 = X[13 * iostride];
			 tmp48 = X[29 * iostride];
			 tmp49 = (K765366864 * tmp47) - (K1_847759065 * tmp48);
			 tmp53 = (K1_847759065 * tmp47) + (K765366864 * tmp48);
		    }
	       }
	       {
		    fftw_real tmp43;
		    fftw_real tmp50;
		    fftw_real tmp121;
		    fftw_real tmp122;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp43 = tmp39 + tmp42;
		    tmp50 = tmp46 + tmp49;
		    tmp51 = tmp43 + tmp50;
		    tmp94 = tmp43 - tmp50;
		    tmp121 = tmp49 - tmp46;
		    tmp122 = tmp57 - tmp55;
		    tmp123 = tmp121 - tmp122;
		    tmp143 = tmp121 + tmp122;
	       }
	       {
		    fftw_real tmp54;
		    fftw_real tmp58;
		    fftw_real tmp118;
		    fftw_real tmp119;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp54 = tmp52 + tmp53;
		    tmp58 = tmp55 + tmp57;
		    tmp59 = tmp54 + tmp58;
		    tmp95 = tmp58 - tmp54;
		    tmp118 = tmp39 - tmp42;
		    tmp119 = tmp52 - tmp53;
		    tmp120 = tmp118 - tmp119;
		    tmp142 = tmp118 + tmp119;
	       }
	  }
	  {
	       fftw_real tmp37;
	       fftw_real tmp85;
	       fftw_real tmp160;
	       fftw_real tmp162;
	       fftw_real tmp84;
	       fftw_real tmp153;
	       fftw_real tmp88;
	       fftw_real tmp161;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp13;
		    fftw_real tmp36;
		    fftw_real tmp154;
		    fftw_real tmp159;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp13 = tmp5 + tmp12;
		    tmp36 = tmp24 + tmp35;
		    tmp37 = tmp13 - tmp36;
		    tmp85 = tmp13 + tmp36;
		    tmp154 = tmp91 - tmp90;
		    tmp159 = tmp155 + tmp158;
		    tmp160 = tmp154 - tmp159;
		    tmp162 = tmp154 + tmp159;
	       }
	       {
		    fftw_real tmp60;
		    fftw_real tmp83;
		    fftw_real tmp86;
		    fftw_real tmp87;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp60 = (K049008570 * tmp51) + (K497592363 * tmp59);
		    tmp83 = (K049008570 * tmp74) - (K497592363 * tmp82);
		    tmp84 = tmp60 + tmp83;
		    tmp153 = tmp83 - tmp60;
		    tmp86 = (K497592363 * tmp51) - (K049008570 * tmp59);
		    tmp87 = (K497592363 * tmp74) + (K049008570 * tmp82);
		    tmp88 = tmp86 + tmp87;
		    tmp161 = tmp87 - tmp86;
	       }
	       X[8 * iostride] = tmp37 - tmp84;
	       X[7 * iostride] = tmp37 + tmp84;
	       X[15 * iostride] = tmp85 - tmp88;
	       X[0] = tmp85 + tmp88;
	       Y[-15 * iostride] = tmp153 - tmp160;
	       Y[0] = tmp153 + tmp160;
	       Y[-8 * iostride] = tmp161 - tmp162;
	       Y[-7 * iostride] = tmp161 + tmp162;
	  }
	  {
	       fftw_real tmp93;
	       fftw_real tmp101;
	       fftw_real tmp166;
	       fftw_real tmp168;
	       fftw_real tmp100;
	       fftw_real tmp163;
	       fftw_real tmp104;
	       fftw_real tmp167;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp89;
		    fftw_real tmp92;
		    fftw_real tmp164;
		    fftw_real tmp165;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp89 = tmp5 - tmp12;
		    tmp92 = tmp90 + tmp91;
		    tmp93 = tmp89 + tmp92;
		    tmp101 = tmp89 - tmp92;
		    tmp164 = tmp35 - tmp24;
		    tmp165 = tmp158 - tmp155;
		    tmp166 = tmp164 - tmp165;
		    tmp168 = tmp164 + tmp165;
	       }
	       {
		    fftw_real tmp96;
		    fftw_real tmp99;
		    fftw_real tmp102;
		    fftw_real tmp103;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp96 = (K386505226 * tmp94) + (K317196642 * tmp95);
		    tmp99 = (K386505226 * tmp97) - (K317196642 * tmp98);
		    tmp100 = tmp96 + tmp99;
		    tmp163 = tmp99 - tmp96;
		    tmp102 = (K317196642 * tmp97) + (K386505226 * tmp98);
		    tmp103 = (K386505226 * tmp95) - (K317196642 * tmp94);
		    tmp104 = tmp102 - tmp103;
		    tmp167 = tmp103 + tmp102;
	       }
	       X[12 * iostride] = tmp93 - tmp100;
	       X[3 * iostride] = tmp93 + tmp100;
	       X[11 * iostride] = tmp101 - tmp104;
	       X[4 * iostride] = tmp101 + tmp104;
	       Y[-11 * iostride] = tmp163 - tmp166;
	       Y[-4 * iostride] = tmp163 + tmp166;
	       Y[-12 * iostride] = tmp167 - tmp168;
	       Y[-3 * iostride] = tmp167 + tmp168;
	  }
	  {
	       fftw_real tmp117;
	       fftw_real tmp133;
	       fftw_real tmp174;
	       fftw_real tmp176;
	       fftw_real tmp132;
	       fftw_real tmp175;
	       fftw_real tmp136;
	       fftw_real tmp169;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp109;
		    fftw_real tmp116;
		    fftw_real tmp172;
		    fftw_real tmp173;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp109 = tmp105 - tmp108;
		    tmp116 = tmp112 - tmp115;
		    tmp117 = tmp109 + tmp116;
		    tmp133 = tmp109 - tmp116;
		    tmp172 = tmp170 - tmp171;
		    tmp173 = tmp138 + tmp139;
		    tmp174 = tmp172 - tmp173;
		    tmp176 = tmp173 + tmp172;
	       }
	       {
		    fftw_real tmp124;
		    fftw_real tmp131;
		    fftw_real tmp134;
		    fftw_real tmp135;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp124 = (K440960632 * tmp120) + (K235698368 * tmp123);
		    tmp131 = (K440960632 * tmp127) + (K235698368 * tmp130);
		    tmp132 = tmp124 - tmp131;
		    tmp175 = tmp124 + tmp131;
		    tmp134 = (K440960632 * tmp130) - (K235698368 * tmp127);
		    tmp135 = (K440960632 * tmp123) - (K235698368 * tmp120);
		    tmp136 = tmp134 - tmp135;
		    tmp169 = tmp135 + tmp134;
	       }
	       X[13 * iostride] = tmp117 - tmp132;
	       X[2 * iostride] = tmp117 + tmp132;
	       X[10 * iostride] = tmp133 - tmp136;
	       X[5 * iostride] = tmp133 + tmp136;
	       Y[-13 * iostride] = tmp169 - tmp174;
	       Y[-2 * iostride] = tmp169 + tmp174;
	       Y[-5 * iostride] = -(tmp175 + tmp176);
	       Y[-10 * iostride] = tmp176 - tmp175;
	  }
	  {
	       fftw_real tmp141;
	       fftw_real tmp149;
	       fftw_real tmp180;
	       fftw_real tmp182;
	       fftw_real tmp148;
	       fftw_real tmp177;
	       fftw_real tmp152;
	       fftw_real tmp181;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp137;
		    fftw_real tmp140;
		    fftw_real tmp178;
		    fftw_real tmp179;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp137 = tmp105 + tmp108;
		    tmp140 = tmp138 - tmp139;
		    tmp141 = tmp137 + tmp140;
		    tmp149 = tmp137 - tmp140;
		    tmp178 = tmp115 + tmp112;
		    tmp179 = tmp170 + tmp171;
		    tmp180 = tmp178 - tmp179;
		    tmp182 = tmp178 + tmp179;
	       }
	       {
		    fftw_real tmp144;
		    fftw_real tmp147;
		    fftw_real tmp150;
		    fftw_real tmp151;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp144 = (K478470167 * tmp142) + (K145142338 * tmp143);
		    tmp147 = (K478470167 * tmp145) - (K145142338 * tmp146);
		    tmp148 = tmp144 + tmp147;
		    tmp177 = tmp147 - tmp144;
		    tmp150 = (K145142338 * tmp145) + (K478470167 * tmp146);
		    tmp151 = (K478470167 * tmp143) - (K145142338 * tmp142);
		    tmp152 = tmp150 - tmp151;
		    tmp181 = tmp151 + tmp150;
	       }
	       X[14 * iostride] = tmp141 - tmp148;
	       X[iostride] = tmp141 + tmp148;
	       X[9 * iostride] = tmp149 - tmp152;
	       X[6 * iostride] = tmp149 + tmp152;
	       Y[-9 * iostride] = tmp177 - tmp180;
	       Y[-6 * iostride] = tmp177 + tmp180;
	       Y[-14 * iostride] = tmp181 - tmp182;
	       Y[-iostride] = tmp181 + tmp182;
	  }
     }
}

static const int twiddle_order[] =
{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31};
fftw_codelet_desc fftw_hc2hc_forward_32_desc =
{
     "fftw_hc2hc_forward_32",
     (void (*)()) fftw_hc2hc_forward_32,
     32,
     FFTW_FORWARD,
     FFTW_HC2HC,
     707,
     31,
     twiddle_order,
};
