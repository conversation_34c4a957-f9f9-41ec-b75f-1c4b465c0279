/*
 * Copyright (c) 1997-1999 Massachusetts Institute of Technology
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 *
 */

/* This file was automatically generated --- DO NOT EDIT */
/* Generated on Sun Nov  7 20:43:58 EST 1999 */

#include "fftw-int.h"
#include "fftw.h"

/* Generated by: ./genfft -magic-alignment-check -magic-twiddle-load-all -magic-variables 4 -magic-loopi -real2hc 16 */

/*
 * This function contains 58 FP additions, 12 FP multiplications,
 * (or, 54 additions, 8 multiplications, 4 fused multiply/add),
 * 30 stack variables, and 32 memory accesses
 */
static const fftw_real K707106781 = FFTW_KONST(+0.707106781186547524400844362104849039284835938);
static const fftw_real K923879532 = FFTW_KONST(+0.923879532511286756128183189396788286822416626);
static const fftw_real K382683432 = FFTW_KONST(+0.382683432365089771728459984030398866761344562);

/*
 * Generator Id's : 
 * $Id: exprdag.ml,v 1.41 1999/05/26 15:44:14 fftw Exp $
 * $Id: fft.ml,v 1.43 1999/05/17 19:44:18 fftw Exp $
 * $Id: to_c.ml,v 1.25 1999/10/26 21:41:32 stevenj Exp $
 */

void fftw_real2hc_16(const fftw_real *input, fftw_real *real_output, fftw_real *imag_output, int istride, int real_ostride, int imag_ostride)
{
     fftw_real tmp3;
     fftw_real tmp6;
     fftw_real tmp7;
     fftw_real tmp35;
     fftw_real tmp18;
     fftw_real tmp33;
     fftw_real tmp40;
     fftw_real tmp48;
     fftw_real tmp56;
     fftw_real tmp10;
     fftw_real tmp13;
     fftw_real tmp14;
     fftw_real tmp36;
     fftw_real tmp17;
     fftw_real tmp26;
     fftw_real tmp41;
     fftw_real tmp51;
     fftw_real tmp57;
     fftw_real tmp16;
     fftw_real tmp15;
     fftw_real tmp43;
     fftw_real tmp44;
     ASSERT_ALIGNED_DOUBLE;
     {
	  fftw_real tmp1;
	  fftw_real tmp2;
	  fftw_real tmp4;
	  fftw_real tmp5;
	  ASSERT_ALIGNED_DOUBLE;
	  tmp1 = input[0];
	  tmp2 = input[8 * istride];
	  tmp3 = tmp1 + tmp2;
	  tmp4 = input[4 * istride];
	  tmp5 = input[12 * istride];
	  tmp6 = tmp4 + tmp5;
	  tmp7 = tmp3 + tmp6;
	  tmp35 = tmp1 - tmp2;
	  tmp18 = tmp4 - tmp5;
     }
     {
	  fftw_real tmp29;
	  fftw_real tmp46;
	  fftw_real tmp32;
	  fftw_real tmp47;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp27;
	       fftw_real tmp28;
	       fftw_real tmp30;
	       fftw_real tmp31;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp27 = input[istride];
	       tmp28 = input[9 * istride];
	       tmp29 = tmp27 - tmp28;
	       tmp46 = tmp27 + tmp28;
	       tmp30 = input[5 * istride];
	       tmp31 = input[13 * istride];
	       tmp32 = tmp30 - tmp31;
	       tmp47 = tmp30 + tmp31;
	  }
	  tmp33 = (K382683432 * tmp29) + (K923879532 * tmp32);
	  tmp40 = (K923879532 * tmp29) - (K382683432 * tmp32);
	  tmp48 = tmp46 - tmp47;
	  tmp56 = tmp46 + tmp47;
     }
     {
	  fftw_real tmp8;
	  fftw_real tmp9;
	  fftw_real tmp11;
	  fftw_real tmp12;
	  ASSERT_ALIGNED_DOUBLE;
	  tmp8 = input[2 * istride];
	  tmp9 = input[10 * istride];
	  tmp10 = tmp8 + tmp9;
	  tmp16 = tmp8 - tmp9;
	  tmp11 = input[14 * istride];
	  tmp12 = input[6 * istride];
	  tmp13 = tmp11 + tmp12;
	  tmp15 = tmp11 - tmp12;
     }
     tmp14 = tmp10 + tmp13;
     tmp36 = K707106781 * (tmp16 + tmp15);
     tmp17 = K707106781 * (tmp15 - tmp16);
     {
	  fftw_real tmp22;
	  fftw_real tmp49;
	  fftw_real tmp25;
	  fftw_real tmp50;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp20;
	       fftw_real tmp21;
	       fftw_real tmp23;
	       fftw_real tmp24;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp20 = input[15 * istride];
	       tmp21 = input[7 * istride];
	       tmp22 = tmp20 - tmp21;
	       tmp49 = tmp20 + tmp21;
	       tmp23 = input[3 * istride];
	       tmp24 = input[11 * istride];
	       tmp25 = tmp23 - tmp24;
	       tmp50 = tmp23 + tmp24;
	  }
	  tmp26 = (K382683432 * tmp22) - (K923879532 * tmp25);
	  tmp41 = (K923879532 * tmp22) + (K382683432 * tmp25);
	  tmp51 = tmp49 - tmp50;
	  tmp57 = tmp49 + tmp50;
     }
     {
	  fftw_real tmp55;
	  fftw_real tmp58;
	  fftw_real tmp53;
	  fftw_real tmp54;
	  ASSERT_ALIGNED_DOUBLE;
	  real_output[4 * real_ostride] = tmp7 - tmp14;
	  tmp55 = tmp7 + tmp14;
	  tmp58 = tmp56 + tmp57;
	  real_output[8 * real_ostride] = tmp55 - tmp58;
	  real_output[0] = tmp55 + tmp58;
	  imag_output[4 * imag_ostride] = tmp57 - tmp56;
	  tmp53 = tmp13 - tmp10;
	  tmp54 = K707106781 * (tmp51 - tmp48);
	  imag_output[2 * imag_ostride] = tmp53 + tmp54;
	  imag_output[6 * imag_ostride] = tmp54 - tmp53;
     }
     {
	  fftw_real tmp45;
	  fftw_real tmp52;
	  fftw_real tmp39;
	  fftw_real tmp42;
	  ASSERT_ALIGNED_DOUBLE;
	  tmp45 = tmp3 - tmp6;
	  tmp52 = K707106781 * (tmp48 + tmp51);
	  real_output[6 * real_ostride] = tmp45 - tmp52;
	  real_output[2 * real_ostride] = tmp45 + tmp52;
	  tmp39 = tmp35 + tmp36;
	  tmp42 = tmp40 + tmp41;
	  real_output[7 * real_ostride] = tmp39 - tmp42;
	  real_output[real_ostride] = tmp39 + tmp42;
     }
     tmp43 = tmp18 + tmp17;
     tmp44 = tmp41 - tmp40;
     imag_output[3 * imag_ostride] = tmp43 + tmp44;
     imag_output[5 * imag_ostride] = tmp44 - tmp43;
     {
	  fftw_real tmp19;
	  fftw_real tmp34;
	  fftw_real tmp37;
	  fftw_real tmp38;
	  ASSERT_ALIGNED_DOUBLE;
	  tmp19 = tmp17 - tmp18;
	  tmp34 = tmp26 - tmp33;
	  imag_output[imag_ostride] = tmp19 + tmp34;
	  imag_output[7 * imag_ostride] = tmp34 - tmp19;
	  tmp37 = tmp35 - tmp36;
	  tmp38 = tmp33 + tmp26;
	  real_output[5 * real_ostride] = tmp37 - tmp38;
	  real_output[3 * real_ostride] = tmp37 + tmp38;
     }
}

fftw_codelet_desc fftw_real2hc_16_desc =
{
     "fftw_real2hc_16",
     (void (*)()) fftw_real2hc_16,
     16,
     FFTW_FORWARD,
     FFTW_REAL2HC,
     354,
     0,
     (const int *) 0,
};
