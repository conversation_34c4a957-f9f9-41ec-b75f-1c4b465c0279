/*
 * Copyright (c) 1997-1999 Massachusetts Institute of Technology
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 *
 */

/* This file was automatically generated --- DO NOT EDIT */
/* Generated on Sun Nov  7 20:45:14 EST 1999 */

#include "fftw-int.h"
#include "fftw.h"

/* Generated by: ./genfft -magic-alignment-check -magic-twiddle-load-all -magic-variables 4 -magic-loopi -hc2hc-backward 9 */

/*
 * This function contains 183 FP additions, 122 FP multiplications,
 * (or, 130 additions, 69 multiplications, 53 fused multiply/add),
 * 43 stack variables, and 72 memory accesses
 */
static const fftw_real K663413948 = FFTW_KONST(+0.663413948168938396205421319635891297216863310);
static const fftw_real K556670399 = FFTW_KONST(+0.556670399226419366452912952047023132968291906);
static const fftw_real K296198132 = FFTW_KONST(+0.296198132726023843175338011893050938967728390);
static const fftw_real K150383733 = FFTW_KONST(+0.150383733180435296639271897612501926072238258);
static const fftw_real K813797681 = FFTW_KONST(+0.813797681349373692844693217248393223289101568);
static const fftw_real K852868531 = FFTW_KONST(+0.852868531952443209628250963940074071936020296);
static const fftw_real K939692620 = FFTW_KONST(+0.939692620785908384054109277324731469936208134);
static const fftw_real K342020143 = FFTW_KONST(+0.342020143325668733044099614682259580763083368);
static const fftw_real K984807753 = FFTW_KONST(+0.984807753012208059366743024589523013670643252);
static const fftw_real K173648177 = FFTW_KONST(+0.173648177666930348851716626769314796000375677);
static const fftw_real K300767466 = FFTW_KONST(+0.300767466360870593278543795225003852144476517);
static const fftw_real K1_705737063 = FFTW_KONST(+1.705737063904886419256501927880148143872040591);
static const fftw_real K642787609 = FFTW_KONST(+0.642787609686539326322643409907263432907559884);
static const fftw_real K766044443 = FFTW_KONST(+0.766044443118978035202392650555416673935832457);
static const fftw_real K1_326827896 = FFTW_KONST(+1.326827896337876792410842639271782594433726619);
static const fftw_real K1_113340798 = FFTW_KONST(+1.113340798452838732905825904094046265936583811);
static const fftw_real K500000000 = FFTW_KONST(+0.500000000000000000000000000000000000000000000);
static const fftw_real K866025403 = FFTW_KONST(+0.866025403784438646763723170752936183471402627);
static const fftw_real K2_000000000 = FFTW_KONST(+2.000000000000000000000000000000000000000000000);
static const fftw_real K1_732050807 = FFTW_KONST(+1.732050807568877293527446341505872366942805254);

/*
 * Generator Id's : 
 * $Id: exprdag.ml,v 1.41 1999/05/26 15:44:14 fftw Exp $
 * $Id: fft.ml,v 1.43 1999/05/17 19:44:18 fftw Exp $
 * $Id: to_c.ml,v 1.25 1999/10/26 21:41:32 stevenj Exp $
 */

void fftw_hc2hc_backward_9(fftw_real *A, const fftw_complex *W, int iostride, int m, int dist)
{
     int i;
     fftw_real *X;
     fftw_real *Y;
     X = A;
     Y = A + (9 * iostride);
     {
	  fftw_real tmp153;
	  fftw_real tmp181;
	  fftw_real tmp175;
	  fftw_real tmp160;
	  fftw_real tmp163;
	  fftw_real tmp158;
	  fftw_real tmp167;
	  fftw_real tmp178;
	  fftw_real tmp170;
	  fftw_real tmp179;
	  fftw_real tmp159;
	  fftw_real tmp164;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp174;
	       fftw_real tmp151;
	       fftw_real tmp152;
	       fftw_real tmp172;
	       fftw_real tmp173;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp173 = Y[-3 * iostride];
	       tmp174 = K1_732050807 * tmp173;
	       tmp151 = X[0];
	       tmp152 = X[3 * iostride];
	       tmp172 = tmp152 - tmp151;
	       tmp153 = tmp151 + (K2_000000000 * tmp152);
	       tmp181 = tmp174 - tmp172;
	       tmp175 = tmp172 + tmp174;
	  }
	  {
	       fftw_real tmp154;
	       fftw_real tmp157;
	       fftw_real tmp168;
	       fftw_real tmp166;
	       fftw_real tmp165;
	       fftw_real tmp169;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp154 = X[iostride];
	       tmp160 = Y[-iostride];
	       {
		    fftw_real tmp155;
		    fftw_real tmp156;
		    fftw_real tmp161;
		    fftw_real tmp162;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp155 = X[4 * iostride];
		    tmp156 = X[2 * iostride];
		    tmp157 = tmp155 + tmp156;
		    tmp168 = K866025403 * (tmp155 - tmp156);
		    tmp161 = Y[-2 * iostride];
		    tmp162 = Y[-4 * iostride];
		    tmp163 = tmp161 - tmp162;
		    tmp166 = K866025403 * (tmp162 + tmp161);
	       }
	       tmp158 = tmp154 + tmp157;
	       tmp165 = tmp154 - (K500000000 * tmp157);
	       tmp167 = tmp165 - tmp166;
	       tmp178 = tmp165 + tmp166;
	       tmp169 = (K500000000 * tmp163) + tmp160;
	       tmp170 = tmp168 + tmp169;
	       tmp179 = tmp169 - tmp168;
	  }
	  X[0] = tmp153 + (K2_000000000 * tmp158);
	  tmp159 = tmp153 - tmp158;
	  tmp164 = K1_732050807 * (tmp160 - tmp163);
	  X[6 * iostride] = tmp159 + tmp164;
	  X[3 * iostride] = tmp159 - tmp164;
	  {
	       fftw_real tmp176;
	       fftw_real tmp171;
	       fftw_real tmp177;
	       fftw_real tmp183;
	       fftw_real tmp180;
	       fftw_real tmp182;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp176 = (K1_113340798 * tmp167) + (K1_326827896 * tmp170);
	       tmp171 = (K766044443 * tmp167) - (K642787609 * tmp170);
	       tmp177 = tmp171 + tmp175;
	       X[iostride] = (K2_000000000 * tmp171) - tmp175;
	       X[7 * iostride] = tmp176 - tmp177;
	       X[4 * iostride] = -(tmp176 + tmp177);
	       tmp183 = (K1_705737063 * tmp178) + (K300767466 * tmp179);
	       tmp180 = (K173648177 * tmp178) - (K984807753 * tmp179);
	       tmp182 = tmp181 - tmp180;
	       X[2 * iostride] = (K2_000000000 * tmp180) + tmp181;
	       X[8 * iostride] = tmp183 + tmp182;
	       X[5 * iostride] = tmp182 - tmp183;
	  }
     }
     X = X + dist;
     Y = Y - dist;
     for (i = 2; i < m; i = i + 2, X = X + dist, Y = Y - dist, W = W + 8) {
	  fftw_real tmp43;
	  fftw_real tmp86;
	  fftw_real tmp134;
	  fftw_real tmp59;
	  fftw_real tmp106;
	  fftw_real tmp124;
	  fftw_real tmp48;
	  fftw_real tmp53;
	  fftw_real tmp54;
	  fftw_real tmp100;
	  fftw_real tmp108;
	  fftw_real tmp130;
	  fftw_real tmp136;
	  fftw_real tmp127;
	  fftw_real tmp135;
	  fftw_real tmp93;
	  fftw_real tmp107;
	  fftw_real tmp64;
	  fftw_real tmp69;
	  fftw_real tmp70;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp39;
	       fftw_real tmp55;
	       fftw_real tmp42;
	       fftw_real tmp104;
	       fftw_real tmp58;
	       fftw_real tmp85;
	       fftw_real tmp84;
	       fftw_real tmp105;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp39 = X[0];
	       tmp55 = Y[0];
	       {
		    fftw_real tmp40;
		    fftw_real tmp41;
		    fftw_real tmp56;
		    fftw_real tmp57;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp40 = X[3 * iostride];
		    tmp41 = Y[-6 * iostride];
		    tmp42 = tmp40 + tmp41;
		    tmp104 = K866025403 * (tmp40 - tmp41);
		    tmp56 = Y[-3 * iostride];
		    tmp57 = X[6 * iostride];
		    tmp58 = tmp56 - tmp57;
		    tmp85 = K866025403 * (tmp56 + tmp57);
	       }
	       tmp43 = tmp39 + tmp42;
	       tmp84 = tmp39 - (K500000000 * tmp42);
	       tmp86 = tmp84 - tmp85;
	       tmp134 = tmp84 + tmp85;
	       tmp59 = tmp55 + tmp58;
	       tmp105 = tmp55 - (K500000000 * tmp58);
	       tmp106 = tmp104 + tmp105;
	       tmp124 = tmp105 - tmp104;
	  }
	  {
	       fftw_real tmp44;
	       fftw_real tmp47;
	       fftw_real tmp87;
	       fftw_real tmp90;
	       fftw_real tmp60;
	       fftw_real tmp63;
	       fftw_real tmp88;
	       fftw_real tmp91;
	       fftw_real tmp49;
	       fftw_real tmp52;
	       fftw_real tmp94;
	       fftw_real tmp97;
	       fftw_real tmp65;
	       fftw_real tmp68;
	       fftw_real tmp95;
	       fftw_real tmp98;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp45;
		    fftw_real tmp46;
		    fftw_real tmp61;
		    fftw_real tmp62;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp44 = X[iostride];
		    tmp45 = X[4 * iostride];
		    tmp46 = Y[-7 * iostride];
		    tmp47 = tmp45 + tmp46;
		    tmp87 = tmp44 - (K500000000 * tmp47);
		    tmp90 = K866025403 * (tmp45 - tmp46);
		    tmp60 = Y[-iostride];
		    tmp61 = Y[-4 * iostride];
		    tmp62 = X[7 * iostride];
		    tmp63 = tmp61 - tmp62;
		    tmp88 = K866025403 * (tmp61 + tmp62);
		    tmp91 = tmp60 - (K500000000 * tmp63);
	       }
	       {
		    fftw_real tmp50;
		    fftw_real tmp51;
		    fftw_real tmp66;
		    fftw_real tmp67;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp49 = X[2 * iostride];
		    tmp50 = Y[-5 * iostride];
		    tmp51 = Y[-8 * iostride];
		    tmp52 = tmp50 + tmp51;
		    tmp94 = tmp49 - (K500000000 * tmp52);
		    tmp97 = K866025403 * (tmp50 - tmp51);
		    tmp65 = Y[-2 * iostride];
		    tmp66 = X[5 * iostride];
		    tmp67 = X[8 * iostride];
		    tmp68 = tmp66 + tmp67;
		    tmp95 = K866025403 * (tmp66 - tmp67);
		    tmp98 = tmp65 + (K500000000 * tmp68);
	       }
	       tmp48 = tmp44 + tmp47;
	       tmp53 = tmp49 + tmp52;
	       tmp54 = tmp48 + tmp53;
	       {
		    fftw_real tmp96;
		    fftw_real tmp99;
		    fftw_real tmp128;
		    fftw_real tmp129;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp96 = tmp94 + tmp95;
		    tmp99 = tmp97 + tmp98;
		    tmp100 = (K173648177 * tmp96) - (K984807753 * tmp99);
		    tmp108 = (K984807753 * tmp96) + (K173648177 * tmp99);
		    tmp128 = tmp94 - tmp95;
		    tmp129 = tmp98 - tmp97;
		    tmp130 = (K342020143 * tmp128) - (K939692620 * tmp129);
		    tmp136 = (K939692620 * tmp128) + (K342020143 * tmp129);
	       }
	       {
		    fftw_real tmp125;
		    fftw_real tmp126;
		    fftw_real tmp89;
		    fftw_real tmp92;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp125 = tmp91 - tmp90;
		    tmp126 = tmp87 + tmp88;
		    tmp127 = (K173648177 * tmp125) + (K984807753 * tmp126);
		    tmp135 = (K173648177 * tmp126) - (K984807753 * tmp125);
		    tmp89 = tmp87 - tmp88;
		    tmp92 = tmp90 + tmp91;
		    tmp93 = (K766044443 * tmp89) - (K642787609 * tmp92);
		    tmp107 = (K766044443 * tmp92) + (K642787609 * tmp89);
		    tmp64 = tmp60 + tmp63;
		    tmp69 = tmp65 - tmp68;
		    tmp70 = tmp64 + tmp69;
	       }
	  }
	  X[0] = tmp43 + tmp54;
	  {
	       fftw_real tmp74;
	       fftw_real tmp80;
	       fftw_real tmp78;
	       fftw_real tmp82;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp72;
		    fftw_real tmp73;
		    fftw_real tmp76;
		    fftw_real tmp77;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp72 = tmp59 - (K500000000 * tmp70);
		    tmp73 = K866025403 * (tmp48 - tmp53);
		    tmp74 = tmp72 - tmp73;
		    tmp80 = tmp73 + tmp72;
		    tmp76 = tmp43 - (K500000000 * tmp54);
		    tmp77 = K866025403 * (tmp69 - tmp64);
		    tmp78 = tmp76 - tmp77;
		    tmp82 = tmp76 + tmp77;
	       }
	       {
		    fftw_real tmp71;
		    fftw_real tmp75;
		    fftw_real tmp79;
		    fftw_real tmp81;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp71 = c_re(W[5]);
		    tmp75 = c_im(W[5]);
		    Y[-2 * iostride] = (tmp71 * tmp74) - (tmp75 * tmp78);
		    X[6 * iostride] = (tmp75 * tmp74) + (tmp71 * tmp78);
		    tmp79 = c_re(W[2]);
		    tmp81 = c_im(W[2]);
		    Y[-5 * iostride] = (tmp79 * tmp80) - (tmp81 * tmp82);
		    X[3 * iostride] = (tmp81 * tmp80) + (tmp79 * tmp82);
	       }
	  }
	  Y[-8 * iostride] = tmp59 + tmp70;
	  {
	       fftw_real tmp113;
	       fftw_real tmp102;
	       fftw_real tmp116;
	       fftw_real tmp117;
	       fftw_real tmp110;
	       fftw_real tmp112;
	       fftw_real tmp101;
	       fftw_real tmp109;
	       fftw_real tmp83;
	       fftw_real tmp103;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp113 = K866025403 * (tmp93 - tmp100);
	       tmp101 = tmp93 + tmp100;
	       tmp102 = tmp86 + tmp101;
	       tmp116 = tmp86 - (K500000000 * tmp101);
	       tmp117 = K866025403 * (tmp108 - tmp107);
	       tmp109 = tmp107 + tmp108;
	       tmp110 = tmp106 + tmp109;
	       tmp112 = tmp106 - (K500000000 * tmp109);
	       tmp83 = c_re(W[0]);
	       tmp103 = c_im(W[0]);
	       X[iostride] = (tmp83 * tmp102) + (tmp103 * tmp110);
	       Y[-7 * iostride] = (tmp83 * tmp110) - (tmp103 * tmp102);
	       {
		    fftw_real tmp120;
		    fftw_real tmp122;
		    fftw_real tmp119;
		    fftw_real tmp121;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp120 = tmp113 + tmp112;
		    tmp122 = tmp116 + tmp117;
		    tmp119 = c_re(W[3]);
		    tmp121 = c_im(W[3]);
		    Y[-4 * iostride] = (tmp119 * tmp120) - (tmp121 * tmp122);
		    X[4 * iostride] = (tmp121 * tmp120) + (tmp119 * tmp122);
	       }
	       {
		    fftw_real tmp114;
		    fftw_real tmp118;
		    fftw_real tmp111;
		    fftw_real tmp115;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp114 = tmp112 - tmp113;
		    tmp118 = tmp116 - tmp117;
		    tmp111 = c_re(W[6]);
		    tmp115 = c_im(W[6]);
		    Y[-iostride] = (tmp111 * tmp114) - (tmp115 * tmp118);
		    X[7 * iostride] = (tmp115 * tmp114) + (tmp111 * tmp118);
	       }
	  }
	  {
	       fftw_real tmp141;
	       fftw_real tmp132;
	       fftw_real tmp144;
	       fftw_real tmp145;
	       fftw_real tmp138;
	       fftw_real tmp140;
	       fftw_real tmp131;
	       fftw_real tmp137;
	       fftw_real tmp123;
	       fftw_real tmp133;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp141 = K866025403 * (tmp130 - tmp127);
	       tmp131 = tmp127 + tmp130;
	       tmp132 = tmp124 + tmp131;
	       tmp144 = tmp124 - (K500000000 * tmp131);
	       tmp145 = K866025403 * (tmp135 + tmp136);
	       tmp137 = tmp135 - tmp136;
	       tmp138 = tmp134 + tmp137;
	       tmp140 = tmp134 - (K500000000 * tmp137);
	       tmp123 = c_re(W[1]);
	       tmp133 = c_im(W[1]);
	       Y[-6 * iostride] = (tmp123 * tmp132) - (tmp133 * tmp138);
	       X[2 * iostride] = (tmp133 * tmp132) + (tmp123 * tmp138);
	       {
		    fftw_real tmp148;
		    fftw_real tmp150;
		    fftw_real tmp147;
		    fftw_real tmp149;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp148 = tmp141 + tmp140;
		    tmp150 = tmp144 + tmp145;
		    tmp147 = c_re(W[4]);
		    tmp149 = c_im(W[4]);
		    X[5 * iostride] = (tmp147 * tmp148) + (tmp149 * tmp150);
		    Y[-3 * iostride] = (tmp147 * tmp150) - (tmp149 * tmp148);
	       }
	       {
		    fftw_real tmp142;
		    fftw_real tmp146;
		    fftw_real tmp139;
		    fftw_real tmp143;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp142 = tmp140 - tmp141;
		    tmp146 = tmp144 - tmp145;
		    tmp139 = c_re(W[7]);
		    tmp143 = c_im(W[7]);
		    X[8 * iostride] = (tmp139 * tmp142) + (tmp143 * tmp146);
		    Y[0] = (tmp139 * tmp146) - (tmp143 * tmp142);
	       }
	  }
     }
     if (i == m) {
	  fftw_real tmp3;
	  fftw_real tmp31;
	  fftw_real tmp28;
	  fftw_real tmp17;
	  fftw_real tmp34;
	  fftw_real tmp7;
	  fftw_real tmp33;
	  fftw_real tmp23;
	  fftw_real tmp20;
	  fftw_real tmp35;
	  fftw_real tmp12;
	  fftw_real tmp14;
	  fftw_real tmp32;
	  fftw_real tmp8;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp27;
	       fftw_real tmp1;
	       fftw_real tmp2;
	       fftw_real tmp25;
	       fftw_real tmp26;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp26 = Y[-iostride];
	       tmp27 = K1_732050807 * tmp26;
	       tmp1 = X[4 * iostride];
	       tmp2 = X[iostride];
	       tmp25 = tmp2 - tmp1;
	       tmp3 = tmp1 + (K2_000000000 * tmp2);
	       tmp31 = tmp25 - tmp27;
	       tmp28 = tmp25 + tmp27;
	  }
	  {
	       fftw_real tmp4;
	       fftw_real tmp6;
	       fftw_real tmp5;
	       fftw_real tmp13;
	       fftw_real tmp15;
	       fftw_real tmp18;
	       fftw_real tmp22;
	       fftw_real tmp9;
	       fftw_real tmp11;
	       fftw_real tmp10;
	       fftw_real tmp21;
	       fftw_real tmp19;
	       fftw_real tmp16;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp4 = X[3 * iostride];
	       tmp6 = X[2 * iostride];
	       tmp5 = X[0];
	       tmp13 = K500000000 * (tmp5 + tmp6);
	       tmp15 = K866025403 * (tmp6 - tmp5);
	       tmp18 = K866025403 * (tmp4 - tmp5);
	       tmp22 = K500000000 * (tmp4 + tmp5);
	       tmp9 = Y[-2 * iostride];
	       tmp11 = Y[0];
	       tmp10 = Y[-3 * iostride];
	       tmp21 = K866025403 * (tmp11 - tmp10);
	       tmp19 = (K500000000 * (tmp11 + tmp10)) + tmp9;
	       tmp16 = (K500000000 * (tmp9 - tmp11)) + tmp10;
	       tmp17 = tmp15 - tmp16;
	       tmp34 = tmp15 + tmp16;
	       tmp7 = tmp4 + tmp5 + tmp6;
	       tmp33 = tmp22 + tmp21 - tmp6;
	       tmp23 = (tmp6 + tmp21) - tmp22;
	       tmp20 = tmp18 - tmp19;
	       tmp35 = tmp18 + tmp19;
	       tmp12 = (K1_732050807 * (tmp9 - tmp10)) - (K1_732050807 * tmp11);
	       tmp14 = tmp4 - (K866025403 * (tmp11 + tmp9)) - tmp13;
	       tmp32 = tmp13 - (K866025403 * (tmp11 + tmp9)) - tmp4;
	  }
	  X[0] = tmp3 + (K2_000000000 * tmp7);
	  tmp8 = tmp7 - tmp3;
	  X[3 * iostride] = tmp8 + tmp12;
	  X[6 * iostride] = tmp12 - tmp8;
	  {
	       fftw_real tmp30;
	       fftw_real tmp24;
	       fftw_real tmp29;
	       fftw_real tmp38;
	       fftw_real tmp36;
	       fftw_real tmp37;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp30 = (K852868531 * tmp14) + (K813797681 * tmp20) + (K150383733 * tmp17) - (K296198132 * tmp23);
	       tmp24 = (K173648177 * tmp14) - (K984807753 * tmp17) - (K342020143 * tmp20) - (K939692620 * tmp23);
	       tmp29 = tmp28 + (K500000000 * tmp24);
	       X[2 * iostride] = tmp24 - tmp28;
	       X[8 * iostride] = tmp30 - tmp29;
	       X[5 * iostride] = tmp29 + tmp30;
	       tmp38 = (K556670399 * tmp32) + (K663413948 * tmp34) - (K150383733 * tmp35) - (K852868531 * tmp33);
	       tmp36 = (K766044443 * tmp32) + (K173648177 * tmp33) - (K642787609 * tmp34) - (K984807753 * tmp35);
	       tmp37 = (K500000000 * tmp36) - tmp31;
	       X[iostride] = tmp31 + tmp36;
	       X[7 * iostride] = tmp38 - tmp37;
	       X[4 * iostride] = tmp37 + tmp38;
	  }
     }
}

static const int twiddle_order[] =
{1, 2, 3, 4, 5, 6, 7, 8};
fftw_codelet_desc fftw_hc2hc_backward_9_desc =
{
     "fftw_hc2hc_backward_9",
     (void (*)()) fftw_hc2hc_backward_9,
     9,
     FFTW_BACKWARD,
     FFTW_HC2HC,
     212,
     8,
     twiddle_order,
};
