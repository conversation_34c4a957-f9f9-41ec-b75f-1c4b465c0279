/*
 * Copyright (c) 1997-1999 Massachusetts Institute of Technology
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 *
 */

/* This file was automatically generated --- DO NOT EDIT */
/* Generated on Sun Nov  7 20:45:10 EST 1999 */

#include "fftw-int.h"
#include "fftw.h"

/* Generated by: ./genfft -magic-alignment-check -magic-twiddle-load-all -magic-variables 4 -magic-loopi -hc2hc-backward 6 */

/*
 * This function contains 72 FP additions, 38 FP multiplications,
 * (or, 54 additions, 20 multiplications, 18 fused multiply/add),
 * 25 stack variables, and 48 memory accesses
 */
static const fftw_real K500000000 = FFTW_KONST(+0.500000000000000000000000000000000000000000000);
static const fftw_real K866025403 = FFTW_KONST(+0.866025403784438646763723170752936183471402627);
static const fftw_real K2_000000000 = FFTW_KONST(+2.000000000000000000000000000000000000000000000);
static const fftw_real K1_732050807 = FFTW_KONST(+1.732050807568877293527446341505872366942805254);

/*
 * Generator Id's : 
 * $Id: exprdag.ml,v 1.41 1999/05/26 15:44:14 fftw Exp $
 * $Id: fft.ml,v 1.43 1999/05/17 19:44:18 fftw Exp $
 * $Id: to_c.ml,v 1.25 1999/10/26 21:41:32 stevenj Exp $
 */

void fftw_hc2hc_backward_6(fftw_real *A, const fftw_complex *W, int iostride, int m, int dist)
{
     int i;
     fftw_real *X;
     fftw_real *Y;
     X = A;
     Y = A + (6 * iostride);
     {
	  fftw_real tmp71;
	  fftw_real tmp75;
	  fftw_real tmp80;
	  fftw_real tmp82;
	  fftw_real tmp74;
	  fftw_real tmp76;
	  fftw_real tmp69;
	  fftw_real tmp70;
	  fftw_real tmp77;
	  fftw_real tmp81;
	  ASSERT_ALIGNED_DOUBLE;
	  tmp69 = X[0];
	  tmp70 = X[3 * iostride];
	  tmp71 = tmp69 - tmp70;
	  tmp75 = tmp69 + tmp70;
	  {
	       fftw_real tmp78;
	       fftw_real tmp79;
	       fftw_real tmp72;
	       fftw_real tmp73;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp78 = Y[-2 * iostride];
	       tmp79 = Y[-iostride];
	       tmp80 = K1_732050807 * (tmp78 + tmp79);
	       tmp82 = K1_732050807 * (tmp78 - tmp79);
	       tmp72 = X[2 * iostride];
	       tmp73 = X[iostride];
	       tmp74 = tmp72 - tmp73;
	       tmp76 = tmp72 + tmp73;
	  }
	  X[3 * iostride] = tmp71 + (K2_000000000 * tmp74);
	  tmp77 = tmp71 - tmp74;
	  X[iostride] = tmp77 - tmp80;
	  X[5 * iostride] = tmp77 + tmp80;
	  X[0] = tmp75 + (K2_000000000 * tmp76);
	  tmp81 = tmp75 - tmp76;
	  X[2 * iostride] = tmp81 + tmp82;
	  X[4 * iostride] = tmp81 - tmp82;
     }
     X = X + dist;
     Y = Y - dist;
     for (i = 2; i < m; i = i + 2, X = X + dist, Y = Y - dist, W = W + 5) {
	  fftw_real tmp15;
	  fftw_real tmp46;
	  fftw_real tmp25;
	  fftw_real tmp52;
	  fftw_real tmp22;
	  fftw_real tmp35;
	  fftw_real tmp49;
	  fftw_real tmp62;
	  fftw_real tmp32;
	  fftw_real tmp39;
	  fftw_real tmp55;
	  fftw_real tmp59;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp13;
	       fftw_real tmp14;
	       fftw_real tmp23;
	       fftw_real tmp24;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp13 = X[0];
	       tmp14 = Y[-3 * iostride];
	       tmp15 = tmp13 + tmp14;
	       tmp46 = tmp13 - tmp14;
	       tmp23 = Y[0];
	       tmp24 = X[3 * iostride];
	       tmp25 = tmp23 - tmp24;
	       tmp52 = tmp23 + tmp24;
	  }
	  {
	       fftw_real tmp18;
	       fftw_real tmp47;
	       fftw_real tmp21;
	       fftw_real tmp48;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp16;
		    fftw_real tmp17;
		    fftw_real tmp19;
		    fftw_real tmp20;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp16 = X[2 * iostride];
		    tmp17 = Y[-5 * iostride];
		    tmp18 = tmp16 + tmp17;
		    tmp47 = tmp16 - tmp17;
		    tmp19 = Y[-4 * iostride];
		    tmp20 = X[iostride];
		    tmp21 = tmp19 + tmp20;
		    tmp48 = tmp19 - tmp20;
	       }
	       tmp22 = tmp18 + tmp21;
	       tmp35 = K866025403 * (tmp18 - tmp21);
	       tmp49 = tmp47 + tmp48;
	       tmp62 = K866025403 * (tmp47 - tmp48);
	  }
	  {
	       fftw_real tmp28;
	       fftw_real tmp54;
	       fftw_real tmp31;
	       fftw_real tmp53;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp26;
		    fftw_real tmp27;
		    fftw_real tmp29;
		    fftw_real tmp30;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp26 = Y[-2 * iostride];
		    tmp27 = X[5 * iostride];
		    tmp28 = tmp26 - tmp27;
		    tmp54 = tmp26 + tmp27;
		    tmp29 = Y[-iostride];
		    tmp30 = X[4 * iostride];
		    tmp31 = tmp29 - tmp30;
		    tmp53 = tmp29 + tmp30;
	       }
	       tmp32 = tmp28 + tmp31;
	       tmp39 = K866025403 * (tmp31 - tmp28);
	       tmp55 = tmp53 - tmp54;
	       tmp59 = K866025403 * (tmp54 + tmp53);
	  }
	  X[0] = tmp15 + tmp22;
	  {
	       fftw_real tmp36;
	       fftw_real tmp42;
	       fftw_real tmp40;
	       fftw_real tmp44;
	       fftw_real tmp34;
	       fftw_real tmp38;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp34 = tmp25 - (K500000000 * tmp32);
	       tmp36 = tmp34 - tmp35;
	       tmp42 = tmp35 + tmp34;
	       tmp38 = tmp15 - (K500000000 * tmp22);
	       tmp40 = tmp38 - tmp39;
	       tmp44 = tmp38 + tmp39;
	       {
		    fftw_real tmp33;
		    fftw_real tmp37;
		    fftw_real tmp41;
		    fftw_real tmp43;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp33 = c_re(W[1]);
		    tmp37 = c_im(W[1]);
		    Y[-3 * iostride] = (tmp33 * tmp36) - (tmp37 * tmp40);
		    X[2 * iostride] = (tmp37 * tmp36) + (tmp33 * tmp40);
		    tmp41 = c_re(W[3]);
		    tmp43 = c_im(W[3]);
		    Y[-iostride] = (tmp41 * tmp42) - (tmp43 * tmp44);
		    X[4 * iostride] = (tmp43 * tmp42) + (tmp41 * tmp44);
	       }
	  }
	  Y[-5 * iostride] = tmp25 + tmp32;
	  {
	       fftw_real tmp50;
	       fftw_real tmp56;
	       fftw_real tmp45;
	       fftw_real tmp51;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp50 = tmp46 + tmp49;
	       tmp56 = tmp52 - tmp55;
	       tmp45 = c_re(W[2]);
	       tmp51 = c_im(W[2]);
	       X[3 * iostride] = (tmp45 * tmp50) + (tmp51 * tmp56);
	       Y[-2 * iostride] = (tmp45 * tmp56) - (tmp51 * tmp50);
	  }
	  {
	       fftw_real tmp60;
	       fftw_real tmp66;
	       fftw_real tmp64;
	       fftw_real tmp68;
	       fftw_real tmp58;
	       fftw_real tmp63;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp58 = tmp46 - (K500000000 * tmp49);
	       tmp60 = tmp58 - tmp59;
	       tmp66 = tmp58 + tmp59;
	       tmp63 = tmp52 + (K500000000 * tmp55);
	       tmp64 = tmp62 + tmp63;
	       tmp68 = tmp63 - tmp62;
	       {
		    fftw_real tmp57;
		    fftw_real tmp61;
		    fftw_real tmp65;
		    fftw_real tmp67;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp57 = c_re(W[0]);
		    tmp61 = c_im(W[0]);
		    X[iostride] = (tmp57 * tmp60) + (tmp61 * tmp64);
		    Y[-4 * iostride] = (tmp57 * tmp64) - (tmp61 * tmp60);
		    tmp65 = c_re(W[4]);
		    tmp67 = c_im(W[4]);
		    X[5 * iostride] = (tmp65 * tmp66) + (tmp67 * tmp68);
		    Y[0] = (tmp65 * tmp68) - (tmp67 * tmp66);
	       }
	  }
     }
     if (i == m) {
	  fftw_real tmp1;
	  fftw_real tmp6;
	  fftw_real tmp4;
	  fftw_real tmp5;
	  fftw_real tmp9;
	  fftw_real tmp11;
	  fftw_real tmp12;
	  fftw_real tmp10;
	  ASSERT_ALIGNED_DOUBLE;
	  tmp1 = X[iostride];
	  tmp6 = Y[-iostride];
	  {
	       fftw_real tmp2;
	       fftw_real tmp3;
	       fftw_real tmp7;
	       fftw_real tmp8;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp2 = X[2 * iostride];
	       tmp3 = X[0];
	       tmp4 = tmp2 + tmp3;
	       tmp5 = K1_732050807 * (tmp2 - tmp3);
	       tmp7 = Y[-2 * iostride];
	       tmp8 = Y[0];
	       tmp9 = tmp7 + tmp8;
	       tmp11 = K1_732050807 * (tmp7 - tmp8);
	  }
	  X[0] = K2_000000000 * (tmp1 + tmp4);
	  tmp12 = (K2_000000000 * tmp1) - tmp4;
	  X[2 * iostride] = tmp11 - tmp12;
	  X[4 * iostride] = tmp12 + tmp11;
	  X[3 * iostride] = K2_000000000 * (tmp6 - tmp9);
	  tmp10 = (K2_000000000 * tmp6) + tmp9;
	  X[iostride] = -(tmp5 + tmp10);
	  X[5 * iostride] = tmp5 - tmp10;
     }
}

static const int twiddle_order[] =
{1, 2, 3, 4, 5};
fftw_codelet_desc fftw_hc2hc_backward_6_desc =
{
     "fftw_hc2hc_backward_6",
     (void (*)()) fftw_hc2hc_backward_6,
     6,
     FFTW_BACKWARD,
     FFTW_HC2HC,
     146,
     5,
     twiddle_order,
};
