/*
 * Copyright (c) 1997-1999 Massachusetts Institute of Technology
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 *
 */

/* This file was automatically generated --- DO NOT EDIT */
/* Generated on Sun Nov  7 20:43:59 EST 1999 */

#include "fftw-int.h"
#include "fftw.h"

/* Generated by: ./genfft -magic-alignment-check -magic-twiddle-load-all -magic-variables 4 -magic-loopi -real2hc 128 */

/*
 * This function contains 956 FP additions, 330 FP multiplications,
 * (or, 812 additions, 186 multiplications, 144 fused multiply/add),
 * 156 stack variables, and 256 memory accesses
 */
static const fftw_real K242980179 = FFTW_KONST(+0.242980179903263889948274162077471118320990783);
static const fftw_real K970031253 = FFTW_KONST(+0.970031253194543992603984207286100251456865962);
static const fftw_real K514102744 = FFTW_KONST(+0.514102744193221726593693838968815772608049120);
static const fftw_real K857728610 = FFTW_KONST(+0.857728610000272069902269984284770137042490799);
static const fftw_real K595699304 = FFTW_KONST(+0.595699304492433343467036528829969889511926338);
static const fftw_real K803207531 = FFTW_KONST(+0.803207531480644909806676512963141923879569427);
static const fftw_real K146730474 = FFTW_KONST(+0.146730474455361751658850129646717819706215317);
static const fftw_real K989176509 = FFTW_KONST(+0.989176509964780973451673738016243063983689533);
static const fftw_real K471396736 = FFTW_KONST(+0.471396736825997648556387625905254377657460319);
static const fftw_real K881921264 = FFTW_KONST(+0.881921264348355029712756863660388349508442621);
static const fftw_real K956940335 = FFTW_KONST(+0.956940335732208864935797886980269969482849206);
static const fftw_real K290284677 = FFTW_KONST(+0.290284677254462367636192375817395274691476278);
static const fftw_real K336889853 = FFTW_KONST(+0.336889853392220050689253212619147570477766780);
static const fftw_real K941544065 = FFTW_KONST(+0.941544065183020778412509402599502357185589796);
static const fftw_real K427555093 = FFTW_KONST(+0.427555093430282094320966856888798534304578629);
static const fftw_real K903989293 = FFTW_KONST(+0.903989293123443331586200297230537048710132025);
static const fftw_real K634393284 = FFTW_KONST(+0.634393284163645498215171613225493370675687095);
static const fftw_real K773010453 = FFTW_KONST(+0.773010453362736960810906609758469800971041293);
static const fftw_real K671558954 = FFTW_KONST(+0.671558954847018400625376850427421803228750632);
static const fftw_real K740951125 = FFTW_KONST(+0.740951125354959091175616897495162729728955309);
static const fftw_real K049067674 = FFTW_KONST(+0.049067674327418014254954976942682658314745363);
static const fftw_real K998795456 = FFTW_KONST(+0.998795456205172392714771604759100694443203615);
static const fftw_real K995184726 = FFTW_KONST(+0.995184726672196886244836953109479921575474869);
static const fftw_real K098017140 = FFTW_KONST(+0.098017140329560601994195563888641845861136673);
static const fftw_real K555570233 = FFTW_KONST(+0.555570233019602224742830813948532874374937191);
static const fftw_real K831469612 = FFTW_KONST(+0.831469612302545237078788377617905756738560812);
static const fftw_real K195090322 = FFTW_KONST(+0.195090322016128267848284868477022240927691618);
static const fftw_real K980785280 = FFTW_KONST(+0.980785280403230449126182236134239036973933731);
static const fftw_real K382683432 = FFTW_KONST(+0.382683432365089771728459984030398866761344562);
static const fftw_real K923879532 = FFTW_KONST(+0.923879532511286756128183189396788286822416626);
static const fftw_real K707106781 = FFTW_KONST(+0.707106781186547524400844362104849039284835938);

/*
 * Generator Id's : 
 * $Id: exprdag.ml,v 1.41 1999/05/26 15:44:14 fftw Exp $
 * $Id: fft.ml,v 1.43 1999/05/17 19:44:18 fftw Exp $
 * $Id: to_c.ml,v 1.25 1999/10/26 21:41:32 stevenj Exp $
 */

void fftw_real2hc_128(const fftw_real *input, fftw_real *real_output, fftw_real *imag_output, int istride, int real_ostride, int imag_ostride)
{
     fftw_real tmp783;
     fftw_real tmp15;
     fftw_real tmp625;
     fftw_real tmp862;
     fftw_real tmp131;
     fftw_real tmp461;
     fftw_real tmp364;
     fftw_real tmp530;
     fftw_real tmp46;
     fftw_real tmp626;
     fftw_real tmp790;
     fftw_real tmp865;
     fftw_real tmp148;
     fftw_real tmp369;
     fftw_real tmp466;
     fftw_real tmp533;
     fftw_real tmp30;
     fftw_real tmp708;
     fftw_real tmp786;
     fftw_real tmp863;
     fftw_real tmp138;
     fftw_real tmp531;
     fftw_real tmp367;
     fftw_real tmp462;
     fftw_real tmp307;
     fftw_real tmp419;
     fftw_real tmp509;
     fftw_real tmp583;
     fftw_real tmp352;
     fftw_real tmp423;
     fftw_real tmp520;
     fftw_real tmp587;
     fftw_real tmp677;
     fftw_real tmp747;
     fftw_real tmp841;
     fftw_real tmp915;
     fftw_real tmp852;
     fftw_real tmp919;
     fftw_real tmp700;
     fftw_real tmp748;
     fftw_real tmp750;
     fftw_real tmp692;
     fftw_real tmp701;
     fftw_real tmp751;
     fftw_real tmp855;
     fftw_real tmp916;
     fftw_real tmp848;
     fftw_real tmp918;
     fftw_real tmp324;
     fftw_real tmp353;
     fftw_real tmp512;
     fftw_real tmp521;
     fftw_real tmp515;
     fftw_real tmp522;
     fftw_real tmp341;
     fftw_real tmp354;
     fftw_real tmp61;
     fftw_real tmp627;
     fftw_real tmp793;
     fftw_real tmp866;
     fftw_real tmp157;
     fftw_real tmp370;
     fftw_real tmp469;
     fftw_real tmp534;
     fftw_real tmp109;
     fftw_real tmp633;
     fftw_real tmp809;
     fftw_real tmp900;
     fftw_real tmp812;
     fftw_real tmp901;
     fftw_real tmp484;
     fftw_real tmp569;
     fftw_real tmp193;
     fftw_real tmp405;
     fftw_real tmp481;
     fftw_real tmp568;
     fftw_real tmp200;
     fftw_real tmp404;
     fftw_real tmp124;
     fftw_real tmp634;
     fftw_real tmp78;
     fftw_real tmp630;
     fftw_real tmp800;
     fftw_real tmp904;
     fftw_real tmp803;
     fftw_real tmp903;
     fftw_real tmp477;
     fftw_real tmp571;
     fftw_real tmp172;
     fftw_real tmp407;
     fftw_real tmp474;
     fftw_real tmp572;
     fftw_real tmp179;
     fftw_real tmp408;
     fftw_real tmp93;
     fftw_real tmp631;
     fftw_real tmp230;
     fftw_real tmp415;
     fftw_real tmp490;
     fftw_real tmp579;
     fftw_real tmp275;
     fftw_real tmp413;
     fftw_real tmp501;
     fftw_real tmp577;
     fftw_real tmp644;
     fftw_real tmp740;
     fftw_real tmp820;
     fftw_real tmp911;
     fftw_real tmp831;
     fftw_real tmp909;
     fftw_real tmp667;
     fftw_real tmp741;
     fftw_real tmp743;
     fftw_real tmp659;
     fftw_real tmp668;
     fftw_real tmp744;
     fftw_real tmp834;
     fftw_real tmp912;
     fftw_real tmp827;
     fftw_real tmp908;
     fftw_real tmp247;
     fftw_real tmp276;
     fftw_real tmp493;
     fftw_real tmp502;
     fftw_real tmp496;
     fftw_real tmp503;
     fftw_real tmp264;
     fftw_real tmp277;
     ASSERT_ALIGNED_DOUBLE;
     {
	  fftw_real tmp3;
	  fftw_real tmp127;
	  fftw_real tmp13;
	  fftw_real tmp129;
	  fftw_real tmp6;
	  fftw_real tmp363;
	  fftw_real tmp10;
	  fftw_real tmp128;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp1;
	       fftw_real tmp2;
	       fftw_real tmp11;
	       fftw_real tmp12;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp1 = input[0];
	       tmp2 = input[64 * istride];
	       tmp3 = tmp1 + tmp2;
	       tmp127 = tmp1 - tmp2;
	       tmp11 = input[112 * istride];
	       tmp12 = input[48 * istride];
	       tmp13 = tmp11 + tmp12;
	       tmp129 = tmp11 - tmp12;
	  }
	  {
	       fftw_real tmp4;
	       fftw_real tmp5;
	       fftw_real tmp8;
	       fftw_real tmp9;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp4 = input[32 * istride];
	       tmp5 = input[96 * istride];
	       tmp6 = tmp4 + tmp5;
	       tmp363 = tmp4 - tmp5;
	       tmp8 = input[16 * istride];
	       tmp9 = input[80 * istride];
	       tmp10 = tmp8 + tmp9;
	       tmp128 = tmp8 - tmp9;
	  }
	  {
	       fftw_real tmp7;
	       fftw_real tmp14;
	       fftw_real tmp130;
	       fftw_real tmp362;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp783 = tmp3 - tmp6;
	       tmp7 = tmp3 + tmp6;
	       tmp14 = tmp10 + tmp13;
	       tmp15 = tmp7 + tmp14;
	       tmp625 = tmp7 - tmp14;
	       tmp862 = tmp13 - tmp10;
	       tmp130 = K707106781 * (tmp128 + tmp129);
	       tmp131 = tmp127 + tmp130;
	       tmp461 = tmp127 - tmp130;
	       tmp362 = K707106781 * (tmp129 - tmp128);
	       tmp364 = tmp362 - tmp363;
	       tmp530 = tmp363 + tmp362;
	  }
     }
     {
	  fftw_real tmp34;
	  fftw_real tmp140;
	  fftw_real tmp37;
	  fftw_real tmp146;
	  fftw_real tmp41;
	  fftw_real tmp145;
	  fftw_real tmp143;
	  fftw_real tmp44;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp32;
	       fftw_real tmp33;
	       fftw_real tmp35;
	       fftw_real tmp36;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp32 = input[4 * istride];
	       tmp33 = input[68 * istride];
	       tmp34 = tmp32 + tmp33;
	       tmp140 = tmp32 - tmp33;
	       tmp35 = input[36 * istride];
	       tmp36 = input[100 * istride];
	       tmp37 = tmp35 + tmp36;
	       tmp146 = tmp35 - tmp36;
	       {
		    fftw_real tmp39;
		    fftw_real tmp40;
		    fftw_real tmp141;
		    fftw_real tmp42;
		    fftw_real tmp43;
		    fftw_real tmp142;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp39 = input[20 * istride];
		    tmp40 = input[84 * istride];
		    tmp141 = tmp39 - tmp40;
		    tmp42 = input[116 * istride];
		    tmp43 = input[52 * istride];
		    tmp142 = tmp42 - tmp43;
		    tmp41 = tmp39 + tmp40;
		    tmp145 = K707106781 * (tmp142 - tmp141);
		    tmp143 = K707106781 * (tmp141 + tmp142);
		    tmp44 = tmp42 + tmp43;
	       }
	  }
	  {
	       fftw_real tmp38;
	       fftw_real tmp45;
	       fftw_real tmp788;
	       fftw_real tmp789;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp38 = tmp34 + tmp37;
	       tmp45 = tmp41 + tmp44;
	       tmp46 = tmp38 + tmp45;
	       tmp626 = tmp38 - tmp45;
	       tmp788 = tmp34 - tmp37;
	       tmp789 = tmp44 - tmp41;
	       tmp790 = (K923879532 * tmp788) + (K382683432 * tmp789);
	       tmp865 = (K923879532 * tmp789) - (K382683432 * tmp788);
	  }
	  {
	       fftw_real tmp144;
	       fftw_real tmp147;
	       fftw_real tmp464;
	       fftw_real tmp465;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp144 = tmp140 + tmp143;
	       tmp147 = tmp145 - tmp146;
	       tmp148 = (K980785280 * tmp144) + (K195090322 * tmp147);
	       tmp369 = (K980785280 * tmp147) - (K195090322 * tmp144);
	       tmp464 = tmp140 - tmp143;
	       tmp465 = tmp146 + tmp145;
	       tmp466 = (K831469612 * tmp464) + (K555570233 * tmp465);
	       tmp533 = (K831469612 * tmp465) - (K555570233 * tmp464);
	  }
     }
     {
	  fftw_real tmp18;
	  fftw_real tmp132;
	  fftw_real tmp28;
	  fftw_real tmp136;
	  fftw_real tmp21;
	  fftw_real tmp133;
	  fftw_real tmp25;
	  fftw_real tmp135;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp16;
	       fftw_real tmp17;
	       fftw_real tmp26;
	       fftw_real tmp27;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp16 = input[8 * istride];
	       tmp17 = input[72 * istride];
	       tmp18 = tmp16 + tmp17;
	       tmp132 = tmp16 - tmp17;
	       tmp26 = input[24 * istride];
	       tmp27 = input[88 * istride];
	       tmp28 = tmp26 + tmp27;
	       tmp136 = tmp26 - tmp27;
	  }
	  {
	       fftw_real tmp19;
	       fftw_real tmp20;
	       fftw_real tmp23;
	       fftw_real tmp24;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp19 = input[40 * istride];
	       tmp20 = input[104 * istride];
	       tmp21 = tmp19 + tmp20;
	       tmp133 = tmp19 - tmp20;
	       tmp23 = input[120 * istride];
	       tmp24 = input[56 * istride];
	       tmp25 = tmp23 + tmp24;
	       tmp135 = tmp23 - tmp24;
	  }
	  {
	       fftw_real tmp22;
	       fftw_real tmp29;
	       fftw_real tmp784;
	       fftw_real tmp785;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp22 = tmp18 + tmp21;
	       tmp29 = tmp25 + tmp28;
	       tmp30 = tmp22 + tmp29;
	       tmp708 = tmp29 - tmp22;
	       tmp784 = tmp18 - tmp21;
	       tmp785 = tmp25 - tmp28;
	       tmp786 = K707106781 * (tmp784 + tmp785);
	       tmp863 = K707106781 * (tmp785 - tmp784);
	  }
	  {
	       fftw_real tmp134;
	       fftw_real tmp137;
	       fftw_real tmp365;
	       fftw_real tmp366;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp134 = (K923879532 * tmp132) - (K382683432 * tmp133);
	       tmp137 = (K923879532 * tmp135) + (K382683432 * tmp136);
	       tmp138 = tmp134 + tmp137;
	       tmp531 = tmp137 - tmp134;
	       tmp365 = (K382683432 * tmp135) - (K923879532 * tmp136);
	       tmp366 = (K382683432 * tmp132) + (K923879532 * tmp133);
	       tmp367 = tmp365 - tmp366;
	       tmp462 = tmp366 + tmp365;
	  }
     }
     {
	  fftw_real tmp283;
	  fftw_real tmp671;
	  fftw_real tmp347;
	  fftw_real tmp672;
	  fftw_real tmp290;
	  fftw_real tmp344;
	  fftw_real tmp674;
	  fftw_real tmp675;
	  fftw_real tmp697;
	  fftw_real tmp698;
	  fftw_real tmp298;
	  fftw_real tmp350;
	  fftw_real tmp838;
	  fftw_real tmp694;
	  fftw_real tmp695;
	  fftw_real tmp305;
	  fftw_real tmp349;
	  fftw_real tmp839;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp281;
	       fftw_real tmp282;
	       fftw_real tmp345;
	       fftw_real tmp346;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp281 = input[127 * istride];
	       tmp282 = input[63 * istride];
	       tmp283 = tmp281 - tmp282;
	       tmp671 = tmp281 + tmp282;
	       tmp345 = input[31 * istride];
	       tmp346 = input[95 * istride];
	       tmp347 = tmp345 - tmp346;
	       tmp672 = tmp345 + tmp346;
	  }
	  {
	       fftw_real tmp284;
	       fftw_real tmp285;
	       fftw_real tmp286;
	       fftw_real tmp287;
	       fftw_real tmp288;
	       fftw_real tmp289;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp284 = input[15 * istride];
	       tmp285 = input[79 * istride];
	       tmp286 = tmp284 - tmp285;
	       tmp287 = input[111 * istride];
	       tmp288 = input[47 * istride];
	       tmp289 = tmp287 - tmp288;
	       tmp290 = K707106781 * (tmp286 + tmp289);
	       tmp344 = K707106781 * (tmp289 - tmp286);
	       tmp674 = tmp284 + tmp285;
	       tmp675 = tmp287 + tmp288;
	  }
	  {
	       fftw_real tmp294;
	       fftw_real tmp297;
	       fftw_real tmp301;
	       fftw_real tmp304;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp292;
		    fftw_real tmp293;
		    fftw_real tmp295;
		    fftw_real tmp296;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp292 = input[7 * istride];
		    tmp293 = input[71 * istride];
		    tmp294 = tmp292 - tmp293;
		    tmp697 = tmp292 + tmp293;
		    tmp295 = input[39 * istride];
		    tmp296 = input[103 * istride];
		    tmp297 = tmp295 - tmp296;
		    tmp698 = tmp295 + tmp296;
	       }
	       tmp298 = (K923879532 * tmp294) - (K382683432 * tmp297);
	       tmp350 = (K382683432 * tmp294) + (K923879532 * tmp297);
	       tmp838 = tmp697 - tmp698;
	       {
		    fftw_real tmp299;
		    fftw_real tmp300;
		    fftw_real tmp302;
		    fftw_real tmp303;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp299 = input[119 * istride];
		    tmp300 = input[55 * istride];
		    tmp301 = tmp299 - tmp300;
		    tmp694 = tmp299 + tmp300;
		    tmp302 = input[23 * istride];
		    tmp303 = input[87 * istride];
		    tmp304 = tmp302 - tmp303;
		    tmp695 = tmp302 + tmp303;
	       }
	       tmp305 = (K923879532 * tmp301) + (K382683432 * tmp304);
	       tmp349 = (K382683432 * tmp301) - (K923879532 * tmp304);
	       tmp839 = tmp694 - tmp695;
	  }
	  {
	       fftw_real tmp291;
	       fftw_real tmp306;
	       fftw_real tmp507;
	       fftw_real tmp508;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp291 = tmp283 + tmp290;
	       tmp306 = tmp298 + tmp305;
	       tmp307 = tmp291 + tmp306;
	       tmp419 = tmp291 - tmp306;
	       tmp507 = tmp283 - tmp290;
	       tmp508 = tmp350 + tmp349;
	       tmp509 = tmp507 + tmp508;
	       tmp583 = tmp507 - tmp508;
	  }
	  {
	       fftw_real tmp348;
	       fftw_real tmp351;
	       fftw_real tmp518;
	       fftw_real tmp519;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp348 = tmp344 - tmp347;
	       tmp351 = tmp349 - tmp350;
	       tmp352 = tmp348 + tmp351;
	       tmp423 = tmp351 - tmp348;
	       tmp518 = tmp347 + tmp344;
	       tmp519 = tmp305 - tmp298;
	       tmp520 = tmp518 + tmp519;
	       tmp587 = tmp519 - tmp518;
	  }
	  {
	       fftw_real tmp673;
	       fftw_real tmp676;
	       fftw_real tmp837;
	       fftw_real tmp840;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp673 = tmp671 + tmp672;
	       tmp676 = tmp674 + tmp675;
	       tmp677 = tmp673 - tmp676;
	       tmp747 = tmp673 + tmp676;
	       tmp837 = tmp671 - tmp672;
	       tmp840 = K707106781 * (tmp838 + tmp839);
	       tmp841 = tmp837 + tmp840;
	       tmp915 = tmp837 - tmp840;
	  }
	  {
	       fftw_real tmp850;
	       fftw_real tmp851;
	       fftw_real tmp696;
	       fftw_real tmp699;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp850 = tmp675 - tmp674;
	       tmp851 = K707106781 * (tmp839 - tmp838);
	       tmp852 = tmp850 + tmp851;
	       tmp919 = tmp851 - tmp850;
	       tmp696 = tmp694 + tmp695;
	       tmp699 = tmp697 + tmp698;
	       tmp700 = tmp696 - tmp699;
	       tmp748 = tmp699 + tmp696;
	  }
     }
     {
	  fftw_real tmp310;
	  fftw_real tmp842;
	  fftw_real tmp680;
	  fftw_real tmp322;
	  fftw_real tmp334;
	  fftw_real tmp336;
	  fftw_real tmp690;
	  fftw_real tmp846;
	  fftw_real tmp327;
	  fftw_real tmp845;
	  fftw_real tmp687;
	  fftw_real tmp339;
	  fftw_real tmp317;
	  fftw_real tmp319;
	  fftw_real tmp683;
	  fftw_real tmp843;
	  fftw_real tmp510;
	  fftw_real tmp511;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp308;
	       fftw_real tmp309;
	       fftw_real tmp678;
	       fftw_real tmp320;
	       fftw_real tmp321;
	       fftw_real tmp679;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp308 = input[3 * istride];
	       tmp309 = input[67 * istride];
	       tmp678 = tmp308 + tmp309;
	       tmp320 = input[35 * istride];
	       tmp321 = input[99 * istride];
	       tmp679 = tmp320 + tmp321;
	       tmp310 = tmp308 - tmp309;
	       tmp842 = tmp678 - tmp679;
	       tmp680 = tmp678 + tmp679;
	       tmp322 = tmp320 - tmp321;
	  }
	  {
	       fftw_real tmp330;
	       fftw_real tmp688;
	       fftw_real tmp333;
	       fftw_real tmp689;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp328;
		    fftw_real tmp329;
		    fftw_real tmp331;
		    fftw_real tmp332;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp328 = input[11 * istride];
		    tmp329 = input[75 * istride];
		    tmp330 = tmp328 - tmp329;
		    tmp688 = tmp328 + tmp329;
		    tmp331 = input[107 * istride];
		    tmp332 = input[43 * istride];
		    tmp333 = tmp331 - tmp332;
		    tmp689 = tmp331 + tmp332;
	       }
	       tmp334 = K707106781 * (tmp330 + tmp333);
	       tmp336 = K707106781 * (tmp333 - tmp330);
	       tmp690 = tmp688 + tmp689;
	       tmp846 = tmp689 - tmp688;
	  }
	  {
	       fftw_real tmp325;
	       fftw_real tmp326;
	       fftw_real tmp685;
	       fftw_real tmp337;
	       fftw_real tmp338;
	       fftw_real tmp686;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp325 = input[123 * istride];
	       tmp326 = input[59 * istride];
	       tmp685 = tmp325 + tmp326;
	       tmp337 = input[27 * istride];
	       tmp338 = input[91 * istride];
	       tmp686 = tmp337 + tmp338;
	       tmp327 = tmp325 - tmp326;
	       tmp845 = tmp685 - tmp686;
	       tmp687 = tmp685 + tmp686;
	       tmp339 = tmp337 - tmp338;
	  }
	  {
	       fftw_real tmp313;
	       fftw_real tmp681;
	       fftw_real tmp316;
	       fftw_real tmp682;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp311;
		    fftw_real tmp312;
		    fftw_real tmp314;
		    fftw_real tmp315;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp311 = input[19 * istride];
		    tmp312 = input[83 * istride];
		    tmp313 = tmp311 - tmp312;
		    tmp681 = tmp311 + tmp312;
		    tmp314 = input[115 * istride];
		    tmp315 = input[51 * istride];
		    tmp316 = tmp314 - tmp315;
		    tmp682 = tmp314 + tmp315;
	       }
	       tmp317 = K707106781 * (tmp313 + tmp316);
	       tmp319 = K707106781 * (tmp316 - tmp313);
	       tmp683 = tmp681 + tmp682;
	       tmp843 = tmp682 - tmp681;
	  }
	  {
	       fftw_real tmp684;
	       fftw_real tmp691;
	       fftw_real tmp853;
	       fftw_real tmp854;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp750 = tmp680 + tmp683;
	       tmp684 = tmp680 - tmp683;
	       tmp691 = tmp687 - tmp690;
	       tmp692 = K707106781 * (tmp684 + tmp691);
	       tmp701 = K707106781 * (tmp691 - tmp684);
	       tmp751 = tmp687 + tmp690;
	       tmp853 = (K923879532 * tmp843) - (K382683432 * tmp842);
	       tmp854 = (K382683432 * tmp845) + (K923879532 * tmp846);
	       tmp855 = tmp853 + tmp854;
	       tmp916 = tmp854 - tmp853;
	  }
	  {
	       fftw_real tmp844;
	       fftw_real tmp847;
	       fftw_real tmp318;
	       fftw_real tmp323;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp844 = (K923879532 * tmp842) + (K382683432 * tmp843);
	       tmp847 = (K923879532 * tmp845) - (K382683432 * tmp846);
	       tmp848 = tmp844 + tmp847;
	       tmp918 = tmp847 - tmp844;
	       tmp318 = tmp310 + tmp317;
	       tmp323 = tmp319 - tmp322;
	       tmp324 = (K980785280 * tmp318) + (K195090322 * tmp323);
	       tmp353 = (K980785280 * tmp323) - (K195090322 * tmp318);
	  }
	  tmp510 = tmp310 - tmp317;
	  tmp511 = tmp322 + tmp319;
	  tmp512 = (K831469612 * tmp510) + (K555570233 * tmp511);
	  tmp521 = (K831469612 * tmp511) - (K555570233 * tmp510);
	  {
	       fftw_real tmp513;
	       fftw_real tmp514;
	       fftw_real tmp335;
	       fftw_real tmp340;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp513 = tmp327 - tmp334;
	       tmp514 = tmp339 + tmp336;
	       tmp515 = (K831469612 * tmp513) - (K555570233 * tmp514);
	       tmp522 = (K555570233 * tmp513) + (K831469612 * tmp514);
	       tmp335 = tmp327 + tmp334;
	       tmp340 = tmp336 - tmp339;
	       tmp341 = (K980785280 * tmp335) - (K195090322 * tmp340);
	       tmp354 = (K195090322 * tmp335) + (K980785280 * tmp340);
	  }
     }
     {
	  fftw_real tmp49;
	  fftw_real tmp149;
	  fftw_real tmp52;
	  fftw_real tmp155;
	  fftw_real tmp56;
	  fftw_real tmp154;
	  fftw_real tmp152;
	  fftw_real tmp59;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp47;
	       fftw_real tmp48;
	       fftw_real tmp50;
	       fftw_real tmp51;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp47 = input[124 * istride];
	       tmp48 = input[60 * istride];
	       tmp49 = tmp47 + tmp48;
	       tmp149 = tmp47 - tmp48;
	       tmp50 = input[28 * istride];
	       tmp51 = input[92 * istride];
	       tmp52 = tmp50 + tmp51;
	       tmp155 = tmp50 - tmp51;
	       {
		    fftw_real tmp54;
		    fftw_real tmp55;
		    fftw_real tmp150;
		    fftw_real tmp57;
		    fftw_real tmp58;
		    fftw_real tmp151;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp54 = input[12 * istride];
		    tmp55 = input[76 * istride];
		    tmp150 = tmp54 - tmp55;
		    tmp57 = input[108 * istride];
		    tmp58 = input[44 * istride];
		    tmp151 = tmp57 - tmp58;
		    tmp56 = tmp54 + tmp55;
		    tmp154 = K707106781 * (tmp151 - tmp150);
		    tmp152 = K707106781 * (tmp150 + tmp151);
		    tmp59 = tmp57 + tmp58;
	       }
	  }
	  {
	       fftw_real tmp53;
	       fftw_real tmp60;
	       fftw_real tmp791;
	       fftw_real tmp792;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp53 = tmp49 + tmp52;
	       tmp60 = tmp56 + tmp59;
	       tmp61 = tmp53 + tmp60;
	       tmp627 = tmp53 - tmp60;
	       tmp791 = tmp49 - tmp52;
	       tmp792 = tmp59 - tmp56;
	       tmp793 = (K923879532 * tmp791) - (K382683432 * tmp792);
	       tmp866 = (K382683432 * tmp791) + (K923879532 * tmp792);
	  }
	  {
	       fftw_real tmp153;
	       fftw_real tmp156;
	       fftw_real tmp467;
	       fftw_real tmp468;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp153 = tmp149 + tmp152;
	       tmp156 = tmp154 - tmp155;
	       tmp157 = (K980785280 * tmp153) - (K195090322 * tmp156);
	       tmp370 = (K195090322 * tmp153) + (K980785280 * tmp156);
	       tmp467 = tmp149 - tmp152;
	       tmp468 = tmp155 + tmp154;
	       tmp469 = (K831469612 * tmp467) - (K555570233 * tmp468);
	       tmp534 = (K555570233 * tmp467) + (K831469612 * tmp468);
	  }
     }
     {
	  fftw_real tmp97;
	  fftw_real tmp181;
	  fftw_real tmp119;
	  fftw_real tmp122;
	  fftw_real tmp191;
	  fftw_real tmp197;
	  fftw_real tmp807;
	  fftw_real tmp100;
	  fftw_real tmp195;
	  fftw_real tmp104;
	  fftw_real tmp194;
	  fftw_real tmp184;
	  fftw_real tmp107;
	  fftw_real tmp112;
	  fftw_real tmp115;
	  fftw_real tmp188;
	  fftw_real tmp198;
	  fftw_real tmp806;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp95;
	       fftw_real tmp96;
	       fftw_real tmp189;
	       fftw_real tmp190;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp95 = input[126 * istride];
	       tmp96 = input[62 * istride];
	       tmp97 = tmp95 + tmp96;
	       tmp181 = tmp95 - tmp96;
	       {
		    fftw_real tmp117;
		    fftw_real tmp118;
		    fftw_real tmp120;
		    fftw_real tmp121;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp117 = input[118 * istride];
		    tmp118 = input[54 * istride];
		    tmp119 = tmp117 + tmp118;
		    tmp189 = tmp117 - tmp118;
		    tmp120 = input[22 * istride];
		    tmp121 = input[86 * istride];
		    tmp122 = tmp120 + tmp121;
		    tmp190 = tmp120 - tmp121;
	       }
	       tmp191 = (K923879532 * tmp189) + (K382683432 * tmp190);
	       tmp197 = (K382683432 * tmp189) - (K923879532 * tmp190);
	       tmp807 = tmp119 - tmp122;
	  }
	  {
	       fftw_real tmp98;
	       fftw_real tmp99;
	       fftw_real tmp186;
	       fftw_real tmp187;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp98 = input[30 * istride];
	       tmp99 = input[94 * istride];
	       tmp100 = tmp98 + tmp99;
	       tmp195 = tmp98 - tmp99;
	       {
		    fftw_real tmp102;
		    fftw_real tmp103;
		    fftw_real tmp182;
		    fftw_real tmp105;
		    fftw_real tmp106;
		    fftw_real tmp183;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp102 = input[14 * istride];
		    tmp103 = input[78 * istride];
		    tmp182 = tmp102 - tmp103;
		    tmp105 = input[110 * istride];
		    tmp106 = input[46 * istride];
		    tmp183 = tmp105 - tmp106;
		    tmp104 = tmp102 + tmp103;
		    tmp194 = K707106781 * (tmp183 - tmp182);
		    tmp184 = K707106781 * (tmp182 + tmp183);
		    tmp107 = tmp105 + tmp106;
	       }
	       {
		    fftw_real tmp110;
		    fftw_real tmp111;
		    fftw_real tmp113;
		    fftw_real tmp114;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp110 = input[6 * istride];
		    tmp111 = input[70 * istride];
		    tmp112 = tmp110 + tmp111;
		    tmp186 = tmp110 - tmp111;
		    tmp113 = input[38 * istride];
		    tmp114 = input[102 * istride];
		    tmp115 = tmp113 + tmp114;
		    tmp187 = tmp113 - tmp114;
	       }
	       tmp188 = (K923879532 * tmp186) - (K382683432 * tmp187);
	       tmp198 = (K382683432 * tmp186) + (K923879532 * tmp187);
	       tmp806 = tmp112 - tmp115;
	  }
	  {
	       fftw_real tmp101;
	       fftw_real tmp108;
	       fftw_real tmp805;
	       fftw_real tmp808;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp101 = tmp97 + tmp100;
	       tmp108 = tmp104 + tmp107;
	       tmp109 = tmp101 + tmp108;
	       tmp633 = tmp101 - tmp108;
	       tmp805 = tmp97 - tmp100;
	       tmp808 = K707106781 * (tmp806 + tmp807);
	       tmp809 = tmp805 + tmp808;
	       tmp900 = tmp805 - tmp808;
	  }
	  {
	       fftw_real tmp810;
	       fftw_real tmp811;
	       fftw_real tmp482;
	       fftw_real tmp483;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp810 = tmp107 - tmp104;
	       tmp811 = K707106781 * (tmp807 - tmp806);
	       tmp812 = tmp810 + tmp811;
	       tmp901 = tmp811 - tmp810;
	       tmp482 = tmp195 + tmp194;
	       tmp483 = tmp191 - tmp188;
	       tmp484 = tmp482 + tmp483;
	       tmp569 = tmp483 - tmp482;
	  }
	  {
	       fftw_real tmp185;
	       fftw_real tmp192;
	       fftw_real tmp479;
	       fftw_real tmp480;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp185 = tmp181 + tmp184;
	       tmp192 = tmp188 + tmp191;
	       tmp193 = tmp185 + tmp192;
	       tmp405 = tmp185 - tmp192;
	       tmp479 = tmp181 - tmp184;
	       tmp480 = tmp198 + tmp197;
	       tmp481 = tmp479 + tmp480;
	       tmp568 = tmp479 - tmp480;
	  }
	  {
	       fftw_real tmp196;
	       fftw_real tmp199;
	       fftw_real tmp116;
	       fftw_real tmp123;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp196 = tmp194 - tmp195;
	       tmp199 = tmp197 - tmp198;
	       tmp200 = tmp196 + tmp199;
	       tmp404 = tmp199 - tmp196;
	       tmp116 = tmp112 + tmp115;
	       tmp123 = tmp119 + tmp122;
	       tmp124 = tmp116 + tmp123;
	       tmp634 = tmp123 - tmp116;
	  }
     }
     {
	  fftw_real tmp66;
	  fftw_real tmp173;
	  fftw_real tmp88;
	  fftw_real tmp91;
	  fftw_real tmp167;
	  fftw_real tmp177;
	  fftw_real tmp798;
	  fftw_real tmp69;
	  fftw_real tmp163;
	  fftw_real tmp73;
	  fftw_real tmp174;
	  fftw_real tmp162;
	  fftw_real tmp76;
	  fftw_real tmp81;
	  fftw_real tmp84;
	  fftw_real tmp170;
	  fftw_real tmp176;
	  fftw_real tmp797;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp64;
	       fftw_real tmp65;
	       fftw_real tmp165;
	       fftw_real tmp166;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp64 = input[2 * istride];
	       tmp65 = input[66 * istride];
	       tmp66 = tmp64 + tmp65;
	       tmp173 = tmp64 - tmp65;
	       {
		    fftw_real tmp86;
		    fftw_real tmp87;
		    fftw_real tmp89;
		    fftw_real tmp90;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp86 = input[122 * istride];
		    tmp87 = input[58 * istride];
		    tmp88 = tmp86 + tmp87;
		    tmp165 = tmp86 - tmp87;
		    tmp89 = input[26 * istride];
		    tmp90 = input[90 * istride];
		    tmp91 = tmp89 + tmp90;
		    tmp166 = tmp89 - tmp90;
	       }
	       tmp167 = (K382683432 * tmp165) - (K923879532 * tmp166);
	       tmp177 = (K923879532 * tmp165) + (K382683432 * tmp166);
	       tmp798 = tmp88 - tmp91;
	  }
	  {
	       fftw_real tmp67;
	       fftw_real tmp68;
	       fftw_real tmp168;
	       fftw_real tmp169;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp67 = input[34 * istride];
	       tmp68 = input[98 * istride];
	       tmp69 = tmp67 + tmp68;
	       tmp163 = tmp67 - tmp68;
	       {
		    fftw_real tmp71;
		    fftw_real tmp72;
		    fftw_real tmp161;
		    fftw_real tmp74;
		    fftw_real tmp75;
		    fftw_real tmp160;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp71 = input[18 * istride];
		    tmp72 = input[82 * istride];
		    tmp161 = tmp71 - tmp72;
		    tmp74 = input[114 * istride];
		    tmp75 = input[50 * istride];
		    tmp160 = tmp74 - tmp75;
		    tmp73 = tmp71 + tmp72;
		    tmp174 = K707106781 * (tmp161 + tmp160);
		    tmp162 = K707106781 * (tmp160 - tmp161);
		    tmp76 = tmp74 + tmp75;
	       }
	       {
		    fftw_real tmp79;
		    fftw_real tmp80;
		    fftw_real tmp82;
		    fftw_real tmp83;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp79 = input[10 * istride];
		    tmp80 = input[74 * istride];
		    tmp81 = tmp79 + tmp80;
		    tmp168 = tmp79 - tmp80;
		    tmp82 = input[42 * istride];
		    tmp83 = input[106 * istride];
		    tmp84 = tmp82 + tmp83;
		    tmp169 = tmp82 - tmp83;
	       }
	       tmp170 = (K382683432 * tmp168) + (K923879532 * tmp169);
	       tmp176 = (K923879532 * tmp168) - (K382683432 * tmp169);
	       tmp797 = tmp81 - tmp84;
	  }
	  {
	       fftw_real tmp70;
	       fftw_real tmp77;
	       fftw_real tmp796;
	       fftw_real tmp799;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp70 = tmp66 + tmp69;
	       tmp77 = tmp73 + tmp76;
	       tmp78 = tmp70 + tmp77;
	       tmp630 = tmp70 - tmp77;
	       tmp796 = tmp66 - tmp69;
	       tmp799 = K707106781 * (tmp797 + tmp798);
	       tmp800 = tmp796 + tmp799;
	       tmp904 = tmp796 - tmp799;
	  }
	  {
	       fftw_real tmp801;
	       fftw_real tmp802;
	       fftw_real tmp475;
	       fftw_real tmp476;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp801 = tmp76 - tmp73;
	       tmp802 = K707106781 * (tmp798 - tmp797);
	       tmp803 = tmp801 + tmp802;
	       tmp903 = tmp802 - tmp801;
	       tmp475 = tmp163 + tmp162;
	       tmp476 = tmp177 - tmp176;
	       tmp477 = tmp475 + tmp476;
	       tmp571 = tmp476 - tmp475;
	  }
	  {
	       fftw_real tmp164;
	       fftw_real tmp171;
	       fftw_real tmp472;
	       fftw_real tmp473;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp164 = tmp162 - tmp163;
	       tmp171 = tmp167 - tmp170;
	       tmp172 = tmp164 + tmp171;
	       tmp407 = tmp171 - tmp164;
	       tmp472 = tmp173 - tmp174;
	       tmp473 = tmp170 + tmp167;
	       tmp474 = tmp472 + tmp473;
	       tmp572 = tmp472 - tmp473;
	  }
	  {
	       fftw_real tmp175;
	       fftw_real tmp178;
	       fftw_real tmp85;
	       fftw_real tmp92;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp175 = tmp173 + tmp174;
	       tmp178 = tmp176 + tmp177;
	       tmp179 = tmp175 + tmp178;
	       tmp408 = tmp175 - tmp178;
	       tmp85 = tmp81 + tmp84;
	       tmp92 = tmp88 + tmp91;
	       tmp93 = tmp85 + tmp92;
	       tmp631 = tmp92 - tmp85;
	  }
     }
     {
	  fftw_real tmp206;
	  fftw_real tmp638;
	  fftw_real tmp270;
	  fftw_real tmp639;
	  fftw_real tmp213;
	  fftw_real tmp267;
	  fftw_real tmp641;
	  fftw_real tmp642;
	  fftw_real tmp664;
	  fftw_real tmp665;
	  fftw_real tmp221;
	  fftw_real tmp273;
	  fftw_real tmp817;
	  fftw_real tmp661;
	  fftw_real tmp662;
	  fftw_real tmp228;
	  fftw_real tmp272;
	  fftw_real tmp818;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp204;
	       fftw_real tmp205;
	       fftw_real tmp268;
	       fftw_real tmp269;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp204 = input[istride];
	       tmp205 = input[65 * istride];
	       tmp206 = tmp204 - tmp205;
	       tmp638 = tmp204 + tmp205;
	       tmp268 = input[33 * istride];
	       tmp269 = input[97 * istride];
	       tmp270 = tmp268 - tmp269;
	       tmp639 = tmp268 + tmp269;
	  }
	  {
	       fftw_real tmp207;
	       fftw_real tmp208;
	       fftw_real tmp209;
	       fftw_real tmp210;
	       fftw_real tmp211;
	       fftw_real tmp212;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp207 = input[17 * istride];
	       tmp208 = input[81 * istride];
	       tmp209 = tmp207 - tmp208;
	       tmp210 = input[113 * istride];
	       tmp211 = input[49 * istride];
	       tmp212 = tmp210 - tmp211;
	       tmp213 = K707106781 * (tmp209 + tmp212);
	       tmp267 = K707106781 * (tmp212 - tmp209);
	       tmp641 = tmp207 + tmp208;
	       tmp642 = tmp210 + tmp211;
	  }
	  {
	       fftw_real tmp217;
	       fftw_real tmp220;
	       fftw_real tmp224;
	       fftw_real tmp227;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp215;
		    fftw_real tmp216;
		    fftw_real tmp218;
		    fftw_real tmp219;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp215 = input[9 * istride];
		    tmp216 = input[73 * istride];
		    tmp217 = tmp215 - tmp216;
		    tmp664 = tmp215 + tmp216;
		    tmp218 = input[41 * istride];
		    tmp219 = input[105 * istride];
		    tmp220 = tmp218 - tmp219;
		    tmp665 = tmp218 + tmp219;
	       }
	       tmp221 = (K923879532 * tmp217) - (K382683432 * tmp220);
	       tmp273 = (K382683432 * tmp217) + (K923879532 * tmp220);
	       tmp817 = tmp664 - tmp665;
	       {
		    fftw_real tmp222;
		    fftw_real tmp223;
		    fftw_real tmp225;
		    fftw_real tmp226;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp222 = input[121 * istride];
		    tmp223 = input[57 * istride];
		    tmp224 = tmp222 - tmp223;
		    tmp661 = tmp222 + tmp223;
		    tmp225 = input[25 * istride];
		    tmp226 = input[89 * istride];
		    tmp227 = tmp225 - tmp226;
		    tmp662 = tmp225 + tmp226;
	       }
	       tmp228 = (K923879532 * tmp224) + (K382683432 * tmp227);
	       tmp272 = (K382683432 * tmp224) - (K923879532 * tmp227);
	       tmp818 = tmp661 - tmp662;
	  }
	  {
	       fftw_real tmp214;
	       fftw_real tmp229;
	       fftw_real tmp488;
	       fftw_real tmp489;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp214 = tmp206 + tmp213;
	       tmp229 = tmp221 + tmp228;
	       tmp230 = tmp214 + tmp229;
	       tmp415 = tmp214 - tmp229;
	       tmp488 = tmp206 - tmp213;
	       tmp489 = tmp273 + tmp272;
	       tmp490 = tmp488 + tmp489;
	       tmp579 = tmp488 - tmp489;
	  }
	  {
	       fftw_real tmp271;
	       fftw_real tmp274;
	       fftw_real tmp499;
	       fftw_real tmp500;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp271 = tmp267 - tmp270;
	       tmp274 = tmp272 - tmp273;
	       tmp275 = tmp271 + tmp274;
	       tmp413 = tmp274 - tmp271;
	       tmp499 = tmp270 + tmp267;
	       tmp500 = tmp228 - tmp221;
	       tmp501 = tmp499 + tmp500;
	       tmp577 = tmp500 - tmp499;
	  }
	  {
	       fftw_real tmp640;
	       fftw_real tmp643;
	       fftw_real tmp816;
	       fftw_real tmp819;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp640 = tmp638 + tmp639;
	       tmp643 = tmp641 + tmp642;
	       tmp644 = tmp640 - tmp643;
	       tmp740 = tmp640 + tmp643;
	       tmp816 = tmp638 - tmp639;
	       tmp819 = K707106781 * (tmp817 + tmp818);
	       tmp820 = tmp816 + tmp819;
	       tmp911 = tmp816 - tmp819;
	  }
	  {
	       fftw_real tmp829;
	       fftw_real tmp830;
	       fftw_real tmp663;
	       fftw_real tmp666;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp829 = tmp642 - tmp641;
	       tmp830 = K707106781 * (tmp818 - tmp817);
	       tmp831 = tmp829 + tmp830;
	       tmp909 = tmp830 - tmp829;
	       tmp663 = tmp661 + tmp662;
	       tmp666 = tmp664 + tmp665;
	       tmp667 = tmp663 - tmp666;
	       tmp741 = tmp666 + tmp663;
	  }
     }
     {
	  fftw_real tmp233;
	  fftw_real tmp821;
	  fftw_real tmp647;
	  fftw_real tmp245;
	  fftw_real tmp257;
	  fftw_real tmp259;
	  fftw_real tmp657;
	  fftw_real tmp825;
	  fftw_real tmp250;
	  fftw_real tmp824;
	  fftw_real tmp654;
	  fftw_real tmp262;
	  fftw_real tmp240;
	  fftw_real tmp242;
	  fftw_real tmp650;
	  fftw_real tmp822;
	  fftw_real tmp491;
	  fftw_real tmp492;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp231;
	       fftw_real tmp232;
	       fftw_real tmp645;
	       fftw_real tmp243;
	       fftw_real tmp244;
	       fftw_real tmp646;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp231 = input[5 * istride];
	       tmp232 = input[69 * istride];
	       tmp645 = tmp231 + tmp232;
	       tmp243 = input[37 * istride];
	       tmp244 = input[101 * istride];
	       tmp646 = tmp243 + tmp244;
	       tmp233 = tmp231 - tmp232;
	       tmp821 = tmp645 - tmp646;
	       tmp647 = tmp645 + tmp646;
	       tmp245 = tmp243 - tmp244;
	  }
	  {
	       fftw_real tmp253;
	       fftw_real tmp655;
	       fftw_real tmp256;
	       fftw_real tmp656;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp251;
		    fftw_real tmp252;
		    fftw_real tmp254;
		    fftw_real tmp255;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp251 = input[13 * istride];
		    tmp252 = input[77 * istride];
		    tmp253 = tmp251 - tmp252;
		    tmp655 = tmp251 + tmp252;
		    tmp254 = input[109 * istride];
		    tmp255 = input[45 * istride];
		    tmp256 = tmp254 - tmp255;
		    tmp656 = tmp254 + tmp255;
	       }
	       tmp257 = K707106781 * (tmp253 + tmp256);
	       tmp259 = K707106781 * (tmp256 - tmp253);
	       tmp657 = tmp655 + tmp656;
	       tmp825 = tmp656 - tmp655;
	  }
	  {
	       fftw_real tmp248;
	       fftw_real tmp249;
	       fftw_real tmp652;
	       fftw_real tmp260;
	       fftw_real tmp261;
	       fftw_real tmp653;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp248 = input[125 * istride];
	       tmp249 = input[61 * istride];
	       tmp652 = tmp248 + tmp249;
	       tmp260 = input[29 * istride];
	       tmp261 = input[93 * istride];
	       tmp653 = tmp260 + tmp261;
	       tmp250 = tmp248 - tmp249;
	       tmp824 = tmp652 - tmp653;
	       tmp654 = tmp652 + tmp653;
	       tmp262 = tmp260 - tmp261;
	  }
	  {
	       fftw_real tmp236;
	       fftw_real tmp648;
	       fftw_real tmp239;
	       fftw_real tmp649;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp234;
		    fftw_real tmp235;
		    fftw_real tmp237;
		    fftw_real tmp238;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp234 = input[21 * istride];
		    tmp235 = input[85 * istride];
		    tmp236 = tmp234 - tmp235;
		    tmp648 = tmp234 + tmp235;
		    tmp237 = input[117 * istride];
		    tmp238 = input[53 * istride];
		    tmp239 = tmp237 - tmp238;
		    tmp649 = tmp237 + tmp238;
	       }
	       tmp240 = K707106781 * (tmp236 + tmp239);
	       tmp242 = K707106781 * (tmp239 - tmp236);
	       tmp650 = tmp648 + tmp649;
	       tmp822 = tmp649 - tmp648;
	  }
	  {
	       fftw_real tmp651;
	       fftw_real tmp658;
	       fftw_real tmp832;
	       fftw_real tmp833;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp743 = tmp647 + tmp650;
	       tmp651 = tmp647 - tmp650;
	       tmp658 = tmp654 - tmp657;
	       tmp659 = K707106781 * (tmp651 + tmp658);
	       tmp668 = K707106781 * (tmp658 - tmp651);
	       tmp744 = tmp654 + tmp657;
	       tmp832 = (K923879532 * tmp822) - (K382683432 * tmp821);
	       tmp833 = (K382683432 * tmp824) + (K923879532 * tmp825);
	       tmp834 = tmp832 + tmp833;
	       tmp912 = tmp833 - tmp832;
	  }
	  {
	       fftw_real tmp823;
	       fftw_real tmp826;
	       fftw_real tmp241;
	       fftw_real tmp246;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp823 = (K923879532 * tmp821) + (K382683432 * tmp822);
	       tmp826 = (K923879532 * tmp824) - (K382683432 * tmp825);
	       tmp827 = tmp823 + tmp826;
	       tmp908 = tmp826 - tmp823;
	       tmp241 = tmp233 + tmp240;
	       tmp246 = tmp242 - tmp245;
	       tmp247 = (K980785280 * tmp241) + (K195090322 * tmp246);
	       tmp276 = (K980785280 * tmp246) - (K195090322 * tmp241);
	  }
	  tmp491 = tmp233 - tmp240;
	  tmp492 = tmp245 + tmp242;
	  tmp493 = (K831469612 * tmp491) + (K555570233 * tmp492);
	  tmp502 = (K831469612 * tmp492) - (K555570233 * tmp491);
	  {
	       fftw_real tmp494;
	       fftw_real tmp495;
	       fftw_real tmp258;
	       fftw_real tmp263;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp494 = tmp250 - tmp257;
	       tmp495 = tmp262 + tmp259;
	       tmp496 = (K831469612 * tmp494) - (K555570233 * tmp495);
	       tmp503 = (K555570233 * tmp494) + (K831469612 * tmp495);
	       tmp258 = tmp250 + tmp257;
	       tmp263 = tmp259 - tmp262;
	       tmp264 = (K980785280 * tmp258) - (K195090322 * tmp263);
	       tmp277 = (K195090322 * tmp258) + (K980785280 * tmp263);
	  }
     }
     {
	  fftw_real tmp63;
	  fftw_real tmp739;
	  fftw_real tmp753;
	  fftw_real tmp759;
	  fftw_real tmp126;
	  fftw_real tmp755;
	  fftw_real tmp746;
	  fftw_real tmp758;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp31;
	       fftw_real tmp62;
	       fftw_real tmp749;
	       fftw_real tmp752;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp31 = tmp15 + tmp30;
	       tmp62 = tmp46 + tmp61;
	       tmp63 = tmp31 + tmp62;
	       tmp739 = tmp31 - tmp62;
	       tmp749 = tmp747 + tmp748;
	       tmp752 = tmp750 + tmp751;
	       tmp753 = tmp749 - tmp752;
	       tmp759 = tmp749 + tmp752;
	  }
	  {
	       fftw_real tmp94;
	       fftw_real tmp125;
	       fftw_real tmp742;
	       fftw_real tmp745;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp94 = tmp78 + tmp93;
	       tmp125 = tmp109 + tmp124;
	       tmp126 = tmp94 + tmp125;
	       tmp755 = tmp125 - tmp94;
	       tmp742 = tmp740 + tmp741;
	       tmp745 = tmp743 + tmp744;
	       tmp746 = tmp742 - tmp745;
	       tmp758 = tmp742 + tmp745;
	  }
	  {
	       fftw_real tmp757;
	       fftw_real tmp760;
	       fftw_real tmp754;
	       fftw_real tmp756;
	       ASSERT_ALIGNED_DOUBLE;
	       real_output[32 * real_ostride] = tmp63 - tmp126;
	       tmp757 = tmp63 + tmp126;
	       tmp760 = tmp758 + tmp759;
	       real_output[64 * real_ostride] = tmp757 - tmp760;
	       real_output[0] = tmp757 + tmp760;
	       imag_output[32 * imag_ostride] = tmp759 - tmp758;
	       tmp754 = K707106781 * (tmp746 + tmp753);
	       real_output[48 * real_ostride] = tmp739 - tmp754;
	       real_output[16 * real_ostride] = tmp739 + tmp754;
	       tmp756 = K707106781 * (tmp753 - tmp746);
	       imag_output[16 * imag_ostride] = tmp755 + tmp756;
	       imag_output[48 * imag_ostride] = tmp756 - tmp755;
	  }
     }
     {
	  fftw_real tmp761;
	  fftw_real tmp774;
	  fftw_real tmp764;
	  fftw_real tmp773;
	  fftw_real tmp768;
	  fftw_real tmp778;
	  fftw_real tmp771;
	  fftw_real tmp779;
	  fftw_real tmp762;
	  fftw_real tmp763;
	  ASSERT_ALIGNED_DOUBLE;
	  tmp761 = tmp15 - tmp30;
	  tmp774 = tmp61 - tmp46;
	  tmp762 = tmp78 - tmp93;
	  tmp763 = tmp109 - tmp124;
	  tmp764 = K707106781 * (tmp762 + tmp763);
	  tmp773 = K707106781 * (tmp763 - tmp762);
	  {
	       fftw_real tmp766;
	       fftw_real tmp767;
	       fftw_real tmp769;
	       fftw_real tmp770;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp766 = tmp740 - tmp741;
	       tmp767 = tmp744 - tmp743;
	       tmp768 = (K923879532 * tmp766) + (K382683432 * tmp767);
	       tmp778 = (K923879532 * tmp767) - (K382683432 * tmp766);
	       tmp769 = tmp747 - tmp748;
	       tmp770 = tmp751 - tmp750;
	       tmp771 = (K923879532 * tmp769) - (K382683432 * tmp770);
	       tmp779 = (K382683432 * tmp769) + (K923879532 * tmp770);
	  }
	  {
	       fftw_real tmp765;
	       fftw_real tmp772;
	       fftw_real tmp781;
	       fftw_real tmp782;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp765 = tmp761 + tmp764;
	       tmp772 = tmp768 + tmp771;
	       real_output[56 * real_ostride] = tmp765 - tmp772;
	       real_output[8 * real_ostride] = tmp765 + tmp772;
	       tmp781 = tmp761 - tmp764;
	       tmp782 = tmp779 - tmp778;
	       real_output[40 * real_ostride] = tmp781 - tmp782;
	       real_output[24 * real_ostride] = tmp781 + tmp782;
	  }
	  {
	       fftw_real tmp777;
	       fftw_real tmp780;
	       fftw_real tmp775;
	       fftw_real tmp776;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp777 = tmp774 + tmp773;
	       tmp780 = tmp778 + tmp779;
	       imag_output[8 * imag_ostride] = tmp777 + tmp780;
	       imag_output[56 * imag_ostride] = tmp780 - tmp777;
	       tmp775 = tmp773 - tmp774;
	       tmp776 = tmp771 - tmp768;
	       imag_output[24 * imag_ostride] = tmp775 + tmp776;
	       imag_output[40 * imag_ostride] = tmp776 - tmp775;
	  }
     }
     {
	  fftw_real tmp159;
	  fftw_real tmp381;
	  fftw_real tmp202;
	  fftw_real tmp391;
	  fftw_real tmp372;
	  fftw_real tmp392;
	  fftw_real tmp361;
	  fftw_real tmp382;
	  fftw_real tmp357;
	  fftw_real tmp377;
	  fftw_real tmp389;
	  fftw_real tmp397;
	  fftw_real tmp280;
	  fftw_real tmp376;
	  fftw_real tmp386;
	  fftw_real tmp396;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp139;
	       fftw_real tmp158;
	       fftw_real tmp359;
	       fftw_real tmp360;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp139 = tmp131 + tmp138;
	       tmp158 = tmp148 + tmp157;
	       tmp159 = tmp139 + tmp158;
	       tmp381 = tmp139 - tmp158;
	       {
		    fftw_real tmp180;
		    fftw_real tmp201;
		    fftw_real tmp368;
		    fftw_real tmp371;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp180 = (K098017140 * tmp172) + (K995184726 * tmp179);
		    tmp201 = (K995184726 * tmp193) - (K098017140 * tmp200);
		    tmp202 = tmp180 + tmp201;
		    tmp391 = tmp201 - tmp180;
		    tmp368 = tmp364 + tmp367;
		    tmp371 = tmp369 + tmp370;
		    tmp372 = tmp368 + tmp371;
		    tmp392 = tmp371 - tmp368;
	       }
	       tmp359 = (K995184726 * tmp172) - (K098017140 * tmp179);
	       tmp360 = (K995184726 * tmp200) + (K098017140 * tmp193);
	       tmp361 = tmp359 + tmp360;
	       tmp382 = tmp360 - tmp359;
	       {
		    fftw_real tmp343;
		    fftw_real tmp387;
		    fftw_real tmp356;
		    fftw_real tmp388;
		    fftw_real tmp342;
		    fftw_real tmp355;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp342 = tmp324 + tmp341;
		    tmp343 = tmp307 + tmp342;
		    tmp387 = tmp307 - tmp342;
		    tmp355 = tmp353 + tmp354;
		    tmp356 = tmp352 + tmp355;
		    tmp388 = tmp355 - tmp352;
		    tmp357 = (K998795456 * tmp343) - (K049067674 * tmp356);
		    tmp377 = (K049067674 * tmp343) + (K998795456 * tmp356);
		    tmp389 = (K740951125 * tmp387) - (K671558954 * tmp388);
		    tmp397 = (K671558954 * tmp387) + (K740951125 * tmp388);
	       }
	       {
		    fftw_real tmp266;
		    fftw_real tmp384;
		    fftw_real tmp279;
		    fftw_real tmp385;
		    fftw_real tmp265;
		    fftw_real tmp278;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp265 = tmp247 + tmp264;
		    tmp266 = tmp230 + tmp265;
		    tmp384 = tmp230 - tmp265;
		    tmp278 = tmp276 + tmp277;
		    tmp279 = tmp275 + tmp278;
		    tmp385 = tmp278 - tmp275;
		    tmp280 = (K998795456 * tmp266) + (K049067674 * tmp279);
		    tmp376 = (K998795456 * tmp279) - (K049067674 * tmp266);
		    tmp386 = (K740951125 * tmp384) + (K671558954 * tmp385);
		    tmp396 = (K740951125 * tmp385) - (K671558954 * tmp384);
	       }
	  }
	  {
	       fftw_real tmp203;
	       fftw_real tmp358;
	       fftw_real tmp373;
	       fftw_real tmp374;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp203 = tmp159 + tmp202;
	       tmp358 = tmp280 + tmp357;
	       real_output[63 * real_ostride] = tmp203 - tmp358;
	       real_output[real_ostride] = tmp203 + tmp358;
	       tmp373 = tmp361 - tmp372;
	       tmp374 = tmp357 - tmp280;
	       imag_output[31 * imag_ostride] = tmp373 + tmp374;
	       imag_output[33 * imag_ostride] = tmp374 - tmp373;
	  }
	  {
	       fftw_real tmp375;
	       fftw_real tmp378;
	       fftw_real tmp379;
	       fftw_real tmp380;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp375 = tmp372 + tmp361;
	       tmp378 = tmp376 + tmp377;
	       imag_output[imag_ostride] = tmp375 + tmp378;
	       imag_output[63 * imag_ostride] = tmp378 - tmp375;
	       tmp379 = tmp159 - tmp202;
	       tmp380 = tmp377 - tmp376;
	       real_output[33 * real_ostride] = tmp379 - tmp380;
	       real_output[31 * real_ostride] = tmp379 + tmp380;
	  }
	  {
	       fftw_real tmp383;
	       fftw_real tmp390;
	       fftw_real tmp393;
	       fftw_real tmp394;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp383 = tmp381 + tmp382;
	       tmp390 = tmp386 + tmp389;
	       real_output[49 * real_ostride] = tmp383 - tmp390;
	       real_output[15 * real_ostride] = tmp383 + tmp390;
	       tmp393 = tmp391 - tmp392;
	       tmp394 = tmp389 - tmp386;
	       imag_output[17 * imag_ostride] = tmp393 + tmp394;
	       imag_output[47 * imag_ostride] = tmp394 - tmp393;
	  }
	  {
	       fftw_real tmp395;
	       fftw_real tmp398;
	       fftw_real tmp399;
	       fftw_real tmp400;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp395 = tmp392 + tmp391;
	       tmp398 = tmp396 + tmp397;
	       imag_output[15 * imag_ostride] = tmp395 + tmp398;
	       imag_output[49 * imag_ostride] = tmp398 - tmp395;
	       tmp399 = tmp381 - tmp382;
	       tmp400 = tmp397 - tmp396;
	       real_output[47 * real_ostride] = tmp399 - tmp400;
	       real_output[17 * real_ostride] = tmp399 + tmp400;
	  }
     }
     {
	  fftw_real tmp403;
	  fftw_real tmp441;
	  fftw_real tmp410;
	  fftw_real tmp451;
	  fftw_real tmp432;
	  fftw_real tmp452;
	  fftw_real tmp429;
	  fftw_real tmp442;
	  fftw_real tmp425;
	  fftw_real tmp437;
	  fftw_real tmp449;
	  fftw_real tmp457;
	  fftw_real tmp418;
	  fftw_real tmp436;
	  fftw_real tmp446;
	  fftw_real tmp456;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp401;
	       fftw_real tmp402;
	       fftw_real tmp427;
	       fftw_real tmp428;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp401 = tmp131 - tmp138;
	       tmp402 = tmp370 - tmp369;
	       tmp403 = tmp401 - tmp402;
	       tmp441 = tmp401 + tmp402;
	       {
		    fftw_real tmp406;
		    fftw_real tmp409;
		    fftw_real tmp430;
		    fftw_real tmp431;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp406 = (K773010453 * tmp404) + (K634393284 * tmp405);
		    tmp409 = (K773010453 * tmp407) - (K634393284 * tmp408);
		    tmp410 = tmp406 - tmp409;
		    tmp451 = tmp409 + tmp406;
		    tmp430 = tmp157 - tmp148;
		    tmp431 = tmp367 - tmp364;
		    tmp432 = tmp430 - tmp431;
		    tmp452 = tmp431 + tmp430;
	       }
	       tmp427 = (K773010453 * tmp405) - (K634393284 * tmp404);
	       tmp428 = (K634393284 * tmp407) + (K773010453 * tmp408);
	       tmp429 = tmp427 - tmp428;
	       tmp442 = tmp428 + tmp427;
	       {
		    fftw_real tmp421;
		    fftw_real tmp447;
		    fftw_real tmp424;
		    fftw_real tmp448;
		    fftw_real tmp420;
		    fftw_real tmp422;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp420 = tmp354 - tmp353;
		    tmp421 = tmp419 - tmp420;
		    tmp447 = tmp419 + tmp420;
		    tmp422 = tmp341 - tmp324;
		    tmp424 = tmp422 - tmp423;
		    tmp448 = tmp423 + tmp422;
		    tmp425 = (K903989293 * tmp421) - (K427555093 * tmp424);
		    tmp437 = (K903989293 * tmp424) + (K427555093 * tmp421);
		    tmp449 = (K941544065 * tmp447) - (K336889853 * tmp448);
		    tmp457 = (K941544065 * tmp448) + (K336889853 * tmp447);
	       }
	       {
		    fftw_real tmp414;
		    fftw_real tmp444;
		    fftw_real tmp417;
		    fftw_real tmp445;
		    fftw_real tmp412;
		    fftw_real tmp416;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp412 = tmp264 - tmp247;
		    tmp414 = tmp412 - tmp413;
		    tmp444 = tmp413 + tmp412;
		    tmp416 = tmp277 - tmp276;
		    tmp417 = tmp415 - tmp416;
		    tmp445 = tmp415 + tmp416;
		    tmp418 = (K427555093 * tmp414) + (K903989293 * tmp417);
		    tmp436 = (K903989293 * tmp414) - (K427555093 * tmp417);
		    tmp446 = (K336889853 * tmp444) + (K941544065 * tmp445);
		    tmp456 = (K941544065 * tmp444) - (K336889853 * tmp445);
	       }
	  }
	  {
	       fftw_real tmp411;
	       fftw_real tmp426;
	       fftw_real tmp433;
	       fftw_real tmp434;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp411 = tmp403 + tmp410;
	       tmp426 = tmp418 + tmp425;
	       real_output[55 * real_ostride] = tmp411 - tmp426;
	       real_output[9 * real_ostride] = tmp411 + tmp426;
	       tmp433 = tmp429 - tmp432;
	       tmp434 = tmp425 - tmp418;
	       imag_output[23 * imag_ostride] = tmp433 + tmp434;
	       imag_output[41 * imag_ostride] = tmp434 - tmp433;
	  }
	  {
	       fftw_real tmp435;
	       fftw_real tmp438;
	       fftw_real tmp439;
	       fftw_real tmp440;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp435 = tmp432 + tmp429;
	       tmp438 = tmp436 + tmp437;
	       imag_output[9 * imag_ostride] = tmp435 + tmp438;
	       imag_output[55 * imag_ostride] = tmp438 - tmp435;
	       tmp439 = tmp403 - tmp410;
	       tmp440 = tmp437 - tmp436;
	       real_output[41 * real_ostride] = tmp439 - tmp440;
	       real_output[23 * real_ostride] = tmp439 + tmp440;
	  }
	  {
	       fftw_real tmp443;
	       fftw_real tmp450;
	       fftw_real tmp453;
	       fftw_real tmp454;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp443 = tmp441 + tmp442;
	       tmp450 = tmp446 + tmp449;
	       real_output[57 * real_ostride] = tmp443 - tmp450;
	       real_output[7 * real_ostride] = tmp443 + tmp450;
	       tmp453 = tmp451 - tmp452;
	       tmp454 = tmp449 - tmp446;
	       imag_output[25 * imag_ostride] = tmp453 + tmp454;
	       imag_output[39 * imag_ostride] = tmp454 - tmp453;
	  }
	  {
	       fftw_real tmp455;
	       fftw_real tmp458;
	       fftw_real tmp459;
	       fftw_real tmp460;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp455 = tmp452 + tmp451;
	       tmp458 = tmp456 + tmp457;
	       imag_output[7 * imag_ostride] = tmp455 + tmp458;
	       imag_output[57 * imag_ostride] = tmp458 - tmp455;
	       tmp459 = tmp441 - tmp442;
	       tmp460 = tmp457 - tmp456;
	       real_output[39 * real_ostride] = tmp459 - tmp460;
	       real_output[25 * real_ostride] = tmp459 + tmp460;
	  }
     }
     {
	  fftw_real tmp629;
	  fftw_real tmp719;
	  fftw_real tmp710;
	  fftw_real tmp730;
	  fftw_real tmp636;
	  fftw_real tmp729;
	  fftw_real tmp707;
	  fftw_real tmp720;
	  fftw_real tmp670;
	  fftw_real tmp714;
	  fftw_real tmp724;
	  fftw_real tmp734;
	  fftw_real tmp703;
	  fftw_real tmp715;
	  fftw_real tmp727;
	  fftw_real tmp735;
	  fftw_real tmp628;
	  fftw_real tmp709;
	  ASSERT_ALIGNED_DOUBLE;
	  tmp628 = K707106781 * (tmp626 + tmp627);
	  tmp629 = tmp625 + tmp628;
	  tmp719 = tmp625 - tmp628;
	  tmp709 = K707106781 * (tmp627 - tmp626);
	  tmp710 = tmp708 + tmp709;
	  tmp730 = tmp709 - tmp708;
	  {
	       fftw_real tmp632;
	       fftw_real tmp635;
	       fftw_real tmp705;
	       fftw_real tmp706;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp632 = (K923879532 * tmp630) + (K382683432 * tmp631);
	       tmp635 = (K923879532 * tmp633) - (K382683432 * tmp634);
	       tmp636 = tmp632 + tmp635;
	       tmp729 = tmp635 - tmp632;
	       tmp705 = (K923879532 * tmp631) - (K382683432 * tmp630);
	       tmp706 = (K382683432 * tmp633) + (K923879532 * tmp634);
	       tmp707 = tmp705 + tmp706;
	       tmp720 = tmp706 - tmp705;
	  }
	  {
	       fftw_real tmp660;
	       fftw_real tmp669;
	       fftw_real tmp722;
	       fftw_real tmp723;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp660 = tmp644 + tmp659;
	       tmp669 = tmp667 + tmp668;
	       tmp670 = (K980785280 * tmp660) + (K195090322 * tmp669);
	       tmp714 = (K980785280 * tmp669) - (K195090322 * tmp660);
	       tmp722 = tmp644 - tmp659;
	       tmp723 = tmp668 - tmp667;
	       tmp724 = (K831469612 * tmp722) + (K555570233 * tmp723);
	       tmp734 = (K831469612 * tmp723) - (K555570233 * tmp722);
	  }
	  {
	       fftw_real tmp693;
	       fftw_real tmp702;
	       fftw_real tmp725;
	       fftw_real tmp726;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp693 = tmp677 + tmp692;
	       tmp702 = tmp700 + tmp701;
	       tmp703 = (K980785280 * tmp693) - (K195090322 * tmp702);
	       tmp715 = (K195090322 * tmp693) + (K980785280 * tmp702);
	       tmp725 = tmp677 - tmp692;
	       tmp726 = tmp701 - tmp700;
	       tmp727 = (K831469612 * tmp725) - (K555570233 * tmp726);
	       tmp735 = (K555570233 * tmp725) + (K831469612 * tmp726);
	  }
	  {
	       fftw_real tmp637;
	       fftw_real tmp704;
	       fftw_real tmp711;
	       fftw_real tmp712;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp637 = tmp629 + tmp636;
	       tmp704 = tmp670 + tmp703;
	       real_output[60 * real_ostride] = tmp637 - tmp704;
	       real_output[4 * real_ostride] = tmp637 + tmp704;
	       tmp711 = tmp707 - tmp710;
	       tmp712 = tmp703 - tmp670;
	       imag_output[28 * imag_ostride] = tmp711 + tmp712;
	       imag_output[36 * imag_ostride] = tmp712 - tmp711;
	  }
	  {
	       fftw_real tmp713;
	       fftw_real tmp716;
	       fftw_real tmp717;
	       fftw_real tmp718;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp713 = tmp710 + tmp707;
	       tmp716 = tmp714 + tmp715;
	       imag_output[4 * imag_ostride] = tmp713 + tmp716;
	       imag_output[60 * imag_ostride] = tmp716 - tmp713;
	       tmp717 = tmp629 - tmp636;
	       tmp718 = tmp715 - tmp714;
	       real_output[36 * real_ostride] = tmp717 - tmp718;
	       real_output[28 * real_ostride] = tmp717 + tmp718;
	  }
	  {
	       fftw_real tmp721;
	       fftw_real tmp728;
	       fftw_real tmp731;
	       fftw_real tmp732;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp721 = tmp719 + tmp720;
	       tmp728 = tmp724 + tmp727;
	       real_output[52 * real_ostride] = tmp721 - tmp728;
	       real_output[12 * real_ostride] = tmp721 + tmp728;
	       tmp731 = tmp729 - tmp730;
	       tmp732 = tmp727 - tmp724;
	       imag_output[20 * imag_ostride] = tmp731 + tmp732;
	       imag_output[44 * imag_ostride] = tmp732 - tmp731;
	  }
	  {
	       fftw_real tmp733;
	       fftw_real tmp736;
	       fftw_real tmp737;
	       fftw_real tmp738;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp733 = tmp730 + tmp729;
	       tmp736 = tmp734 + tmp735;
	       imag_output[12 * imag_ostride] = tmp733 + tmp736;
	       imag_output[52 * imag_ostride] = tmp736 - tmp733;
	       tmp737 = tmp719 - tmp720;
	       tmp738 = tmp735 - tmp734;
	       real_output[44 * real_ostride] = tmp737 - tmp738;
	       real_output[20 * real_ostride] = tmp737 + tmp738;
	  }
     }
     {
	  fftw_real tmp795;
	  fftw_real tmp877;
	  fftw_real tmp882;
	  fftw_real tmp892;
	  fftw_real tmp885;
	  fftw_real tmp893;
	  fftw_real tmp857;
	  fftw_real tmp873;
	  fftw_real tmp836;
	  fftw_real tmp872;
	  fftw_real tmp861;
	  fftw_real tmp878;
	  fftw_real tmp814;
	  fftw_real tmp887;
	  fftw_real tmp868;
	  fftw_real tmp888;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp787;
	       fftw_real tmp794;
	       fftw_real tmp880;
	       fftw_real tmp881;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp787 = tmp783 + tmp786;
	       tmp794 = tmp790 + tmp793;
	       tmp795 = tmp787 + tmp794;
	       tmp877 = tmp787 - tmp794;
	       tmp880 = tmp820 - tmp827;
	       tmp881 = tmp834 - tmp831;
	       tmp882 = (K773010453 * tmp880) + (K634393284 * tmp881);
	       tmp892 = (K773010453 * tmp881) - (K634393284 * tmp880);
	  }
	  {
	       fftw_real tmp883;
	       fftw_real tmp884;
	       fftw_real tmp849;
	       fftw_real tmp856;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp883 = tmp841 - tmp848;
	       tmp884 = tmp855 - tmp852;
	       tmp885 = (K773010453 * tmp883) - (K634393284 * tmp884);
	       tmp893 = (K634393284 * tmp883) + (K773010453 * tmp884);
	       tmp849 = tmp841 + tmp848;
	       tmp856 = tmp852 + tmp855;
	       tmp857 = (K995184726 * tmp849) - (K098017140 * tmp856);
	       tmp873 = (K098017140 * tmp849) + (K995184726 * tmp856);
	  }
	  {
	       fftw_real tmp828;
	       fftw_real tmp835;
	       fftw_real tmp859;
	       fftw_real tmp860;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp828 = tmp820 + tmp827;
	       tmp835 = tmp831 + tmp834;
	       tmp836 = (K995184726 * tmp828) + (K098017140 * tmp835);
	       tmp872 = (K995184726 * tmp835) - (K098017140 * tmp828);
	       tmp859 = (K980785280 * tmp803) - (K195090322 * tmp800);
	       tmp860 = (K195090322 * tmp809) + (K980785280 * tmp812);
	       tmp861 = tmp859 + tmp860;
	       tmp878 = tmp860 - tmp859;
	  }
	  {
	       fftw_real tmp804;
	       fftw_real tmp813;
	       fftw_real tmp864;
	       fftw_real tmp867;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp804 = (K980785280 * tmp800) + (K195090322 * tmp803);
	       tmp813 = (K980785280 * tmp809) - (K195090322 * tmp812);
	       tmp814 = tmp804 + tmp813;
	       tmp887 = tmp813 - tmp804;
	       tmp864 = tmp862 + tmp863;
	       tmp867 = tmp865 + tmp866;
	       tmp868 = tmp864 + tmp867;
	       tmp888 = tmp867 - tmp864;
	  }
	  {
	       fftw_real tmp815;
	       fftw_real tmp858;
	       fftw_real tmp869;
	       fftw_real tmp870;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp815 = tmp795 + tmp814;
	       tmp858 = tmp836 + tmp857;
	       real_output[62 * real_ostride] = tmp815 - tmp858;
	       real_output[2 * real_ostride] = tmp815 + tmp858;
	       tmp869 = tmp861 - tmp868;
	       tmp870 = tmp857 - tmp836;
	       imag_output[30 * imag_ostride] = tmp869 + tmp870;
	       imag_output[34 * imag_ostride] = tmp870 - tmp869;
	  }
	  {
	       fftw_real tmp871;
	       fftw_real tmp874;
	       fftw_real tmp875;
	       fftw_real tmp876;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp871 = tmp868 + tmp861;
	       tmp874 = tmp872 + tmp873;
	       imag_output[2 * imag_ostride] = tmp871 + tmp874;
	       imag_output[62 * imag_ostride] = tmp874 - tmp871;
	       tmp875 = tmp795 - tmp814;
	       tmp876 = tmp873 - tmp872;
	       real_output[34 * real_ostride] = tmp875 - tmp876;
	       real_output[30 * real_ostride] = tmp875 + tmp876;
	  }
	  {
	       fftw_real tmp879;
	       fftw_real tmp886;
	       fftw_real tmp889;
	       fftw_real tmp890;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp879 = tmp877 + tmp878;
	       tmp886 = tmp882 + tmp885;
	       real_output[50 * real_ostride] = tmp879 - tmp886;
	       real_output[14 * real_ostride] = tmp879 + tmp886;
	       tmp889 = tmp887 - tmp888;
	       tmp890 = tmp885 - tmp882;
	       imag_output[18 * imag_ostride] = tmp889 + tmp890;
	       imag_output[46 * imag_ostride] = tmp890 - tmp889;
	  }
	  {
	       fftw_real tmp891;
	       fftw_real tmp894;
	       fftw_real tmp895;
	       fftw_real tmp896;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp891 = tmp888 + tmp887;
	       tmp894 = tmp892 + tmp893;
	       imag_output[14 * imag_ostride] = tmp891 + tmp894;
	       imag_output[50 * imag_ostride] = tmp894 - tmp891;
	       tmp895 = tmp877 - tmp878;
	       tmp896 = tmp893 - tmp892;
	       real_output[46 * real_ostride] = tmp895 - tmp896;
	       real_output[18 * real_ostride] = tmp895 + tmp896;
	  }
     }
     {
	  fftw_real tmp899;
	  fftw_real tmp937;
	  fftw_real tmp942;
	  fftw_real tmp952;
	  fftw_real tmp945;
	  fftw_real tmp953;
	  fftw_real tmp921;
	  fftw_real tmp933;
	  fftw_real tmp914;
	  fftw_real tmp932;
	  fftw_real tmp925;
	  fftw_real tmp938;
	  fftw_real tmp906;
	  fftw_real tmp947;
	  fftw_real tmp928;
	  fftw_real tmp948;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp897;
	       fftw_real tmp898;
	       fftw_real tmp940;
	       fftw_real tmp941;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp897 = tmp783 - tmp786;
	       tmp898 = tmp866 - tmp865;
	       tmp899 = tmp897 - tmp898;
	       tmp937 = tmp897 + tmp898;
	       tmp940 = tmp909 + tmp908;
	       tmp941 = tmp911 + tmp912;
	       tmp942 = (K290284677 * tmp940) + (K956940335 * tmp941);
	       tmp952 = (K956940335 * tmp940) - (K290284677 * tmp941);
	  }
	  {
	       fftw_real tmp943;
	       fftw_real tmp944;
	       fftw_real tmp917;
	       fftw_real tmp920;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp943 = tmp915 + tmp916;
	       tmp944 = tmp919 + tmp918;
	       tmp945 = (K956940335 * tmp943) - (K290284677 * tmp944);
	       tmp953 = (K956940335 * tmp944) + (K290284677 * tmp943);
	       tmp917 = tmp915 - tmp916;
	       tmp920 = tmp918 - tmp919;
	       tmp921 = (K881921264 * tmp917) - (K471396736 * tmp920);
	       tmp933 = (K881921264 * tmp920) + (K471396736 * tmp917);
	  }
	  {
	       fftw_real tmp910;
	       fftw_real tmp913;
	       fftw_real tmp923;
	       fftw_real tmp924;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp910 = tmp908 - tmp909;
	       tmp913 = tmp911 - tmp912;
	       tmp914 = (K471396736 * tmp910) + (K881921264 * tmp913);
	       tmp932 = (K881921264 * tmp910) - (K471396736 * tmp913);
	       tmp923 = (K831469612 * tmp900) - (K555570233 * tmp901);
	       tmp924 = (K831469612 * tmp904) + (K555570233 * tmp903);
	       tmp925 = tmp923 - tmp924;
	       tmp938 = tmp924 + tmp923;
	  }
	  {
	       fftw_real tmp902;
	       fftw_real tmp905;
	       fftw_real tmp926;
	       fftw_real tmp927;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp902 = (K555570233 * tmp900) + (K831469612 * tmp901);
	       tmp905 = (K831469612 * tmp903) - (K555570233 * tmp904);
	       tmp906 = tmp902 - tmp905;
	       tmp947 = tmp905 + tmp902;
	       tmp926 = tmp793 - tmp790;
	       tmp927 = tmp863 - tmp862;
	       tmp928 = tmp926 - tmp927;
	       tmp948 = tmp927 + tmp926;
	  }
	  {
	       fftw_real tmp907;
	       fftw_real tmp922;
	       fftw_real tmp929;
	       fftw_real tmp930;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp907 = tmp899 + tmp906;
	       tmp922 = tmp914 + tmp921;
	       real_output[54 * real_ostride] = tmp907 - tmp922;
	       real_output[10 * real_ostride] = tmp907 + tmp922;
	       tmp929 = tmp925 - tmp928;
	       tmp930 = tmp921 - tmp914;
	       imag_output[22 * imag_ostride] = tmp929 + tmp930;
	       imag_output[42 * imag_ostride] = tmp930 - tmp929;
	  }
	  {
	       fftw_real tmp931;
	       fftw_real tmp934;
	       fftw_real tmp935;
	       fftw_real tmp936;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp931 = tmp928 + tmp925;
	       tmp934 = tmp932 + tmp933;
	       imag_output[10 * imag_ostride] = tmp931 + tmp934;
	       imag_output[54 * imag_ostride] = tmp934 - tmp931;
	       tmp935 = tmp899 - tmp906;
	       tmp936 = tmp933 - tmp932;
	       real_output[42 * real_ostride] = tmp935 - tmp936;
	       real_output[22 * real_ostride] = tmp935 + tmp936;
	  }
	  {
	       fftw_real tmp939;
	       fftw_real tmp946;
	       fftw_real tmp949;
	       fftw_real tmp950;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp939 = tmp937 + tmp938;
	       tmp946 = tmp942 + tmp945;
	       real_output[58 * real_ostride] = tmp939 - tmp946;
	       real_output[6 * real_ostride] = tmp939 + tmp946;
	       tmp949 = tmp947 - tmp948;
	       tmp950 = tmp945 - tmp942;
	       imag_output[26 * imag_ostride] = tmp949 + tmp950;
	       imag_output[38 * imag_ostride] = tmp950 - tmp949;
	  }
	  {
	       fftw_real tmp951;
	       fftw_real tmp954;
	       fftw_real tmp955;
	       fftw_real tmp956;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp951 = tmp948 + tmp947;
	       tmp954 = tmp952 + tmp953;
	       imag_output[6 * imag_ostride] = tmp951 + tmp954;
	       imag_output[58 * imag_ostride] = tmp954 - tmp951;
	       tmp955 = tmp937 - tmp938;
	       tmp956 = tmp953 - tmp952;
	       real_output[38 * real_ostride] = tmp955 - tmp956;
	       real_output[26 * real_ostride] = tmp955 + tmp956;
	  }
     }
     {
	  fftw_real tmp471;
	  fftw_real tmp545;
	  fftw_real tmp486;
	  fftw_real tmp555;
	  fftw_real tmp536;
	  fftw_real tmp556;
	  fftw_real tmp529;
	  fftw_real tmp546;
	  fftw_real tmp525;
	  fftw_real tmp541;
	  fftw_real tmp553;
	  fftw_real tmp561;
	  fftw_real tmp506;
	  fftw_real tmp540;
	  fftw_real tmp550;
	  fftw_real tmp560;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp463;
	       fftw_real tmp470;
	       fftw_real tmp527;
	       fftw_real tmp528;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp463 = tmp461 + tmp462;
	       tmp470 = tmp466 + tmp469;
	       tmp471 = tmp463 + tmp470;
	       tmp545 = tmp463 - tmp470;
	       {
		    fftw_real tmp478;
		    fftw_real tmp485;
		    fftw_real tmp532;
		    fftw_real tmp535;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp478 = (K956940335 * tmp474) + (K290284677 * tmp477);
		    tmp485 = (K956940335 * tmp481) - (K290284677 * tmp484);
		    tmp486 = tmp478 + tmp485;
		    tmp555 = tmp485 - tmp478;
		    tmp532 = tmp530 + tmp531;
		    tmp535 = tmp533 + tmp534;
		    tmp536 = tmp532 + tmp535;
		    tmp556 = tmp535 - tmp532;
	       }
	       tmp527 = (K956940335 * tmp477) - (K290284677 * tmp474);
	       tmp528 = (K290284677 * tmp481) + (K956940335 * tmp484);
	       tmp529 = tmp527 + tmp528;
	       tmp546 = tmp528 - tmp527;
	       {
		    fftw_real tmp517;
		    fftw_real tmp551;
		    fftw_real tmp524;
		    fftw_real tmp552;
		    fftw_real tmp516;
		    fftw_real tmp523;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp516 = tmp512 + tmp515;
		    tmp517 = tmp509 + tmp516;
		    tmp551 = tmp509 - tmp516;
		    tmp523 = tmp521 + tmp522;
		    tmp524 = tmp520 + tmp523;
		    tmp552 = tmp523 - tmp520;
		    tmp525 = (K989176509 * tmp517) - (K146730474 * tmp524);
		    tmp541 = (K146730474 * tmp517) + (K989176509 * tmp524);
		    tmp553 = (K803207531 * tmp551) - (K595699304 * tmp552);
		    tmp561 = (K595699304 * tmp551) + (K803207531 * tmp552);
	       }
	       {
		    fftw_real tmp498;
		    fftw_real tmp548;
		    fftw_real tmp505;
		    fftw_real tmp549;
		    fftw_real tmp497;
		    fftw_real tmp504;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp497 = tmp493 + tmp496;
		    tmp498 = tmp490 + tmp497;
		    tmp548 = tmp490 - tmp497;
		    tmp504 = tmp502 + tmp503;
		    tmp505 = tmp501 + tmp504;
		    tmp549 = tmp504 - tmp501;
		    tmp506 = (K989176509 * tmp498) + (K146730474 * tmp505);
		    tmp540 = (K989176509 * tmp505) - (K146730474 * tmp498);
		    tmp550 = (K803207531 * tmp548) + (K595699304 * tmp549);
		    tmp560 = (K803207531 * tmp549) - (K595699304 * tmp548);
	       }
	  }
	  {
	       fftw_real tmp487;
	       fftw_real tmp526;
	       fftw_real tmp537;
	       fftw_real tmp538;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp487 = tmp471 + tmp486;
	       tmp526 = tmp506 + tmp525;
	       real_output[61 * real_ostride] = tmp487 - tmp526;
	       real_output[3 * real_ostride] = tmp487 + tmp526;
	       tmp537 = tmp529 - tmp536;
	       tmp538 = tmp525 - tmp506;
	       imag_output[29 * imag_ostride] = tmp537 + tmp538;
	       imag_output[35 * imag_ostride] = tmp538 - tmp537;
	  }
	  {
	       fftw_real tmp539;
	       fftw_real tmp542;
	       fftw_real tmp543;
	       fftw_real tmp544;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp539 = tmp536 + tmp529;
	       tmp542 = tmp540 + tmp541;
	       imag_output[3 * imag_ostride] = tmp539 + tmp542;
	       imag_output[61 * imag_ostride] = tmp542 - tmp539;
	       tmp543 = tmp471 - tmp486;
	       tmp544 = tmp541 - tmp540;
	       real_output[35 * real_ostride] = tmp543 - tmp544;
	       real_output[29 * real_ostride] = tmp543 + tmp544;
	  }
	  {
	       fftw_real tmp547;
	       fftw_real tmp554;
	       fftw_real tmp557;
	       fftw_real tmp558;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp547 = tmp545 + tmp546;
	       tmp554 = tmp550 + tmp553;
	       real_output[51 * real_ostride] = tmp547 - tmp554;
	       real_output[13 * real_ostride] = tmp547 + tmp554;
	       tmp557 = tmp555 - tmp556;
	       tmp558 = tmp553 - tmp550;
	       imag_output[19 * imag_ostride] = tmp557 + tmp558;
	       imag_output[45 * imag_ostride] = tmp558 - tmp557;
	  }
	  {
	       fftw_real tmp559;
	       fftw_real tmp562;
	       fftw_real tmp563;
	       fftw_real tmp564;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp559 = tmp556 + tmp555;
	       tmp562 = tmp560 + tmp561;
	       imag_output[13 * imag_ostride] = tmp559 + tmp562;
	       imag_output[51 * imag_ostride] = tmp562 - tmp559;
	       tmp563 = tmp545 - tmp546;
	       tmp564 = tmp561 - tmp560;
	       real_output[45 * real_ostride] = tmp563 - tmp564;
	       real_output[19 * real_ostride] = tmp563 + tmp564;
	  }
     }
     {
	  fftw_real tmp567;
	  fftw_real tmp605;
	  fftw_real tmp574;
	  fftw_real tmp615;
	  fftw_real tmp596;
	  fftw_real tmp616;
	  fftw_real tmp593;
	  fftw_real tmp606;
	  fftw_real tmp589;
	  fftw_real tmp601;
	  fftw_real tmp613;
	  fftw_real tmp621;
	  fftw_real tmp582;
	  fftw_real tmp600;
	  fftw_real tmp610;
	  fftw_real tmp620;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp565;
	       fftw_real tmp566;
	       fftw_real tmp591;
	       fftw_real tmp592;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp565 = tmp461 - tmp462;
	       tmp566 = tmp534 - tmp533;
	       tmp567 = tmp565 - tmp566;
	       tmp605 = tmp565 + tmp566;
	       {
		    fftw_real tmp570;
		    fftw_real tmp573;
		    fftw_real tmp594;
		    fftw_real tmp595;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp570 = (K471396736 * tmp568) + (K881921264 * tmp569);
		    tmp573 = (K881921264 * tmp571) - (K471396736 * tmp572);
		    tmp574 = tmp570 - tmp573;
		    tmp615 = tmp573 + tmp570;
		    tmp594 = tmp469 - tmp466;
		    tmp595 = tmp531 - tmp530;
		    tmp596 = tmp594 - tmp595;
		    tmp616 = tmp595 + tmp594;
	       }
	       tmp591 = (K881921264 * tmp568) - (K471396736 * tmp569);
	       tmp592 = (K881921264 * tmp572) + (K471396736 * tmp571);
	       tmp593 = tmp591 - tmp592;
	       tmp606 = tmp592 + tmp591;
	       {
		    fftw_real tmp585;
		    fftw_real tmp611;
		    fftw_real tmp588;
		    fftw_real tmp612;
		    fftw_real tmp584;
		    fftw_real tmp586;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp584 = tmp522 - tmp521;
		    tmp585 = tmp583 - tmp584;
		    tmp611 = tmp583 + tmp584;
		    tmp586 = tmp515 - tmp512;
		    tmp588 = tmp586 - tmp587;
		    tmp612 = tmp587 + tmp586;
		    tmp589 = (K857728610 * tmp585) - (K514102744 * tmp588);
		    tmp601 = (K857728610 * tmp588) + (K514102744 * tmp585);
		    tmp613 = (K970031253 * tmp611) - (K242980179 * tmp612);
		    tmp621 = (K970031253 * tmp612) + (K242980179 * tmp611);
	       }
	       {
		    fftw_real tmp578;
		    fftw_real tmp608;
		    fftw_real tmp581;
		    fftw_real tmp609;
		    fftw_real tmp576;
		    fftw_real tmp580;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp576 = tmp496 - tmp493;
		    tmp578 = tmp576 - tmp577;
		    tmp608 = tmp577 + tmp576;
		    tmp580 = tmp503 - tmp502;
		    tmp581 = tmp579 - tmp580;
		    tmp609 = tmp579 + tmp580;
		    tmp582 = (K514102744 * tmp578) + (K857728610 * tmp581);
		    tmp600 = (K857728610 * tmp578) - (K514102744 * tmp581);
		    tmp610 = (K242980179 * tmp608) + (K970031253 * tmp609);
		    tmp620 = (K970031253 * tmp608) - (K242980179 * tmp609);
	       }
	  }
	  {
	       fftw_real tmp575;
	       fftw_real tmp590;
	       fftw_real tmp597;
	       fftw_real tmp598;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp575 = tmp567 + tmp574;
	       tmp590 = tmp582 + tmp589;
	       real_output[53 * real_ostride] = tmp575 - tmp590;
	       real_output[11 * real_ostride] = tmp575 + tmp590;
	       tmp597 = tmp593 - tmp596;
	       tmp598 = tmp589 - tmp582;
	       imag_output[21 * imag_ostride] = tmp597 + tmp598;
	       imag_output[43 * imag_ostride] = tmp598 - tmp597;
	  }
	  {
	       fftw_real tmp599;
	       fftw_real tmp602;
	       fftw_real tmp603;
	       fftw_real tmp604;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp599 = tmp596 + tmp593;
	       tmp602 = tmp600 + tmp601;
	       imag_output[11 * imag_ostride] = tmp599 + tmp602;
	       imag_output[53 * imag_ostride] = tmp602 - tmp599;
	       tmp603 = tmp567 - tmp574;
	       tmp604 = tmp601 - tmp600;
	       real_output[43 * real_ostride] = tmp603 - tmp604;
	       real_output[21 * real_ostride] = tmp603 + tmp604;
	  }
	  {
	       fftw_real tmp607;
	       fftw_real tmp614;
	       fftw_real tmp617;
	       fftw_real tmp618;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp607 = tmp605 + tmp606;
	       tmp614 = tmp610 + tmp613;
	       real_output[59 * real_ostride] = tmp607 - tmp614;
	       real_output[5 * real_ostride] = tmp607 + tmp614;
	       tmp617 = tmp615 - tmp616;
	       tmp618 = tmp613 - tmp610;
	       imag_output[27 * imag_ostride] = tmp617 + tmp618;
	       imag_output[37 * imag_ostride] = tmp618 - tmp617;
	  }
	  {
	       fftw_real tmp619;
	       fftw_real tmp622;
	       fftw_real tmp623;
	       fftw_real tmp624;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp619 = tmp616 + tmp615;
	       tmp622 = tmp620 + tmp621;
	       imag_output[5 * imag_ostride] = tmp619 + tmp622;
	       imag_output[59 * imag_ostride] = tmp622 - tmp619;
	       tmp623 = tmp605 - tmp606;
	       tmp624 = tmp621 - tmp620;
	       real_output[37 * real_ostride] = tmp623 - tmp624;
	       real_output[27 * real_ostride] = tmp623 + tmp624;
	  }
     }
}

fftw_codelet_desc fftw_real2hc_128_desc =
{
     "fftw_real2hc_128",
     (void (*)()) fftw_real2hc_128,
     128,
     FFTW_FORWARD,
     FFTW_REAL2HC,
     2818,
     0,
     (const int *) 0,
};
