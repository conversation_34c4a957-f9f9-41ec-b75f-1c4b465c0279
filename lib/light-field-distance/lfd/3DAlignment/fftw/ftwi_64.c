/*
 * Copyright (c) 1997-1999 Massachusetts Institute of Technology
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 *
 */

/* This file was automatically generated --- DO NOT EDIT */
/* Generated on Sun Nov  7 20:45:03 EST 1999 */

#include "fftw-int.h"
#include "fftw.h"

/* Generated by: ./genfft -magic-alignment-check -magic-twiddle-load-all -magic-variables 4 -magic-loopi -twiddleinv 64 */

/*
 * This function contains 1038 FP additions, 500 FP multiplications,
 * (or, 808 additions, 270 multiplications, 230 fused multiply/add),
 * 162 stack variables, and 256 memory accesses
 */
static const fftw_real K098017140 = FFTW_KONST(+0.098017140329560601994195563888641845861136673);
static const fftw_real K995184726 = FFTW_KONST(+0.995184726672196886244836953109479921575474869);
static const fftw_real K773010453 = FFTW_KONST(+0.773010453362736960810906609758469800971041293);
static const fftw_real K634393284 = FFTW_KONST(+0.634393284163645498215171613225493370675687095);
static const fftw_real K195090322 = FFTW_KONST(+0.195090322016128267848284868477022240927691618);
static const fftw_real K980785280 = FFTW_KONST(+0.980785280403230449126182236134239036973933731);
static const fftw_real K471396736 = FFTW_KONST(+0.471396736825997648556387625905254377657460319);
static const fftw_real K881921264 = FFTW_KONST(+0.881921264348355029712756863660388349508442621);
static const fftw_real K956940335 = FFTW_KONST(+0.956940335732208864935797886980269969482849206);
static const fftw_real K290284677 = FFTW_KONST(+0.290284677254462367636192375817395274691476278);
static const fftw_real K831469612 = FFTW_KONST(+0.831469612302545237078788377617905756738560812);
static const fftw_real K555570233 = FFTW_KONST(+0.555570233019602224742830813948532874374937191);
static const fftw_real K707106781 = FFTW_KONST(+0.707106781186547524400844362104849039284835938);
static const fftw_real K382683432 = FFTW_KONST(+0.382683432365089771728459984030398866761344562);
static const fftw_real K923879532 = FFTW_KONST(+0.923879532511286756128183189396788286822416626);

/*
 * Generator Id's : 
 * $Id: exprdag.ml,v 1.41 1999/05/26 15:44:14 fftw Exp $
 * $Id: fft.ml,v 1.43 1999/05/17 19:44:18 fftw Exp $
 * $Id: to_c.ml,v 1.25 1999/10/26 21:41:32 stevenj Exp $
 */

void fftwi_twiddle_64(fftw_complex *A, const fftw_complex *W, int iostride, int m, int dist)
{
     int i;
     fftw_complex *inout;
     inout = A;
     for (i = m; i > 0; i = i - 1, inout = inout + dist, W = W + 63) {
	  fftw_real tmp19;
	  fftw_real tmp791;
	  fftw_real tmp1109;
	  fftw_real tmp1139;
	  fftw_real tmp1047;
	  fftw_real tmp1077;
	  fftw_real tmp383;
	  fftw_real tmp655;
	  fftw_real tmp66;
	  fftw_real tmp800;
	  fftw_real tmp909;
	  fftw_real tmp993;
	  fftw_real tmp417;
	  fftw_real tmp608;
	  fftw_real tmp665;
	  fftw_real tmp744;
	  fftw_real tmp42;
	  fftw_real tmp1076;
	  fftw_real tmp794;
	  fftw_real tmp1042;
	  fftw_real tmp394;
	  fftw_real tmp1138;
	  fftw_real tmp658;
	  fftw_real tmp1106;
	  fftw_real tmp329;
	  fftw_real tmp1007;
	  fftw_real tmp863;
	  fftw_real tmp923;
	  fftw_real tmp976;
	  fftw_real tmp1004;
	  fftw_real tmp880;
	  fftw_real tmp920;
	  fftw_real tmp535;
	  fftw_real tmp703;
	  fftw_real tmp576;
	  fftw_real tmp714;
	  fftw_real tmp579;
	  fftw_real tmp704;
	  fftw_real tmp546;
	  fftw_real tmp715;
	  fftw_real tmp376;
	  fftw_real tmp1005;
	  fftw_real tmp868;
	  fftw_real tmp881;
	  fftw_real tmp979;
	  fftw_real tmp1008;
	  fftw_real tmp873;
	  fftw_real tmp882;
	  fftw_real tmp558;
	  fftw_real tmp582;
	  fftw_real tmp708;
	  fftw_real tmp718;
	  fftw_real tmp569;
	  fftw_real tmp581;
	  fftw_real tmp711;
	  fftw_real tmp717;
	  fftw_real tmp89;
	  fftw_real tmp805;
	  fftw_real tmp908;
	  fftw_real tmp992;
	  fftw_real tmp406;
	  fftw_real tmp609;
	  fftw_real tmp662;
	  fftw_real tmp745;
	  fftw_real tmp161;
	  fftw_real tmp184;
	  fftw_real tmp997;
	  fftw_real tmp812;
	  fftw_real tmp912;
	  fftw_real tmp956;
	  fftw_real tmp957;
	  fftw_real tmp996;
	  fftw_real tmp817;
	  fftw_real tmp913;
	  fftw_real tmp424;
	  fftw_real tmp668;
	  fftw_real tmp441;
	  fftw_real tmp671;
	  fftw_real tmp444;
	  fftw_real tmp669;
	  fftw_real tmp435;
	  fftw_real tmp672;
	  fftw_real tmp114;
	  fftw_real tmp137;
	  fftw_real tmp999;
	  fftw_real tmp823;
	  fftw_real tmp915;
	  fftw_real tmp959;
	  fftw_real tmp960;
	  fftw_real tmp1000;
	  fftw_real tmp828;
	  fftw_real tmp916;
	  fftw_real tmp451;
	  fftw_real tmp678;
	  fftw_real tmp468;
	  fftw_real tmp675;
	  fftw_real tmp471;
	  fftw_real tmp679;
	  fftw_real tmp462;
	  fftw_real tmp676;
	  fftw_real tmp234;
	  fftw_real tmp1014;
	  fftw_real tmp836;
	  fftw_real tmp930;
	  fftw_real tmp967;
	  fftw_real tmp1011;
	  fftw_real tmp853;
	  fftw_real tmp927;
	  fftw_real tmp480;
	  fftw_real tmp684;
	  fftw_real tmp521;
	  fftw_real tmp695;
	  fftw_real tmp524;
	  fftw_real tmp685;
	  fftw_real tmp491;
	  fftw_real tmp696;
	  fftw_real tmp281;
	  fftw_real tmp1012;
	  fftw_real tmp841;
	  fftw_real tmp854;
	  fftw_real tmp970;
	  fftw_real tmp1015;
	  fftw_real tmp846;
	  fftw_real tmp855;
	  fftw_real tmp503;
	  fftw_real tmp527;
	  fftw_real tmp689;
	  fftw_real tmp699;
	  fftw_real tmp514;
	  fftw_real tmp526;
	  fftw_real tmp692;
	  fftw_real tmp698;
	  ASSERT_ALIGNED_DOUBLE;
	  {
	       fftw_real tmp1;
	       fftw_real tmp1045;
	       fftw_real tmp6;
	       fftw_real tmp1044;
	       fftw_real tmp12;
	       fftw_real tmp380;
	       fftw_real tmp17;
	       fftw_real tmp381;
	       ASSERT_ALIGNED_DOUBLE;
	       tmp1 = c_re(inout[0]);
	       tmp1045 = c_im(inout[0]);
	       {
		    fftw_real tmp3;
		    fftw_real tmp5;
		    fftw_real tmp2;
		    fftw_real tmp4;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp3 = c_re(inout[32 * iostride]);
		    tmp5 = c_im(inout[32 * iostride]);
		    tmp2 = c_re(W[31]);
		    tmp4 = c_im(W[31]);
		    tmp6 = (tmp2 * tmp3) + (tmp4 * tmp5);
		    tmp1044 = (tmp2 * tmp5) - (tmp4 * tmp3);
	       }
	       {
		    fftw_real tmp9;
		    fftw_real tmp11;
		    fftw_real tmp8;
		    fftw_real tmp10;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp9 = c_re(inout[16 * iostride]);
		    tmp11 = c_im(inout[16 * iostride]);
		    tmp8 = c_re(W[15]);
		    tmp10 = c_im(W[15]);
		    tmp12 = (tmp8 * tmp9) + (tmp10 * tmp11);
		    tmp380 = (tmp8 * tmp11) - (tmp10 * tmp9);
	       }
	       {
		    fftw_real tmp14;
		    fftw_real tmp16;
		    fftw_real tmp13;
		    fftw_real tmp15;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp14 = c_re(inout[48 * iostride]);
		    tmp16 = c_im(inout[48 * iostride]);
		    tmp13 = c_re(W[47]);
		    tmp15 = c_im(W[47]);
		    tmp17 = (tmp13 * tmp14) + (tmp15 * tmp16);
		    tmp381 = (tmp13 * tmp16) - (tmp15 * tmp14);
	       }
	       {
		    fftw_real tmp7;
		    fftw_real tmp18;
		    fftw_real tmp1107;
		    fftw_real tmp1108;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp7 = tmp1 + tmp6;
		    tmp18 = tmp12 + tmp17;
		    tmp19 = tmp7 + tmp18;
		    tmp791 = tmp7 - tmp18;
		    tmp1107 = tmp12 - tmp17;
		    tmp1108 = tmp1045 - tmp1044;
		    tmp1109 = tmp1107 + tmp1108;
		    tmp1139 = tmp1108 - tmp1107;
	       }
	       {
		    fftw_real tmp1043;
		    fftw_real tmp1046;
		    fftw_real tmp379;
		    fftw_real tmp382;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp1043 = tmp380 + tmp381;
		    tmp1046 = tmp1044 + tmp1045;
		    tmp1047 = tmp1043 + tmp1046;
		    tmp1077 = tmp1046 - tmp1043;
		    tmp379 = tmp1 - tmp6;
		    tmp382 = tmp380 - tmp381;
		    tmp383 = tmp379 - tmp382;
		    tmp655 = tmp379 + tmp382;
	       }
	  }
	  {
	       fftw_real tmp54;
	       fftw_real tmp412;
	       fftw_real tmp409;
	       fftw_real tmp797;
	       fftw_real tmp65;
	       fftw_real tmp410;
	       fftw_real tmp415;
	       fftw_real tmp798;
	       fftw_real tmp796;
	       fftw_real tmp799;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp48;
		    fftw_real tmp407;
		    fftw_real tmp53;
		    fftw_real tmp408;
		    ASSERT_ALIGNED_DOUBLE;
		    {
			 fftw_real tmp45;
			 fftw_real tmp47;
			 fftw_real tmp44;
			 fftw_real tmp46;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp45 = c_re(inout[4 * iostride]);
			 tmp47 = c_im(inout[4 * iostride]);
			 tmp44 = c_re(W[3]);
			 tmp46 = c_im(W[3]);
			 tmp48 = (tmp44 * tmp45) + (tmp46 * tmp47);
			 tmp407 = (tmp44 * tmp47) - (tmp46 * tmp45);
		    }
		    {
			 fftw_real tmp50;
			 fftw_real tmp52;
			 fftw_real tmp49;
			 fftw_real tmp51;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp50 = c_re(inout[36 * iostride]);
			 tmp52 = c_im(inout[36 * iostride]);
			 tmp49 = c_re(W[35]);
			 tmp51 = c_im(W[35]);
			 tmp53 = (tmp49 * tmp50) + (tmp51 * tmp52);
			 tmp408 = (tmp49 * tmp52) - (tmp51 * tmp50);
		    }
		    tmp54 = tmp48 + tmp53;
		    tmp412 = tmp48 - tmp53;
		    tmp409 = tmp407 - tmp408;
		    tmp797 = tmp407 + tmp408;
	       }
	       {
		    fftw_real tmp59;
		    fftw_real tmp413;
		    fftw_real tmp64;
		    fftw_real tmp414;
		    ASSERT_ALIGNED_DOUBLE;
		    {
			 fftw_real tmp56;
			 fftw_real tmp58;
			 fftw_real tmp55;
			 fftw_real tmp57;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp56 = c_re(inout[20 * iostride]);
			 tmp58 = c_im(inout[20 * iostride]);
			 tmp55 = c_re(W[19]);
			 tmp57 = c_im(W[19]);
			 tmp59 = (tmp55 * tmp56) + (tmp57 * tmp58);
			 tmp413 = (tmp55 * tmp58) - (tmp57 * tmp56);
		    }
		    {
			 fftw_real tmp61;
			 fftw_real tmp63;
			 fftw_real tmp60;
			 fftw_real tmp62;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp61 = c_re(inout[52 * iostride]);
			 tmp63 = c_im(inout[52 * iostride]);
			 tmp60 = c_re(W[51]);
			 tmp62 = c_im(W[51]);
			 tmp64 = (tmp60 * tmp61) + (tmp62 * tmp63);
			 tmp414 = (tmp60 * tmp63) - (tmp62 * tmp61);
		    }
		    tmp65 = tmp59 + tmp64;
		    tmp410 = tmp59 - tmp64;
		    tmp415 = tmp413 - tmp414;
		    tmp798 = tmp413 + tmp414;
	       }
	       tmp66 = tmp54 + tmp65;
	       tmp796 = tmp54 - tmp65;
	       tmp799 = tmp797 - tmp798;
	       tmp800 = tmp796 - tmp799;
	       tmp909 = tmp796 + tmp799;
	       tmp993 = tmp797 + tmp798;
	       {
		    fftw_real tmp411;
		    fftw_real tmp416;
		    fftw_real tmp663;
		    fftw_real tmp664;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp411 = tmp409 + tmp410;
		    tmp416 = tmp412 - tmp415;
		    tmp417 = (K923879532 * tmp411) + (K382683432 * tmp416);
		    tmp608 = (K923879532 * tmp416) - (K382683432 * tmp411);
		    tmp663 = tmp409 - tmp410;
		    tmp664 = tmp412 + tmp415;
		    tmp665 = (K382683432 * tmp663) + (K923879532 * tmp664);
		    tmp744 = (K382683432 * tmp664) - (K923879532 * tmp663);
	       }
	  }
	  {
	       fftw_real tmp24;
	       fftw_real tmp385;
	       fftw_real tmp29;
	       fftw_real tmp386;
	       fftw_real tmp384;
	       fftw_real tmp387;
	       fftw_real tmp35;
	       fftw_real tmp390;
	       fftw_real tmp40;
	       fftw_real tmp391;
	       fftw_real tmp389;
	       fftw_real tmp392;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp21;
		    fftw_real tmp23;
		    fftw_real tmp20;
		    fftw_real tmp22;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp21 = c_re(inout[8 * iostride]);
		    tmp23 = c_im(inout[8 * iostride]);
		    tmp20 = c_re(W[7]);
		    tmp22 = c_im(W[7]);
		    tmp24 = (tmp20 * tmp21) + (tmp22 * tmp23);
		    tmp385 = (tmp20 * tmp23) - (tmp22 * tmp21);
	       }
	       {
		    fftw_real tmp26;
		    fftw_real tmp28;
		    fftw_real tmp25;
		    fftw_real tmp27;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp26 = c_re(inout[40 * iostride]);
		    tmp28 = c_im(inout[40 * iostride]);
		    tmp25 = c_re(W[39]);
		    tmp27 = c_im(W[39]);
		    tmp29 = (tmp25 * tmp26) + (tmp27 * tmp28);
		    tmp386 = (tmp25 * tmp28) - (tmp27 * tmp26);
	       }
	       tmp384 = tmp24 - tmp29;
	       tmp387 = tmp385 - tmp386;
	       {
		    fftw_real tmp32;
		    fftw_real tmp34;
		    fftw_real tmp31;
		    fftw_real tmp33;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp32 = c_re(inout[56 * iostride]);
		    tmp34 = c_im(inout[56 * iostride]);
		    tmp31 = c_re(W[55]);
		    tmp33 = c_im(W[55]);
		    tmp35 = (tmp31 * tmp32) + (tmp33 * tmp34);
		    tmp390 = (tmp31 * tmp34) - (tmp33 * tmp32);
	       }
	       {
		    fftw_real tmp37;
		    fftw_real tmp39;
		    fftw_real tmp36;
		    fftw_real tmp38;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp37 = c_re(inout[24 * iostride]);
		    tmp39 = c_im(inout[24 * iostride]);
		    tmp36 = c_re(W[23]);
		    tmp38 = c_im(W[23]);
		    tmp40 = (tmp36 * tmp37) + (tmp38 * tmp39);
		    tmp391 = (tmp36 * tmp39) - (tmp38 * tmp37);
	       }
	       tmp389 = tmp35 - tmp40;
	       tmp392 = tmp390 - tmp391;
	       {
		    fftw_real tmp30;
		    fftw_real tmp41;
		    fftw_real tmp792;
		    fftw_real tmp793;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp30 = tmp24 + tmp29;
		    tmp41 = tmp35 + tmp40;
		    tmp42 = tmp30 + tmp41;
		    tmp1076 = tmp30 - tmp41;
		    tmp792 = tmp390 + tmp391;
		    tmp793 = tmp385 + tmp386;
		    tmp794 = tmp792 - tmp793;
		    tmp1042 = tmp793 + tmp792;
	       }
	       {
		    fftw_real tmp388;
		    fftw_real tmp393;
		    fftw_real tmp656;
		    fftw_real tmp657;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp388 = tmp384 - tmp387;
		    tmp393 = tmp389 + tmp392;
		    tmp394 = K707106781 * (tmp388 + tmp393);
		    tmp1138 = K707106781 * (tmp388 - tmp393);
		    tmp656 = tmp392 - tmp389;
		    tmp657 = tmp384 + tmp387;
		    tmp658 = K707106781 * (tmp656 - tmp657);
		    tmp1106 = K707106781 * (tmp657 + tmp656);
	       }
	  }
	  {
	       fftw_real tmp287;
	       fftw_real tmp572;
	       fftw_real tmp292;
	       fftw_real tmp573;
	       fftw_real tmp293;
	       fftw_real tmp876;
	       fftw_real tmp327;
	       fftw_real tmp541;
	       fftw_real tmp544;
	       fftw_real tmp860;
	       fftw_real tmp298;
	       fftw_real tmp532;
	       fftw_real tmp303;
	       fftw_real tmp533;
	       fftw_real tmp304;
	       fftw_real tmp877;
	       fftw_real tmp316;
	       fftw_real tmp536;
	       fftw_real tmp539;
	       fftw_real tmp861;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp284;
		    fftw_real tmp286;
		    fftw_real tmp283;
		    fftw_real tmp285;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp284 = c_re(inout[63 * iostride]);
		    tmp286 = c_im(inout[63 * iostride]);
		    tmp283 = c_re(W[62]);
		    tmp285 = c_im(W[62]);
		    tmp287 = (tmp283 * tmp284) + (tmp285 * tmp286);
		    tmp572 = (tmp283 * tmp286) - (tmp285 * tmp284);
	       }
	       {
		    fftw_real tmp289;
		    fftw_real tmp291;
		    fftw_real tmp288;
		    fftw_real tmp290;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp289 = c_re(inout[31 * iostride]);
		    tmp291 = c_im(inout[31 * iostride]);
		    tmp288 = c_re(W[30]);
		    tmp290 = c_im(W[30]);
		    tmp292 = (tmp288 * tmp289) + (tmp290 * tmp291);
		    tmp573 = (tmp288 * tmp291) - (tmp290 * tmp289);
	       }
	       tmp293 = tmp287 + tmp292;
	       tmp876 = tmp572 + tmp573;
	       {
		    fftw_real tmp321;
		    fftw_real tmp542;
		    fftw_real tmp326;
		    fftw_real tmp543;
		    ASSERT_ALIGNED_DOUBLE;
		    {
			 fftw_real tmp318;
			 fftw_real tmp320;
			 fftw_real tmp317;
			 fftw_real tmp319;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp318 = c_re(inout[55 * iostride]);
			 tmp320 = c_im(inout[55 * iostride]);
			 tmp317 = c_re(W[54]);
			 tmp319 = c_im(W[54]);
			 tmp321 = (tmp317 * tmp318) + (tmp319 * tmp320);
			 tmp542 = (tmp317 * tmp320) - (tmp319 * tmp318);
		    }
		    {
			 fftw_real tmp323;
			 fftw_real tmp325;
			 fftw_real tmp322;
			 fftw_real tmp324;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp323 = c_re(inout[23 * iostride]);
			 tmp325 = c_im(inout[23 * iostride]);
			 tmp322 = c_re(W[22]);
			 tmp324 = c_im(W[22]);
			 tmp326 = (tmp322 * tmp323) + (tmp324 * tmp325);
			 tmp543 = (tmp322 * tmp325) - (tmp324 * tmp323);
		    }
		    tmp327 = tmp321 + tmp326;
		    tmp541 = tmp321 - tmp326;
		    tmp544 = tmp542 - tmp543;
		    tmp860 = tmp542 + tmp543;
	       }
	       {
		    fftw_real tmp295;
		    fftw_real tmp297;
		    fftw_real tmp294;
		    fftw_real tmp296;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp295 = c_re(inout[15 * iostride]);
		    tmp297 = c_im(inout[15 * iostride]);
		    tmp294 = c_re(W[14]);
		    tmp296 = c_im(W[14]);
		    tmp298 = (tmp294 * tmp295) + (tmp296 * tmp297);
		    tmp532 = (tmp294 * tmp297) - (tmp296 * tmp295);
	       }
	       {
		    fftw_real tmp300;
		    fftw_real tmp302;
		    fftw_real tmp299;
		    fftw_real tmp301;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp300 = c_re(inout[47 * iostride]);
		    tmp302 = c_im(inout[47 * iostride]);
		    tmp299 = c_re(W[46]);
		    tmp301 = c_im(W[46]);
		    tmp303 = (tmp299 * tmp300) + (tmp301 * tmp302);
		    tmp533 = (tmp299 * tmp302) - (tmp301 * tmp300);
	       }
	       tmp304 = tmp298 + tmp303;
	       tmp877 = tmp532 + tmp533;
	       {
		    fftw_real tmp310;
		    fftw_real tmp537;
		    fftw_real tmp315;
		    fftw_real tmp538;
		    ASSERT_ALIGNED_DOUBLE;
		    {
			 fftw_real tmp307;
			 fftw_real tmp309;
			 fftw_real tmp306;
			 fftw_real tmp308;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp307 = c_re(inout[7 * iostride]);
			 tmp309 = c_im(inout[7 * iostride]);
			 tmp306 = c_re(W[6]);
			 tmp308 = c_im(W[6]);
			 tmp310 = (tmp306 * tmp307) + (tmp308 * tmp309);
			 tmp537 = (tmp306 * tmp309) - (tmp308 * tmp307);
		    }
		    {
			 fftw_real tmp312;
			 fftw_real tmp314;
			 fftw_real tmp311;
			 fftw_real tmp313;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp312 = c_re(inout[39 * iostride]);
			 tmp314 = c_im(inout[39 * iostride]);
			 tmp311 = c_re(W[38]);
			 tmp313 = c_im(W[38]);
			 tmp315 = (tmp311 * tmp312) + (tmp313 * tmp314);
			 tmp538 = (tmp311 * tmp314) - (tmp313 * tmp312);
		    }
		    tmp316 = tmp310 + tmp315;
		    tmp536 = tmp310 - tmp315;
		    tmp539 = tmp537 - tmp538;
		    tmp861 = tmp537 + tmp538;
	       }
	       {
		    fftw_real tmp305;
		    fftw_real tmp328;
		    fftw_real tmp859;
		    fftw_real tmp862;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp305 = tmp293 + tmp304;
		    tmp328 = tmp316 + tmp327;
		    tmp329 = tmp305 + tmp328;
		    tmp1007 = tmp305 - tmp328;
		    tmp859 = tmp293 - tmp304;
		    tmp862 = tmp860 - tmp861;
		    tmp863 = tmp859 + tmp862;
		    tmp923 = tmp859 - tmp862;
	       }
	       {
		    fftw_real tmp974;
		    fftw_real tmp975;
		    fftw_real tmp878;
		    fftw_real tmp879;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp974 = tmp876 + tmp877;
		    tmp975 = tmp861 + tmp860;
		    tmp976 = tmp974 + tmp975;
		    tmp1004 = tmp974 - tmp975;
		    tmp878 = tmp876 - tmp877;
		    tmp879 = tmp316 - tmp327;
		    tmp880 = tmp878 + tmp879;
		    tmp920 = tmp878 - tmp879;
	       }
	       {
		    fftw_real tmp531;
		    fftw_real tmp534;
		    fftw_real tmp574;
		    fftw_real tmp575;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp531 = tmp287 - tmp292;
		    tmp534 = tmp532 - tmp533;
		    tmp535 = tmp531 - tmp534;
		    tmp703 = tmp531 + tmp534;
		    tmp574 = tmp572 - tmp573;
		    tmp575 = tmp298 - tmp303;
		    tmp576 = tmp574 + tmp575;
		    tmp714 = tmp574 - tmp575;
	       }
	       {
		    fftw_real tmp577;
		    fftw_real tmp578;
		    fftw_real tmp540;
		    fftw_real tmp545;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp577 = tmp536 + tmp539;
		    tmp578 = tmp544 - tmp541;
		    tmp579 = K707106781 * (tmp577 + tmp578);
		    tmp704 = K707106781 * (tmp578 - tmp577);
		    tmp540 = tmp536 - tmp539;
		    tmp545 = tmp541 + tmp544;
		    tmp546 = K707106781 * (tmp540 + tmp545);
		    tmp715 = K707106781 * (tmp540 - tmp545);
	       }
	  }
	  {
	       fftw_real tmp340;
	       fftw_real tmp564;
	       fftw_real tmp561;
	       fftw_real tmp865;
	       fftw_real tmp374;
	       fftw_real tmp551;
	       fftw_real tmp556;
	       fftw_real tmp871;
	       fftw_real tmp351;
	       fftw_real tmp562;
	       fftw_real tmp567;
	       fftw_real tmp866;
	       fftw_real tmp363;
	       fftw_real tmp553;
	       fftw_real tmp550;
	       fftw_real tmp870;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp334;
		    fftw_real tmp559;
		    fftw_real tmp339;
		    fftw_real tmp560;
		    ASSERT_ALIGNED_DOUBLE;
		    {
			 fftw_real tmp331;
			 fftw_real tmp333;
			 fftw_real tmp330;
			 fftw_real tmp332;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp331 = c_re(inout[3 * iostride]);
			 tmp333 = c_im(inout[3 * iostride]);
			 tmp330 = c_re(W[2]);
			 tmp332 = c_im(W[2]);
			 tmp334 = (tmp330 * tmp331) + (tmp332 * tmp333);
			 tmp559 = (tmp330 * tmp333) - (tmp332 * tmp331);
		    }
		    {
			 fftw_real tmp336;
			 fftw_real tmp338;
			 fftw_real tmp335;
			 fftw_real tmp337;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp336 = c_re(inout[35 * iostride]);
			 tmp338 = c_im(inout[35 * iostride]);
			 tmp335 = c_re(W[34]);
			 tmp337 = c_im(W[34]);
			 tmp339 = (tmp335 * tmp336) + (tmp337 * tmp338);
			 tmp560 = (tmp335 * tmp338) - (tmp337 * tmp336);
		    }
		    tmp340 = tmp334 + tmp339;
		    tmp564 = tmp334 - tmp339;
		    tmp561 = tmp559 - tmp560;
		    tmp865 = tmp559 + tmp560;
	       }
	       {
		    fftw_real tmp368;
		    fftw_real tmp554;
		    fftw_real tmp373;
		    fftw_real tmp555;
		    ASSERT_ALIGNED_DOUBLE;
		    {
			 fftw_real tmp365;
			 fftw_real tmp367;
			 fftw_real tmp364;
			 fftw_real tmp366;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp365 = c_re(inout[11 * iostride]);
			 tmp367 = c_im(inout[11 * iostride]);
			 tmp364 = c_re(W[10]);
			 tmp366 = c_im(W[10]);
			 tmp368 = (tmp364 * tmp365) + (tmp366 * tmp367);
			 tmp554 = (tmp364 * tmp367) - (tmp366 * tmp365);
		    }
		    {
			 fftw_real tmp370;
			 fftw_real tmp372;
			 fftw_real tmp369;
			 fftw_real tmp371;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp370 = c_re(inout[43 * iostride]);
			 tmp372 = c_im(inout[43 * iostride]);
			 tmp369 = c_re(W[42]);
			 tmp371 = c_im(W[42]);
			 tmp373 = (tmp369 * tmp370) + (tmp371 * tmp372);
			 tmp555 = (tmp369 * tmp372) - (tmp371 * tmp370);
		    }
		    tmp374 = tmp368 + tmp373;
		    tmp551 = tmp368 - tmp373;
		    tmp556 = tmp554 - tmp555;
		    tmp871 = tmp554 + tmp555;
	       }
	       {
		    fftw_real tmp345;
		    fftw_real tmp565;
		    fftw_real tmp350;
		    fftw_real tmp566;
		    ASSERT_ALIGNED_DOUBLE;
		    {
			 fftw_real tmp342;
			 fftw_real tmp344;
			 fftw_real tmp341;
			 fftw_real tmp343;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp342 = c_re(inout[19 * iostride]);
			 tmp344 = c_im(inout[19 * iostride]);
			 tmp341 = c_re(W[18]);
			 tmp343 = c_im(W[18]);
			 tmp345 = (tmp341 * tmp342) + (tmp343 * tmp344);
			 tmp565 = (tmp341 * tmp344) - (tmp343 * tmp342);
		    }
		    {
			 fftw_real tmp347;
			 fftw_real tmp349;
			 fftw_real tmp346;
			 fftw_real tmp348;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp347 = c_re(inout[51 * iostride]);
			 tmp349 = c_im(inout[51 * iostride]);
			 tmp346 = c_re(W[50]);
			 tmp348 = c_im(W[50]);
			 tmp350 = (tmp346 * tmp347) + (tmp348 * tmp349);
			 tmp566 = (tmp346 * tmp349) - (tmp348 * tmp347);
		    }
		    tmp351 = tmp345 + tmp350;
		    tmp562 = tmp345 - tmp350;
		    tmp567 = tmp565 - tmp566;
		    tmp866 = tmp565 + tmp566;
	       }
	       {
		    fftw_real tmp357;
		    fftw_real tmp548;
		    fftw_real tmp362;
		    fftw_real tmp549;
		    ASSERT_ALIGNED_DOUBLE;
		    {
			 fftw_real tmp354;
			 fftw_real tmp356;
			 fftw_real tmp353;
			 fftw_real tmp355;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp354 = c_re(inout[59 * iostride]);
			 tmp356 = c_im(inout[59 * iostride]);
			 tmp353 = c_re(W[58]);
			 tmp355 = c_im(W[58]);
			 tmp357 = (tmp353 * tmp354) + (tmp355 * tmp356);
			 tmp548 = (tmp353 * tmp356) - (tmp355 * tmp354);
		    }
		    {
			 fftw_real tmp359;
			 fftw_real tmp361;
			 fftw_real tmp358;
			 fftw_real tmp360;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp359 = c_re(inout[27 * iostride]);
			 tmp361 = c_im(inout[27 * iostride]);
			 tmp358 = c_re(W[26]);
			 tmp360 = c_im(W[26]);
			 tmp362 = (tmp358 * tmp359) + (tmp360 * tmp361);
			 tmp549 = (tmp358 * tmp361) - (tmp360 * tmp359);
		    }
		    tmp363 = tmp357 + tmp362;
		    tmp553 = tmp357 - tmp362;
		    tmp550 = tmp548 - tmp549;
		    tmp870 = tmp548 + tmp549;
	       }
	       {
		    fftw_real tmp352;
		    fftw_real tmp375;
		    fftw_real tmp864;
		    fftw_real tmp867;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp352 = tmp340 + tmp351;
		    tmp375 = tmp363 + tmp374;
		    tmp376 = tmp352 + tmp375;
		    tmp1005 = tmp352 - tmp375;
		    tmp864 = tmp340 - tmp351;
		    tmp867 = tmp865 - tmp866;
		    tmp868 = tmp864 - tmp867;
		    tmp881 = tmp864 + tmp867;
	       }
	       {
		    fftw_real tmp977;
		    fftw_real tmp978;
		    fftw_real tmp869;
		    fftw_real tmp872;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp977 = tmp865 + tmp866;
		    tmp978 = tmp870 + tmp871;
		    tmp979 = tmp977 + tmp978;
		    tmp1008 = tmp978 - tmp977;
		    tmp869 = tmp363 - tmp374;
		    tmp872 = tmp870 - tmp871;
		    tmp873 = tmp869 + tmp872;
		    tmp882 = tmp872 - tmp869;
	       }
	       {
		    fftw_real tmp552;
		    fftw_real tmp557;
		    fftw_real tmp706;
		    fftw_real tmp707;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp552 = tmp550 + tmp551;
		    tmp557 = tmp553 - tmp556;
		    tmp558 = (K923879532 * tmp552) - (K382683432 * tmp557);
		    tmp582 = (K382683432 * tmp552) + (K923879532 * tmp557);
		    tmp706 = tmp550 - tmp551;
		    tmp707 = tmp553 + tmp556;
		    tmp708 = (K382683432 * tmp706) - (K923879532 * tmp707);
		    tmp718 = (K923879532 * tmp706) + (K382683432 * tmp707);
	       }
	       {
		    fftw_real tmp563;
		    fftw_real tmp568;
		    fftw_real tmp709;
		    fftw_real tmp710;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp563 = tmp561 + tmp562;
		    tmp568 = tmp564 - tmp567;
		    tmp569 = (K923879532 * tmp563) + (K382683432 * tmp568);
		    tmp581 = (K923879532 * tmp568) - (K382683432 * tmp563);
		    tmp709 = tmp561 - tmp562;
		    tmp710 = tmp564 + tmp567;
		    tmp711 = (K382683432 * tmp709) + (K923879532 * tmp710);
		    tmp717 = (K382683432 * tmp710) - (K923879532 * tmp709);
	       }
	  }
	  {
	       fftw_real tmp77;
	       fftw_real tmp401;
	       fftw_real tmp398;
	       fftw_real tmp802;
	       fftw_real tmp88;
	       fftw_real tmp399;
	       fftw_real tmp404;
	       fftw_real tmp803;
	       fftw_real tmp801;
	       fftw_real tmp804;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp71;
		    fftw_real tmp396;
		    fftw_real tmp76;
		    fftw_real tmp397;
		    ASSERT_ALIGNED_DOUBLE;
		    {
			 fftw_real tmp68;
			 fftw_real tmp70;
			 fftw_real tmp67;
			 fftw_real tmp69;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp68 = c_re(inout[60 * iostride]);
			 tmp70 = c_im(inout[60 * iostride]);
			 tmp67 = c_re(W[59]);
			 tmp69 = c_im(W[59]);
			 tmp71 = (tmp67 * tmp68) + (tmp69 * tmp70);
			 tmp396 = (tmp67 * tmp70) - (tmp69 * tmp68);
		    }
		    {
			 fftw_real tmp73;
			 fftw_real tmp75;
			 fftw_real tmp72;
			 fftw_real tmp74;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp73 = c_re(inout[28 * iostride]);
			 tmp75 = c_im(inout[28 * iostride]);
			 tmp72 = c_re(W[27]);
			 tmp74 = c_im(W[27]);
			 tmp76 = (tmp72 * tmp73) + (tmp74 * tmp75);
			 tmp397 = (tmp72 * tmp75) - (tmp74 * tmp73);
		    }
		    tmp77 = tmp71 + tmp76;
		    tmp401 = tmp71 - tmp76;
		    tmp398 = tmp396 - tmp397;
		    tmp802 = tmp396 + tmp397;
	       }
	       {
		    fftw_real tmp82;
		    fftw_real tmp402;
		    fftw_real tmp87;
		    fftw_real tmp403;
		    ASSERT_ALIGNED_DOUBLE;
		    {
			 fftw_real tmp79;
			 fftw_real tmp81;
			 fftw_real tmp78;
			 fftw_real tmp80;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp79 = c_re(inout[12 * iostride]);
			 tmp81 = c_im(inout[12 * iostride]);
			 tmp78 = c_re(W[11]);
			 tmp80 = c_im(W[11]);
			 tmp82 = (tmp78 * tmp79) + (tmp80 * tmp81);
			 tmp402 = (tmp78 * tmp81) - (tmp80 * tmp79);
		    }
		    {
			 fftw_real tmp84;
			 fftw_real tmp86;
			 fftw_real tmp83;
			 fftw_real tmp85;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp84 = c_re(inout[44 * iostride]);
			 tmp86 = c_im(inout[44 * iostride]);
			 tmp83 = c_re(W[43]);
			 tmp85 = c_im(W[43]);
			 tmp87 = (tmp83 * tmp84) + (tmp85 * tmp86);
			 tmp403 = (tmp83 * tmp86) - (tmp85 * tmp84);
		    }
		    tmp88 = tmp82 + tmp87;
		    tmp399 = tmp82 - tmp87;
		    tmp404 = tmp402 - tmp403;
		    tmp803 = tmp402 + tmp403;
	       }
	       tmp89 = tmp77 + tmp88;
	       tmp801 = tmp77 - tmp88;
	       tmp804 = tmp802 - tmp803;
	       tmp805 = tmp801 + tmp804;
	       tmp908 = tmp804 - tmp801;
	       tmp992 = tmp802 + tmp803;
	       {
		    fftw_real tmp400;
		    fftw_real tmp405;
		    fftw_real tmp660;
		    fftw_real tmp661;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp400 = tmp398 + tmp399;
		    tmp405 = tmp401 - tmp404;
		    tmp406 = (K923879532 * tmp400) - (K382683432 * tmp405);
		    tmp609 = (K382683432 * tmp400) + (K923879532 * tmp405);
		    tmp660 = tmp398 - tmp399;
		    tmp661 = tmp401 + tmp404;
		    tmp662 = (K382683432 * tmp660) - (K923879532 * tmp661);
		    tmp745 = (K923879532 * tmp660) + (K382683432 * tmp661);
	       }
	  }
	  {
	       fftw_real tmp143;
	       fftw_real tmp420;
	       fftw_real tmp148;
	       fftw_real tmp421;
	       fftw_real tmp149;
	       fftw_real tmp808;
	       fftw_real tmp183;
	       fftw_real tmp433;
	       fftw_real tmp432;
	       fftw_real tmp814;
	       fftw_real tmp154;
	       fftw_real tmp438;
	       fftw_real tmp159;
	       fftw_real tmp439;
	       fftw_real tmp160;
	       fftw_real tmp809;
	       fftw_real tmp172;
	       fftw_real tmp425;
	       fftw_real tmp428;
	       fftw_real tmp815;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp140;
		    fftw_real tmp142;
		    fftw_real tmp139;
		    fftw_real tmp141;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp140 = c_re(inout[62 * iostride]);
		    tmp142 = c_im(inout[62 * iostride]);
		    tmp139 = c_re(W[61]);
		    tmp141 = c_im(W[61]);
		    tmp143 = (tmp139 * tmp140) + (tmp141 * tmp142);
		    tmp420 = (tmp139 * tmp142) - (tmp141 * tmp140);
	       }
	       {
		    fftw_real tmp145;
		    fftw_real tmp147;
		    fftw_real tmp144;
		    fftw_real tmp146;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp145 = c_re(inout[30 * iostride]);
		    tmp147 = c_im(inout[30 * iostride]);
		    tmp144 = c_re(W[29]);
		    tmp146 = c_im(W[29]);
		    tmp148 = (tmp144 * tmp145) + (tmp146 * tmp147);
		    tmp421 = (tmp144 * tmp147) - (tmp146 * tmp145);
	       }
	       tmp149 = tmp143 + tmp148;
	       tmp808 = tmp420 + tmp421;
	       {
		    fftw_real tmp177;
		    fftw_real tmp430;
		    fftw_real tmp182;
		    fftw_real tmp431;
		    ASSERT_ALIGNED_DOUBLE;
		    {
			 fftw_real tmp174;
			 fftw_real tmp176;
			 fftw_real tmp173;
			 fftw_real tmp175;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp174 = c_re(inout[54 * iostride]);
			 tmp176 = c_im(inout[54 * iostride]);
			 tmp173 = c_re(W[53]);
			 tmp175 = c_im(W[53]);
			 tmp177 = (tmp173 * tmp174) + (tmp175 * tmp176);
			 tmp430 = (tmp173 * tmp176) - (tmp175 * tmp174);
		    }
		    {
			 fftw_real tmp179;
			 fftw_real tmp181;
			 fftw_real tmp178;
			 fftw_real tmp180;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp179 = c_re(inout[22 * iostride]);
			 tmp181 = c_im(inout[22 * iostride]);
			 tmp178 = c_re(W[21]);
			 tmp180 = c_im(W[21]);
			 tmp182 = (tmp178 * tmp179) + (tmp180 * tmp181);
			 tmp431 = (tmp178 * tmp181) - (tmp180 * tmp179);
		    }
		    tmp183 = tmp177 + tmp182;
		    tmp433 = tmp177 - tmp182;
		    tmp432 = tmp430 - tmp431;
		    tmp814 = tmp430 + tmp431;
	       }
	       {
		    fftw_real tmp151;
		    fftw_real tmp153;
		    fftw_real tmp150;
		    fftw_real tmp152;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp151 = c_re(inout[14 * iostride]);
		    tmp153 = c_im(inout[14 * iostride]);
		    tmp150 = c_re(W[13]);
		    tmp152 = c_im(W[13]);
		    tmp154 = (tmp150 * tmp151) + (tmp152 * tmp153);
		    tmp438 = (tmp150 * tmp153) - (tmp152 * tmp151);
	       }
	       {
		    fftw_real tmp156;
		    fftw_real tmp158;
		    fftw_real tmp155;
		    fftw_real tmp157;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp156 = c_re(inout[46 * iostride]);
		    tmp158 = c_im(inout[46 * iostride]);
		    tmp155 = c_re(W[45]);
		    tmp157 = c_im(W[45]);
		    tmp159 = (tmp155 * tmp156) + (tmp157 * tmp158);
		    tmp439 = (tmp155 * tmp158) - (tmp157 * tmp156);
	       }
	       tmp160 = tmp154 + tmp159;
	       tmp809 = tmp438 + tmp439;
	       {
		    fftw_real tmp166;
		    fftw_real tmp426;
		    fftw_real tmp171;
		    fftw_real tmp427;
		    ASSERT_ALIGNED_DOUBLE;
		    {
			 fftw_real tmp163;
			 fftw_real tmp165;
			 fftw_real tmp162;
			 fftw_real tmp164;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp163 = c_re(inout[6 * iostride]);
			 tmp165 = c_im(inout[6 * iostride]);
			 tmp162 = c_re(W[5]);
			 tmp164 = c_im(W[5]);
			 tmp166 = (tmp162 * tmp163) + (tmp164 * tmp165);
			 tmp426 = (tmp162 * tmp165) - (tmp164 * tmp163);
		    }
		    {
			 fftw_real tmp168;
			 fftw_real tmp170;
			 fftw_real tmp167;
			 fftw_real tmp169;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp168 = c_re(inout[38 * iostride]);
			 tmp170 = c_im(inout[38 * iostride]);
			 tmp167 = c_re(W[37]);
			 tmp169 = c_im(W[37]);
			 tmp171 = (tmp167 * tmp168) + (tmp169 * tmp170);
			 tmp427 = (tmp167 * tmp170) - (tmp169 * tmp168);
		    }
		    tmp172 = tmp166 + tmp171;
		    tmp425 = tmp166 - tmp171;
		    tmp428 = tmp426 - tmp427;
		    tmp815 = tmp426 + tmp427;
	       }
	       {
		    fftw_real tmp810;
		    fftw_real tmp811;
		    fftw_real tmp813;
		    fftw_real tmp816;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp161 = tmp149 + tmp160;
		    tmp184 = tmp172 + tmp183;
		    tmp997 = tmp161 - tmp184;
		    tmp810 = tmp808 - tmp809;
		    tmp811 = tmp172 - tmp183;
		    tmp812 = tmp810 + tmp811;
		    tmp912 = tmp810 - tmp811;
		    tmp956 = tmp808 + tmp809;
		    tmp957 = tmp815 + tmp814;
		    tmp996 = tmp956 - tmp957;
		    tmp813 = tmp149 - tmp160;
		    tmp816 = tmp814 - tmp815;
		    tmp817 = tmp813 + tmp816;
		    tmp913 = tmp813 - tmp816;
	       }
	       {
		    fftw_real tmp422;
		    fftw_real tmp423;
		    fftw_real tmp437;
		    fftw_real tmp440;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp422 = tmp420 - tmp421;
		    tmp423 = tmp154 - tmp159;
		    tmp424 = tmp422 + tmp423;
		    tmp668 = tmp422 - tmp423;
		    tmp437 = tmp143 - tmp148;
		    tmp440 = tmp438 - tmp439;
		    tmp441 = tmp437 - tmp440;
		    tmp671 = tmp437 + tmp440;
	       }
	       {
		    fftw_real tmp442;
		    fftw_real tmp443;
		    fftw_real tmp429;
		    fftw_real tmp434;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp442 = tmp425 - tmp428;
		    tmp443 = tmp433 + tmp432;
		    tmp444 = K707106781 * (tmp442 + tmp443);
		    tmp669 = K707106781 * (tmp442 - tmp443);
		    tmp429 = tmp425 + tmp428;
		    tmp434 = tmp432 - tmp433;
		    tmp435 = K707106781 * (tmp429 + tmp434);
		    tmp672 = K707106781 * (tmp434 - tmp429);
	       }
	  }
	  {
	       fftw_real tmp96;
	       fftw_real tmp464;
	       fftw_real tmp101;
	       fftw_real tmp465;
	       fftw_real tmp102;
	       fftw_real tmp819;
	       fftw_real tmp136;
	       fftw_real tmp457;
	       fftw_real tmp460;
	       fftw_real tmp825;
	       fftw_real tmp107;
	       fftw_real tmp448;
	       fftw_real tmp112;
	       fftw_real tmp449;
	       fftw_real tmp113;
	       fftw_real tmp820;
	       fftw_real tmp125;
	       fftw_real tmp452;
	       fftw_real tmp455;
	       fftw_real tmp826;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp93;
		    fftw_real tmp95;
		    fftw_real tmp92;
		    fftw_real tmp94;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp93 = c_re(inout[2 * iostride]);
		    tmp95 = c_im(inout[2 * iostride]);
		    tmp92 = c_re(W[1]);
		    tmp94 = c_im(W[1]);
		    tmp96 = (tmp92 * tmp93) + (tmp94 * tmp95);
		    tmp464 = (tmp92 * tmp95) - (tmp94 * tmp93);
	       }
	       {
		    fftw_real tmp98;
		    fftw_real tmp100;
		    fftw_real tmp97;
		    fftw_real tmp99;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp98 = c_re(inout[34 * iostride]);
		    tmp100 = c_im(inout[34 * iostride]);
		    tmp97 = c_re(W[33]);
		    tmp99 = c_im(W[33]);
		    tmp101 = (tmp97 * tmp98) + (tmp99 * tmp100);
		    tmp465 = (tmp97 * tmp100) - (tmp99 * tmp98);
	       }
	       tmp102 = tmp96 + tmp101;
	       tmp819 = tmp464 + tmp465;
	       {
		    fftw_real tmp130;
		    fftw_real tmp458;
		    fftw_real tmp135;
		    fftw_real tmp459;
		    ASSERT_ALIGNED_DOUBLE;
		    {
			 fftw_real tmp127;
			 fftw_real tmp129;
			 fftw_real tmp126;
			 fftw_real tmp128;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp127 = c_re(inout[58 * iostride]);
			 tmp129 = c_im(inout[58 * iostride]);
			 tmp126 = c_re(W[57]);
			 tmp128 = c_im(W[57]);
			 tmp130 = (tmp126 * tmp127) + (tmp128 * tmp129);
			 tmp458 = (tmp126 * tmp129) - (tmp128 * tmp127);
		    }
		    {
			 fftw_real tmp132;
			 fftw_real tmp134;
			 fftw_real tmp131;
			 fftw_real tmp133;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp132 = c_re(inout[26 * iostride]);
			 tmp134 = c_im(inout[26 * iostride]);
			 tmp131 = c_re(W[25]);
			 tmp133 = c_im(W[25]);
			 tmp135 = (tmp131 * tmp132) + (tmp133 * tmp134);
			 tmp459 = (tmp131 * tmp134) - (tmp133 * tmp132);
		    }
		    tmp136 = tmp130 + tmp135;
		    tmp457 = tmp130 - tmp135;
		    tmp460 = tmp458 - tmp459;
		    tmp825 = tmp458 + tmp459;
	       }
	       {
		    fftw_real tmp104;
		    fftw_real tmp106;
		    fftw_real tmp103;
		    fftw_real tmp105;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp104 = c_re(inout[18 * iostride]);
		    tmp106 = c_im(inout[18 * iostride]);
		    tmp103 = c_re(W[17]);
		    tmp105 = c_im(W[17]);
		    tmp107 = (tmp103 * tmp104) + (tmp105 * tmp106);
		    tmp448 = (tmp103 * tmp106) - (tmp105 * tmp104);
	       }
	       {
		    fftw_real tmp109;
		    fftw_real tmp111;
		    fftw_real tmp108;
		    fftw_real tmp110;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp109 = c_re(inout[50 * iostride]);
		    tmp111 = c_im(inout[50 * iostride]);
		    tmp108 = c_re(W[49]);
		    tmp110 = c_im(W[49]);
		    tmp112 = (tmp108 * tmp109) + (tmp110 * tmp111);
		    tmp449 = (tmp108 * tmp111) - (tmp110 * tmp109);
	       }
	       tmp113 = tmp107 + tmp112;
	       tmp820 = tmp448 + tmp449;
	       {
		    fftw_real tmp119;
		    fftw_real tmp453;
		    fftw_real tmp124;
		    fftw_real tmp454;
		    ASSERT_ALIGNED_DOUBLE;
		    {
			 fftw_real tmp116;
			 fftw_real tmp118;
			 fftw_real tmp115;
			 fftw_real tmp117;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp116 = c_re(inout[10 * iostride]);
			 tmp118 = c_im(inout[10 * iostride]);
			 tmp115 = c_re(W[9]);
			 tmp117 = c_im(W[9]);
			 tmp119 = (tmp115 * tmp116) + (tmp117 * tmp118);
			 tmp453 = (tmp115 * tmp118) - (tmp117 * tmp116);
		    }
		    {
			 fftw_real tmp121;
			 fftw_real tmp123;
			 fftw_real tmp120;
			 fftw_real tmp122;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp121 = c_re(inout[42 * iostride]);
			 tmp123 = c_im(inout[42 * iostride]);
			 tmp120 = c_re(W[41]);
			 tmp122 = c_im(W[41]);
			 tmp124 = (tmp120 * tmp121) + (tmp122 * tmp123);
			 tmp454 = (tmp120 * tmp123) - (tmp122 * tmp121);
		    }
		    tmp125 = tmp119 + tmp124;
		    tmp452 = tmp119 - tmp124;
		    tmp455 = tmp453 - tmp454;
		    tmp826 = tmp453 + tmp454;
	       }
	       {
		    fftw_real tmp821;
		    fftw_real tmp822;
		    fftw_real tmp824;
		    fftw_real tmp827;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp114 = tmp102 + tmp113;
		    tmp137 = tmp125 + tmp136;
		    tmp999 = tmp114 - tmp137;
		    tmp821 = tmp819 - tmp820;
		    tmp822 = tmp125 - tmp136;
		    tmp823 = tmp821 + tmp822;
		    tmp915 = tmp821 - tmp822;
		    tmp959 = tmp819 + tmp820;
		    tmp960 = tmp826 + tmp825;
		    tmp1000 = tmp959 - tmp960;
		    tmp824 = tmp102 - tmp113;
		    tmp827 = tmp825 - tmp826;
		    tmp828 = tmp824 + tmp827;
		    tmp916 = tmp824 - tmp827;
	       }
	       {
		    fftw_real tmp447;
		    fftw_real tmp450;
		    fftw_real tmp466;
		    fftw_real tmp467;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp447 = tmp96 - tmp101;
		    tmp450 = tmp448 - tmp449;
		    tmp451 = tmp447 - tmp450;
		    tmp678 = tmp447 + tmp450;
		    tmp466 = tmp464 - tmp465;
		    tmp467 = tmp107 - tmp112;
		    tmp468 = tmp466 + tmp467;
		    tmp675 = tmp466 - tmp467;
	       }
	       {
		    fftw_real tmp469;
		    fftw_real tmp470;
		    fftw_real tmp456;
		    fftw_real tmp461;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp469 = tmp452 + tmp455;
		    tmp470 = tmp460 - tmp457;
		    tmp471 = K707106781 * (tmp469 + tmp470);
		    tmp679 = K707106781 * (tmp470 - tmp469);
		    tmp456 = tmp452 - tmp455;
		    tmp461 = tmp457 + tmp460;
		    tmp462 = K707106781 * (tmp456 + tmp461);
		    tmp676 = K707106781 * (tmp456 - tmp461);
	       }
	  }
	  {
	       fftw_real tmp192;
	       fftw_real tmp517;
	       fftw_real tmp197;
	       fftw_real tmp518;
	       fftw_real tmp198;
	       fftw_real tmp849;
	       fftw_real tmp232;
	       fftw_real tmp486;
	       fftw_real tmp489;
	       fftw_real tmp833;
	       fftw_real tmp203;
	       fftw_real tmp477;
	       fftw_real tmp208;
	       fftw_real tmp478;
	       fftw_real tmp209;
	       fftw_real tmp850;
	       fftw_real tmp221;
	       fftw_real tmp481;
	       fftw_real tmp484;
	       fftw_real tmp834;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp189;
		    fftw_real tmp191;
		    fftw_real tmp188;
		    fftw_real tmp190;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp189 = c_re(inout[iostride]);
		    tmp191 = c_im(inout[iostride]);
		    tmp188 = c_re(W[0]);
		    tmp190 = c_im(W[0]);
		    tmp192 = (tmp188 * tmp189) + (tmp190 * tmp191);
		    tmp517 = (tmp188 * tmp191) - (tmp190 * tmp189);
	       }
	       {
		    fftw_real tmp194;
		    fftw_real tmp196;
		    fftw_real tmp193;
		    fftw_real tmp195;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp194 = c_re(inout[33 * iostride]);
		    tmp196 = c_im(inout[33 * iostride]);
		    tmp193 = c_re(W[32]);
		    tmp195 = c_im(W[32]);
		    tmp197 = (tmp193 * tmp194) + (tmp195 * tmp196);
		    tmp518 = (tmp193 * tmp196) - (tmp195 * tmp194);
	       }
	       tmp198 = tmp192 + tmp197;
	       tmp849 = tmp517 + tmp518;
	       {
		    fftw_real tmp226;
		    fftw_real tmp487;
		    fftw_real tmp231;
		    fftw_real tmp488;
		    ASSERT_ALIGNED_DOUBLE;
		    {
			 fftw_real tmp223;
			 fftw_real tmp225;
			 fftw_real tmp222;
			 fftw_real tmp224;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp223 = c_re(inout[57 * iostride]);
			 tmp225 = c_im(inout[57 * iostride]);
			 tmp222 = c_re(W[56]);
			 tmp224 = c_im(W[56]);
			 tmp226 = (tmp222 * tmp223) + (tmp224 * tmp225);
			 tmp487 = (tmp222 * tmp225) - (tmp224 * tmp223);
		    }
		    {
			 fftw_real tmp228;
			 fftw_real tmp230;
			 fftw_real tmp227;
			 fftw_real tmp229;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp228 = c_re(inout[25 * iostride]);
			 tmp230 = c_im(inout[25 * iostride]);
			 tmp227 = c_re(W[24]);
			 tmp229 = c_im(W[24]);
			 tmp231 = (tmp227 * tmp228) + (tmp229 * tmp230);
			 tmp488 = (tmp227 * tmp230) - (tmp229 * tmp228);
		    }
		    tmp232 = tmp226 + tmp231;
		    tmp486 = tmp226 - tmp231;
		    tmp489 = tmp487 - tmp488;
		    tmp833 = tmp487 + tmp488;
	       }
	       {
		    fftw_real tmp200;
		    fftw_real tmp202;
		    fftw_real tmp199;
		    fftw_real tmp201;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp200 = c_re(inout[17 * iostride]);
		    tmp202 = c_im(inout[17 * iostride]);
		    tmp199 = c_re(W[16]);
		    tmp201 = c_im(W[16]);
		    tmp203 = (tmp199 * tmp200) + (tmp201 * tmp202);
		    tmp477 = (tmp199 * tmp202) - (tmp201 * tmp200);
	       }
	       {
		    fftw_real tmp205;
		    fftw_real tmp207;
		    fftw_real tmp204;
		    fftw_real tmp206;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp205 = c_re(inout[49 * iostride]);
		    tmp207 = c_im(inout[49 * iostride]);
		    tmp204 = c_re(W[48]);
		    tmp206 = c_im(W[48]);
		    tmp208 = (tmp204 * tmp205) + (tmp206 * tmp207);
		    tmp478 = (tmp204 * tmp207) - (tmp206 * tmp205);
	       }
	       tmp209 = tmp203 + tmp208;
	       tmp850 = tmp477 + tmp478;
	       {
		    fftw_real tmp215;
		    fftw_real tmp482;
		    fftw_real tmp220;
		    fftw_real tmp483;
		    ASSERT_ALIGNED_DOUBLE;
		    {
			 fftw_real tmp212;
			 fftw_real tmp214;
			 fftw_real tmp211;
			 fftw_real tmp213;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp212 = c_re(inout[9 * iostride]);
			 tmp214 = c_im(inout[9 * iostride]);
			 tmp211 = c_re(W[8]);
			 tmp213 = c_im(W[8]);
			 tmp215 = (tmp211 * tmp212) + (tmp213 * tmp214);
			 tmp482 = (tmp211 * tmp214) - (tmp213 * tmp212);
		    }
		    {
			 fftw_real tmp217;
			 fftw_real tmp219;
			 fftw_real tmp216;
			 fftw_real tmp218;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp217 = c_re(inout[41 * iostride]);
			 tmp219 = c_im(inout[41 * iostride]);
			 tmp216 = c_re(W[40]);
			 tmp218 = c_im(W[40]);
			 tmp220 = (tmp216 * tmp217) + (tmp218 * tmp219);
			 tmp483 = (tmp216 * tmp219) - (tmp218 * tmp217);
		    }
		    tmp221 = tmp215 + tmp220;
		    tmp481 = tmp215 - tmp220;
		    tmp484 = tmp482 - tmp483;
		    tmp834 = tmp482 + tmp483;
	       }
	       {
		    fftw_real tmp210;
		    fftw_real tmp233;
		    fftw_real tmp832;
		    fftw_real tmp835;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp210 = tmp198 + tmp209;
		    tmp233 = tmp221 + tmp232;
		    tmp234 = tmp210 + tmp233;
		    tmp1014 = tmp210 - tmp233;
		    tmp832 = tmp198 - tmp209;
		    tmp835 = tmp833 - tmp834;
		    tmp836 = tmp832 + tmp835;
		    tmp930 = tmp832 - tmp835;
	       }
	       {
		    fftw_real tmp965;
		    fftw_real tmp966;
		    fftw_real tmp851;
		    fftw_real tmp852;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp965 = tmp849 + tmp850;
		    tmp966 = tmp834 + tmp833;
		    tmp967 = tmp965 + tmp966;
		    tmp1011 = tmp965 - tmp966;
		    tmp851 = tmp849 - tmp850;
		    tmp852 = tmp221 - tmp232;
		    tmp853 = tmp851 + tmp852;
		    tmp927 = tmp851 - tmp852;
	       }
	       {
		    fftw_real tmp476;
		    fftw_real tmp479;
		    fftw_real tmp519;
		    fftw_real tmp520;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp476 = tmp192 - tmp197;
		    tmp479 = tmp477 - tmp478;
		    tmp480 = tmp476 - tmp479;
		    tmp684 = tmp476 + tmp479;
		    tmp519 = tmp517 - tmp518;
		    tmp520 = tmp203 - tmp208;
		    tmp521 = tmp519 + tmp520;
		    tmp695 = tmp519 - tmp520;
	       }
	       {
		    fftw_real tmp522;
		    fftw_real tmp523;
		    fftw_real tmp485;
		    fftw_real tmp490;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp522 = tmp481 + tmp484;
		    tmp523 = tmp489 - tmp486;
		    tmp524 = K707106781 * (tmp522 + tmp523);
		    tmp685 = K707106781 * (tmp523 - tmp522);
		    tmp485 = tmp481 - tmp484;
		    tmp490 = tmp486 + tmp489;
		    tmp491 = K707106781 * (tmp485 + tmp490);
		    tmp696 = K707106781 * (tmp485 - tmp490);
	       }
	  }
	  {
	       fftw_real tmp245;
	       fftw_real tmp509;
	       fftw_real tmp506;
	       fftw_real tmp838;
	       fftw_real tmp279;
	       fftw_real tmp496;
	       fftw_real tmp501;
	       fftw_real tmp844;
	       fftw_real tmp256;
	       fftw_real tmp507;
	       fftw_real tmp512;
	       fftw_real tmp839;
	       fftw_real tmp268;
	       fftw_real tmp498;
	       fftw_real tmp495;
	       fftw_real tmp843;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp239;
		    fftw_real tmp504;
		    fftw_real tmp244;
		    fftw_real tmp505;
		    ASSERT_ALIGNED_DOUBLE;
		    {
			 fftw_real tmp236;
			 fftw_real tmp238;
			 fftw_real tmp235;
			 fftw_real tmp237;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp236 = c_re(inout[5 * iostride]);
			 tmp238 = c_im(inout[5 * iostride]);
			 tmp235 = c_re(W[4]);
			 tmp237 = c_im(W[4]);
			 tmp239 = (tmp235 * tmp236) + (tmp237 * tmp238);
			 tmp504 = (tmp235 * tmp238) - (tmp237 * tmp236);
		    }
		    {
			 fftw_real tmp241;
			 fftw_real tmp243;
			 fftw_real tmp240;
			 fftw_real tmp242;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp241 = c_re(inout[37 * iostride]);
			 tmp243 = c_im(inout[37 * iostride]);
			 tmp240 = c_re(W[36]);
			 tmp242 = c_im(W[36]);
			 tmp244 = (tmp240 * tmp241) + (tmp242 * tmp243);
			 tmp505 = (tmp240 * tmp243) - (tmp242 * tmp241);
		    }
		    tmp245 = tmp239 + tmp244;
		    tmp509 = tmp239 - tmp244;
		    tmp506 = tmp504 - tmp505;
		    tmp838 = tmp504 + tmp505;
	       }
	       {
		    fftw_real tmp273;
		    fftw_real tmp499;
		    fftw_real tmp278;
		    fftw_real tmp500;
		    ASSERT_ALIGNED_DOUBLE;
		    {
			 fftw_real tmp270;
			 fftw_real tmp272;
			 fftw_real tmp269;
			 fftw_real tmp271;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp270 = c_re(inout[13 * iostride]);
			 tmp272 = c_im(inout[13 * iostride]);
			 tmp269 = c_re(W[12]);
			 tmp271 = c_im(W[12]);
			 tmp273 = (tmp269 * tmp270) + (tmp271 * tmp272);
			 tmp499 = (tmp269 * tmp272) - (tmp271 * tmp270);
		    }
		    {
			 fftw_real tmp275;
			 fftw_real tmp277;
			 fftw_real tmp274;
			 fftw_real tmp276;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp275 = c_re(inout[45 * iostride]);
			 tmp277 = c_im(inout[45 * iostride]);
			 tmp274 = c_re(W[44]);
			 tmp276 = c_im(W[44]);
			 tmp278 = (tmp274 * tmp275) + (tmp276 * tmp277);
			 tmp500 = (tmp274 * tmp277) - (tmp276 * tmp275);
		    }
		    tmp279 = tmp273 + tmp278;
		    tmp496 = tmp273 - tmp278;
		    tmp501 = tmp499 - tmp500;
		    tmp844 = tmp499 + tmp500;
	       }
	       {
		    fftw_real tmp250;
		    fftw_real tmp510;
		    fftw_real tmp255;
		    fftw_real tmp511;
		    ASSERT_ALIGNED_DOUBLE;
		    {
			 fftw_real tmp247;
			 fftw_real tmp249;
			 fftw_real tmp246;
			 fftw_real tmp248;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp247 = c_re(inout[21 * iostride]);
			 tmp249 = c_im(inout[21 * iostride]);
			 tmp246 = c_re(W[20]);
			 tmp248 = c_im(W[20]);
			 tmp250 = (tmp246 * tmp247) + (tmp248 * tmp249);
			 tmp510 = (tmp246 * tmp249) - (tmp248 * tmp247);
		    }
		    {
			 fftw_real tmp252;
			 fftw_real tmp254;
			 fftw_real tmp251;
			 fftw_real tmp253;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp252 = c_re(inout[53 * iostride]);
			 tmp254 = c_im(inout[53 * iostride]);
			 tmp251 = c_re(W[52]);
			 tmp253 = c_im(W[52]);
			 tmp255 = (tmp251 * tmp252) + (tmp253 * tmp254);
			 tmp511 = (tmp251 * tmp254) - (tmp253 * tmp252);
		    }
		    tmp256 = tmp250 + tmp255;
		    tmp507 = tmp250 - tmp255;
		    tmp512 = tmp510 - tmp511;
		    tmp839 = tmp510 + tmp511;
	       }
	       {
		    fftw_real tmp262;
		    fftw_real tmp493;
		    fftw_real tmp267;
		    fftw_real tmp494;
		    ASSERT_ALIGNED_DOUBLE;
		    {
			 fftw_real tmp259;
			 fftw_real tmp261;
			 fftw_real tmp258;
			 fftw_real tmp260;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp259 = c_re(inout[61 * iostride]);
			 tmp261 = c_im(inout[61 * iostride]);
			 tmp258 = c_re(W[60]);
			 tmp260 = c_im(W[60]);
			 tmp262 = (tmp258 * tmp259) + (tmp260 * tmp261);
			 tmp493 = (tmp258 * tmp261) - (tmp260 * tmp259);
		    }
		    {
			 fftw_real tmp264;
			 fftw_real tmp266;
			 fftw_real tmp263;
			 fftw_real tmp265;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp264 = c_re(inout[29 * iostride]);
			 tmp266 = c_im(inout[29 * iostride]);
			 tmp263 = c_re(W[28]);
			 tmp265 = c_im(W[28]);
			 tmp267 = (tmp263 * tmp264) + (tmp265 * tmp266);
			 tmp494 = (tmp263 * tmp266) - (tmp265 * tmp264);
		    }
		    tmp268 = tmp262 + tmp267;
		    tmp498 = tmp262 - tmp267;
		    tmp495 = tmp493 - tmp494;
		    tmp843 = tmp493 + tmp494;
	       }
	       {
		    fftw_real tmp257;
		    fftw_real tmp280;
		    fftw_real tmp837;
		    fftw_real tmp840;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp257 = tmp245 + tmp256;
		    tmp280 = tmp268 + tmp279;
		    tmp281 = tmp257 + tmp280;
		    tmp1012 = tmp257 - tmp280;
		    tmp837 = tmp245 - tmp256;
		    tmp840 = tmp838 - tmp839;
		    tmp841 = tmp837 - tmp840;
		    tmp854 = tmp837 + tmp840;
	       }
	       {
		    fftw_real tmp968;
		    fftw_real tmp969;
		    fftw_real tmp842;
		    fftw_real tmp845;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp968 = tmp838 + tmp839;
		    tmp969 = tmp843 + tmp844;
		    tmp970 = tmp968 + tmp969;
		    tmp1015 = tmp969 - tmp968;
		    tmp842 = tmp268 - tmp279;
		    tmp845 = tmp843 - tmp844;
		    tmp846 = tmp842 + tmp845;
		    tmp855 = tmp845 - tmp842;
	       }
	       {
		    fftw_real tmp497;
		    fftw_real tmp502;
		    fftw_real tmp687;
		    fftw_real tmp688;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp497 = tmp495 + tmp496;
		    tmp502 = tmp498 - tmp501;
		    tmp503 = (K923879532 * tmp497) - (K382683432 * tmp502);
		    tmp527 = (K382683432 * tmp497) + (K923879532 * tmp502);
		    tmp687 = tmp495 - tmp496;
		    tmp688 = tmp498 + tmp501;
		    tmp689 = (K382683432 * tmp687) - (K923879532 * tmp688);
		    tmp699 = (K923879532 * tmp687) + (K382683432 * tmp688);
	       }
	       {
		    fftw_real tmp508;
		    fftw_real tmp513;
		    fftw_real tmp690;
		    fftw_real tmp691;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp508 = tmp506 + tmp507;
		    tmp513 = tmp509 - tmp512;
		    tmp514 = (K923879532 * tmp508) + (K382683432 * tmp513);
		    tmp526 = (K923879532 * tmp513) - (K382683432 * tmp508);
		    tmp690 = tmp506 - tmp507;
		    tmp691 = tmp509 + tmp512;
		    tmp692 = (K382683432 * tmp690) + (K923879532 * tmp691);
		    tmp698 = (K382683432 * tmp691) - (K923879532 * tmp690);
	       }
	  }
	  {
	       fftw_real tmp91;
	       fftw_real tmp955;
	       fftw_real tmp990;
	       fftw_real tmp1039;
	       fftw_real tmp1049;
	       fftw_real tmp1055;
	       fftw_real tmp186;
	       fftw_real tmp1054;
	       fftw_real tmp972;
	       fftw_real tmp985;
	       fftw_real tmp378;
	       fftw_real tmp1051;
	       fftw_real tmp981;
	       fftw_real tmp984;
	       fftw_real tmp962;
	       fftw_real tmp1040;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp43;
		    fftw_real tmp90;
		    fftw_real tmp988;
		    fftw_real tmp989;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp43 = tmp19 + tmp42;
		    tmp90 = tmp66 + tmp89;
		    tmp91 = tmp43 + tmp90;
		    tmp955 = tmp43 - tmp90;
		    tmp988 = tmp976 + tmp979;
		    tmp989 = tmp967 + tmp970;
		    tmp990 = tmp988 - tmp989;
		    tmp1039 = tmp989 + tmp988;
	       }
	       {
		    fftw_real tmp1041;
		    fftw_real tmp1048;
		    fftw_real tmp138;
		    fftw_real tmp185;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp1041 = tmp993 + tmp992;
		    tmp1048 = tmp1042 + tmp1047;
		    tmp1049 = tmp1041 + tmp1048;
		    tmp1055 = tmp1048 - tmp1041;
		    tmp138 = tmp114 + tmp137;
		    tmp185 = tmp161 + tmp184;
		    tmp186 = tmp138 + tmp185;
		    tmp1054 = tmp138 - tmp185;
	       }
	       {
		    fftw_real tmp964;
		    fftw_real tmp971;
		    fftw_real tmp282;
		    fftw_real tmp377;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp964 = tmp234 - tmp281;
		    tmp971 = tmp967 - tmp970;
		    tmp972 = tmp964 - tmp971;
		    tmp985 = tmp964 + tmp971;
		    tmp282 = tmp234 + tmp281;
		    tmp377 = tmp329 + tmp376;
		    tmp378 = tmp282 + tmp377;
		    tmp1051 = tmp282 - tmp377;
	       }
	       {
		    fftw_real tmp973;
		    fftw_real tmp980;
		    fftw_real tmp958;
		    fftw_real tmp961;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp973 = tmp329 - tmp376;
		    tmp980 = tmp976 - tmp979;
		    tmp981 = tmp973 + tmp980;
		    tmp984 = tmp980 - tmp973;
		    tmp958 = tmp956 + tmp957;
		    tmp961 = tmp959 + tmp960;
		    tmp962 = tmp958 - tmp961;
		    tmp1040 = tmp961 + tmp958;
	       }
	       {
		    fftw_real tmp187;
		    fftw_real tmp987;
		    fftw_real tmp1050;
		    fftw_real tmp1052;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp187 = tmp91 + tmp186;
		    c_re(inout[32 * iostride]) = tmp187 - tmp378;
		    c_re(inout[0]) = tmp187 + tmp378;
		    tmp987 = tmp91 - tmp186;
		    c_re(inout[48 * iostride]) = tmp987 - tmp990;
		    c_re(inout[16 * iostride]) = tmp987 + tmp990;
		    {
			 fftw_real tmp963;
			 fftw_real tmp982;
			 fftw_real tmp1057;
			 fftw_real tmp1058;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp963 = tmp955 + tmp962;
			 tmp982 = K707106781 * (tmp972 + tmp981);
			 c_re(inout[40 * iostride]) = tmp963 - tmp982;
			 c_re(inout[8 * iostride]) = tmp963 + tmp982;
			 tmp1057 = K707106781 * (tmp972 - tmp981);
			 tmp1058 = tmp1055 - tmp1054;
			 c_im(inout[24 * iostride]) = tmp1057 + tmp1058;
			 c_im(inout[56 * iostride]) = tmp1058 - tmp1057;
		    }
		    tmp1050 = tmp1040 + tmp1049;
		    c_im(inout[0]) = tmp1039 + tmp1050;
		    c_im(inout[32 * iostride]) = tmp1050 - tmp1039;
		    tmp1052 = tmp1049 - tmp1040;
		    c_im(inout[16 * iostride]) = tmp1051 + tmp1052;
		    c_im(inout[48 * iostride]) = tmp1052 - tmp1051;
		    {
			 fftw_real tmp1053;
			 fftw_real tmp1056;
			 fftw_real tmp983;
			 fftw_real tmp986;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp1053 = K707106781 * (tmp985 + tmp984);
			 tmp1056 = tmp1054 + tmp1055;
			 c_im(inout[8 * iostride]) = tmp1053 + tmp1056;
			 c_im(inout[40 * iostride]) = tmp1056 - tmp1053;
			 tmp983 = tmp955 - tmp962;
			 tmp986 = K707106781 * (tmp984 - tmp985);
			 c_re(inout[56 * iostride]) = tmp983 - tmp986;
			 c_re(inout[24 * iostride]) = tmp983 + tmp986;
		    }
	       }
	  }
	  {
	       fftw_real tmp995;
	       fftw_real tmp1023;
	       fftw_real tmp1026;
	       fftw_real tmp1068;
	       fftw_real tmp1002;
	       fftw_real tmp1060;
	       fftw_real tmp1063;
	       fftw_real tmp1069;
	       fftw_real tmp1010;
	       fftw_real tmp1021;
	       fftw_real tmp1030;
	       fftw_real tmp1037;
	       fftw_real tmp1017;
	       fftw_real tmp1020;
	       fftw_real tmp1033;
	       fftw_real tmp1036;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp991;
		    fftw_real tmp994;
		    fftw_real tmp1024;
		    fftw_real tmp1025;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp991 = tmp19 - tmp42;
		    tmp994 = tmp992 - tmp993;
		    tmp995 = tmp991 - tmp994;
		    tmp1023 = tmp991 + tmp994;
		    tmp1024 = tmp999 - tmp1000;
		    tmp1025 = tmp997 + tmp996;
		    tmp1026 = K707106781 * (tmp1024 + tmp1025);
		    tmp1068 = K707106781 * (tmp1024 - tmp1025);
	       }
	       {
		    fftw_real tmp998;
		    fftw_real tmp1001;
		    fftw_real tmp1061;
		    fftw_real tmp1062;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp998 = tmp996 - tmp997;
		    tmp1001 = tmp999 + tmp1000;
		    tmp1002 = K707106781 * (tmp998 - tmp1001);
		    tmp1060 = K707106781 * (tmp1001 + tmp998);
		    tmp1061 = tmp66 - tmp89;
		    tmp1062 = tmp1047 - tmp1042;
		    tmp1063 = tmp1061 + tmp1062;
		    tmp1069 = tmp1062 - tmp1061;
	       }
	       {
		    fftw_real tmp1006;
		    fftw_real tmp1009;
		    fftw_real tmp1028;
		    fftw_real tmp1029;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp1006 = tmp1004 - tmp1005;
		    tmp1009 = tmp1007 - tmp1008;
		    tmp1010 = (K382683432 * tmp1006) - (K923879532 * tmp1009);
		    tmp1021 = (K923879532 * tmp1006) + (K382683432 * tmp1009);
		    tmp1028 = tmp1004 + tmp1005;
		    tmp1029 = tmp1007 + tmp1008;
		    tmp1030 = (K923879532 * tmp1028) - (K382683432 * tmp1029);
		    tmp1037 = (K382683432 * tmp1028) + (K923879532 * tmp1029);
	       }
	       {
		    fftw_real tmp1013;
		    fftw_real tmp1016;
		    fftw_real tmp1031;
		    fftw_real tmp1032;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp1013 = tmp1011 - tmp1012;
		    tmp1016 = tmp1014 - tmp1015;
		    tmp1017 = (K382683432 * tmp1013) + (K923879532 * tmp1016);
		    tmp1020 = (K382683432 * tmp1016) - (K923879532 * tmp1013);
		    tmp1031 = tmp1011 + tmp1012;
		    tmp1032 = tmp1014 + tmp1015;
		    tmp1033 = (K923879532 * tmp1031) + (K382683432 * tmp1032);
		    tmp1036 = (K923879532 * tmp1032) - (K382683432 * tmp1031);
	       }
	       {
		    fftw_real tmp1003;
		    fftw_real tmp1018;
		    fftw_real tmp1019;
		    fftw_real tmp1022;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp1003 = tmp995 - tmp1002;
		    tmp1018 = tmp1010 - tmp1017;
		    c_re(inout[60 * iostride]) = tmp1003 - tmp1018;
		    c_re(inout[28 * iostride]) = tmp1003 + tmp1018;
		    tmp1019 = tmp995 + tmp1002;
		    tmp1022 = tmp1020 + tmp1021;
		    c_re(inout[44 * iostride]) = tmp1019 - tmp1022;
		    c_re(inout[12 * iostride]) = tmp1019 + tmp1022;
	       }
	       {
		    fftw_real tmp1071;
		    fftw_real tmp1072;
		    fftw_real tmp1067;
		    fftw_real tmp1070;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp1071 = tmp1020 - tmp1021;
		    tmp1072 = tmp1069 - tmp1068;
		    c_im(inout[28 * iostride]) = tmp1071 + tmp1072;
		    c_im(inout[60 * iostride]) = tmp1072 - tmp1071;
		    tmp1067 = tmp1017 + tmp1010;
		    tmp1070 = tmp1068 + tmp1069;
		    c_im(inout[12 * iostride]) = tmp1067 + tmp1070;
		    c_im(inout[44 * iostride]) = tmp1070 - tmp1067;
	       }
	       {
		    fftw_real tmp1027;
		    fftw_real tmp1034;
		    fftw_real tmp1035;
		    fftw_real tmp1038;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp1027 = tmp1023 - tmp1026;
		    tmp1034 = tmp1030 - tmp1033;
		    c_re(inout[52 * iostride]) = tmp1027 - tmp1034;
		    c_re(inout[20 * iostride]) = tmp1027 + tmp1034;
		    tmp1035 = tmp1023 + tmp1026;
		    tmp1038 = tmp1036 + tmp1037;
		    c_re(inout[36 * iostride]) = tmp1035 - tmp1038;
		    c_re(inout[4 * iostride]) = tmp1035 + tmp1038;
	       }
	       {
		    fftw_real tmp1065;
		    fftw_real tmp1066;
		    fftw_real tmp1059;
		    fftw_real tmp1064;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp1065 = tmp1036 - tmp1037;
		    tmp1066 = tmp1063 - tmp1060;
		    c_im(inout[20 * iostride]) = tmp1065 + tmp1066;
		    c_im(inout[52 * iostride]) = tmp1066 - tmp1065;
		    tmp1059 = tmp1033 + tmp1030;
		    tmp1064 = tmp1060 + tmp1063;
		    c_im(inout[4 * iostride]) = tmp1059 + tmp1064;
		    c_im(inout[36 * iostride]) = tmp1064 - tmp1059;
	       }
	  }
	  {
	       fftw_real tmp419;
	       fftw_real tmp591;
	       fftw_real tmp1125;
	       fftw_real tmp1131;
	       fftw_real tmp474;
	       fftw_real tmp1122;
	       fftw_real tmp594;
	       fftw_real tmp1130;
	       fftw_real tmp530;
	       fftw_real tmp589;
	       fftw_real tmp598;
	       fftw_real tmp605;
	       fftw_real tmp585;
	       fftw_real tmp588;
	       fftw_real tmp601;
	       fftw_real tmp604;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp395;
		    fftw_real tmp418;
		    fftw_real tmp1123;
		    fftw_real tmp1124;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp395 = tmp383 - tmp394;
		    tmp418 = tmp406 - tmp417;
		    tmp419 = tmp395 - tmp418;
		    tmp591 = tmp395 + tmp418;
		    tmp1123 = tmp608 - tmp609;
		    tmp1124 = tmp1109 - tmp1106;
		    tmp1125 = tmp1123 + tmp1124;
		    tmp1131 = tmp1124 - tmp1123;
	       }
	       {
		    fftw_real tmp446;
		    fftw_real tmp593;
		    fftw_real tmp473;
		    fftw_real tmp592;
		    ASSERT_ALIGNED_DOUBLE;
		    {
			 fftw_real tmp436;
			 fftw_real tmp445;
			 fftw_real tmp463;
			 fftw_real tmp472;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp436 = tmp424 - tmp435;
			 tmp445 = tmp441 - tmp444;
			 tmp446 = (K555570233 * tmp436) - (K831469612 * tmp445);
			 tmp593 = (K555570233 * tmp445) + (K831469612 * tmp436);
			 tmp463 = tmp451 - tmp462;
			 tmp472 = tmp468 - tmp471;
			 tmp473 = (K831469612 * tmp463) + (K555570233 * tmp472);
			 tmp592 = (K555570233 * tmp463) - (K831469612 * tmp472);
		    }
		    tmp474 = tmp446 - tmp473;
		    tmp1122 = tmp473 + tmp446;
		    tmp594 = tmp592 + tmp593;
		    tmp1130 = tmp592 - tmp593;
	       }
	       {
		    fftw_real tmp516;
		    fftw_real tmp596;
		    fftw_real tmp529;
		    fftw_real tmp597;
		    ASSERT_ALIGNED_DOUBLE;
		    {
			 fftw_real tmp492;
			 fftw_real tmp515;
			 fftw_real tmp525;
			 fftw_real tmp528;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp492 = tmp480 - tmp491;
			 tmp515 = tmp503 - tmp514;
			 tmp516 = tmp492 - tmp515;
			 tmp596 = tmp492 + tmp515;
			 tmp525 = tmp521 - tmp524;
			 tmp528 = tmp526 - tmp527;
			 tmp529 = tmp525 - tmp528;
			 tmp597 = tmp525 + tmp528;
		    }
		    tmp530 = (K290284677 * tmp516) - (K956940335 * tmp529);
		    tmp589 = (K956940335 * tmp516) + (K290284677 * tmp529);
		    tmp598 = (K881921264 * tmp596) - (K471396736 * tmp597);
		    tmp605 = (K471396736 * tmp596) + (K881921264 * tmp597);
	       }
	       {
		    fftw_real tmp571;
		    fftw_real tmp599;
		    fftw_real tmp584;
		    fftw_real tmp600;
		    ASSERT_ALIGNED_DOUBLE;
		    {
			 fftw_real tmp547;
			 fftw_real tmp570;
			 fftw_real tmp580;
			 fftw_real tmp583;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp547 = tmp535 - tmp546;
			 tmp570 = tmp558 - tmp569;
			 tmp571 = tmp547 - tmp570;
			 tmp599 = tmp547 + tmp570;
			 tmp580 = tmp576 - tmp579;
			 tmp583 = tmp581 - tmp582;
			 tmp584 = tmp580 - tmp583;
			 tmp600 = tmp580 + tmp583;
		    }
		    tmp585 = (K290284677 * tmp571) + (K956940335 * tmp584);
		    tmp588 = (K290284677 * tmp584) - (K956940335 * tmp571);
		    tmp601 = (K881921264 * tmp599) + (K471396736 * tmp600);
		    tmp604 = (K881921264 * tmp600) - (K471396736 * tmp599);
	       }
	       {
		    fftw_real tmp475;
		    fftw_real tmp586;
		    fftw_real tmp587;
		    fftw_real tmp590;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp475 = tmp419 + tmp474;
		    tmp586 = tmp530 + tmp585;
		    c_re(inout[45 * iostride]) = tmp475 - tmp586;
		    c_re(inout[13 * iostride]) = tmp475 + tmp586;
		    tmp587 = tmp419 - tmp474;
		    tmp590 = tmp588 - tmp589;
		    c_re(inout[61 * iostride]) = tmp587 - tmp590;
		    c_re(inout[29 * iostride]) = tmp587 + tmp590;
	       }
	       {
		    fftw_real tmp1129;
		    fftw_real tmp1132;
		    fftw_real tmp1133;
		    fftw_real tmp1134;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp1129 = tmp589 + tmp588;
		    tmp1132 = tmp1130 + tmp1131;
		    c_im(inout[13 * iostride]) = tmp1129 + tmp1132;
		    c_im(inout[45 * iostride]) = tmp1132 - tmp1129;
		    tmp1133 = tmp530 - tmp585;
		    tmp1134 = tmp1131 - tmp1130;
		    c_im(inout[29 * iostride]) = tmp1133 + tmp1134;
		    c_im(inout[61 * iostride]) = tmp1134 - tmp1133;
	       }
	       {
		    fftw_real tmp595;
		    fftw_real tmp602;
		    fftw_real tmp603;
		    fftw_real tmp606;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp595 = tmp591 + tmp594;
		    tmp602 = tmp598 + tmp601;
		    c_re(inout[37 * iostride]) = tmp595 - tmp602;
		    c_re(inout[5 * iostride]) = tmp595 + tmp602;
		    tmp603 = tmp591 - tmp594;
		    tmp606 = tmp604 - tmp605;
		    c_re(inout[53 * iostride]) = tmp603 - tmp606;
		    c_re(inout[21 * iostride]) = tmp603 + tmp606;
	       }
	       {
		    fftw_real tmp1121;
		    fftw_real tmp1126;
		    fftw_real tmp1127;
		    fftw_real tmp1128;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp1121 = tmp605 + tmp604;
		    tmp1126 = tmp1122 + tmp1125;
		    c_im(inout[5 * iostride]) = tmp1121 + tmp1126;
		    c_im(inout[37 * iostride]) = tmp1126 - tmp1121;
		    tmp1127 = tmp598 - tmp601;
		    tmp1128 = tmp1125 - tmp1122;
		    c_im(inout[21 * iostride]) = tmp1127 + tmp1128;
		    c_im(inout[53 * iostride]) = tmp1128 - tmp1127;
	       }
	  }
	  {
	       fftw_real tmp611;
	       fftw_real tmp639;
	       fftw_real tmp1111;
	       fftw_real tmp1117;
	       fftw_real tmp618;
	       fftw_real tmp1104;
	       fftw_real tmp642;
	       fftw_real tmp1116;
	       fftw_real tmp626;
	       fftw_real tmp637;
	       fftw_real tmp646;
	       fftw_real tmp653;
	       fftw_real tmp633;
	       fftw_real tmp636;
	       fftw_real tmp649;
	       fftw_real tmp652;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp607;
		    fftw_real tmp610;
		    fftw_real tmp1105;
		    fftw_real tmp1110;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp607 = tmp383 + tmp394;
		    tmp610 = tmp608 + tmp609;
		    tmp611 = tmp607 - tmp610;
		    tmp639 = tmp607 + tmp610;
		    tmp1105 = tmp417 + tmp406;
		    tmp1110 = tmp1106 + tmp1109;
		    tmp1111 = tmp1105 + tmp1110;
		    tmp1117 = tmp1110 - tmp1105;
	       }
	       {
		    fftw_real tmp614;
		    fftw_real tmp641;
		    fftw_real tmp617;
		    fftw_real tmp640;
		    ASSERT_ALIGNED_DOUBLE;
		    {
			 fftw_real tmp612;
			 fftw_real tmp613;
			 fftw_real tmp615;
			 fftw_real tmp616;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp612 = tmp424 + tmp435;
			 tmp613 = tmp441 + tmp444;
			 tmp614 = (K980785280 * tmp612) - (K195090322 * tmp613);
			 tmp641 = (K980785280 * tmp613) + (K195090322 * tmp612);
			 tmp615 = tmp451 + tmp462;
			 tmp616 = tmp468 + tmp471;
			 tmp617 = (K195090322 * tmp615) + (K980785280 * tmp616);
			 tmp640 = (K980785280 * tmp615) - (K195090322 * tmp616);
		    }
		    tmp618 = tmp614 - tmp617;
		    tmp1104 = tmp617 + tmp614;
		    tmp642 = tmp640 + tmp641;
		    tmp1116 = tmp640 - tmp641;
	       }
	       {
		    fftw_real tmp622;
		    fftw_real tmp644;
		    fftw_real tmp625;
		    fftw_real tmp645;
		    ASSERT_ALIGNED_DOUBLE;
		    {
			 fftw_real tmp620;
			 fftw_real tmp621;
			 fftw_real tmp623;
			 fftw_real tmp624;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp620 = tmp576 + tmp579;
			 tmp621 = tmp569 + tmp558;
			 tmp622 = tmp620 - tmp621;
			 tmp644 = tmp620 + tmp621;
			 tmp623 = tmp535 + tmp546;
			 tmp624 = tmp581 + tmp582;
			 tmp625 = tmp623 - tmp624;
			 tmp645 = tmp623 + tmp624;
		    }
		    tmp626 = (K634393284 * tmp622) - (K773010453 * tmp625);
		    tmp637 = (K773010453 * tmp622) + (K634393284 * tmp625);
		    tmp646 = (K995184726 * tmp644) - (K098017140 * tmp645);
		    tmp653 = (K098017140 * tmp644) + (K995184726 * tmp645);
	       }
	       {
		    fftw_real tmp629;
		    fftw_real tmp647;
		    fftw_real tmp632;
		    fftw_real tmp648;
		    ASSERT_ALIGNED_DOUBLE;
		    {
			 fftw_real tmp627;
			 fftw_real tmp628;
			 fftw_real tmp630;
			 fftw_real tmp631;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp627 = tmp521 + tmp524;
			 tmp628 = tmp514 + tmp503;
			 tmp629 = tmp627 - tmp628;
			 tmp647 = tmp627 + tmp628;
			 tmp630 = tmp480 + tmp491;
			 tmp631 = tmp526 + tmp527;
			 tmp632 = tmp630 - tmp631;
			 tmp648 = tmp630 + tmp631;
		    }
		    tmp633 = (K634393284 * tmp629) + (K773010453 * tmp632);
		    tmp636 = (K634393284 * tmp632) - (K773010453 * tmp629);
		    tmp649 = (K995184726 * tmp647) + (K098017140 * tmp648);
		    tmp652 = (K995184726 * tmp648) - (K098017140 * tmp647);
	       }
	       {
		    fftw_real tmp619;
		    fftw_real tmp634;
		    fftw_real tmp635;
		    fftw_real tmp638;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp619 = tmp611 - tmp618;
		    tmp634 = tmp626 - tmp633;
		    c_re(inout[57 * iostride]) = tmp619 - tmp634;
		    c_re(inout[25 * iostride]) = tmp619 + tmp634;
		    tmp635 = tmp611 + tmp618;
		    tmp638 = tmp636 + tmp637;
		    c_re(inout[41 * iostride]) = tmp635 - tmp638;
		    c_re(inout[9 * iostride]) = tmp635 + tmp638;
	       }
	       {
		    fftw_real tmp1119;
		    fftw_real tmp1120;
		    fftw_real tmp1115;
		    fftw_real tmp1118;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp1119 = tmp636 - tmp637;
		    tmp1120 = tmp1117 - tmp1116;
		    c_im(inout[25 * iostride]) = tmp1119 + tmp1120;
		    c_im(inout[57 * iostride]) = tmp1120 - tmp1119;
		    tmp1115 = tmp633 + tmp626;
		    tmp1118 = tmp1116 + tmp1117;
		    c_im(inout[9 * iostride]) = tmp1115 + tmp1118;
		    c_im(inout[41 * iostride]) = tmp1118 - tmp1115;
	       }
	       {
		    fftw_real tmp643;
		    fftw_real tmp650;
		    fftw_real tmp651;
		    fftw_real tmp654;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp643 = tmp639 - tmp642;
		    tmp650 = tmp646 - tmp649;
		    c_re(inout[49 * iostride]) = tmp643 - tmp650;
		    c_re(inout[17 * iostride]) = tmp643 + tmp650;
		    tmp651 = tmp639 + tmp642;
		    tmp654 = tmp652 + tmp653;
		    c_re(inout[33 * iostride]) = tmp651 - tmp654;
		    c_re(inout[iostride]) = tmp651 + tmp654;
	       }
	       {
		    fftw_real tmp1113;
		    fftw_real tmp1114;
		    fftw_real tmp1103;
		    fftw_real tmp1112;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp1113 = tmp652 - tmp653;
		    tmp1114 = tmp1111 - tmp1104;
		    c_im(inout[17 * iostride]) = tmp1113 + tmp1114;
		    c_im(inout[49 * iostride]) = tmp1114 - tmp1113;
		    tmp1103 = tmp649 + tmp646;
		    tmp1112 = tmp1104 + tmp1111;
		    c_im(inout[iostride]) = tmp1103 + tmp1112;
		    c_im(inout[33 * iostride]) = tmp1112 - tmp1103;
	       }
	  }
	  {
	       fftw_real tmp807;
	       fftw_real tmp891;
	       fftw_real tmp830;
	       fftw_real tmp1074;
	       fftw_real tmp1079;
	       fftw_real tmp1085;
	       fftw_real tmp894;
	       fftw_real tmp1084;
	       fftw_real tmp885;
	       fftw_real tmp888;
	       fftw_real tmp901;
	       fftw_real tmp904;
	       fftw_real tmp858;
	       fftw_real tmp889;
	       fftw_real tmp898;
	       fftw_real tmp905;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp795;
		    fftw_real tmp806;
		    fftw_real tmp892;
		    fftw_real tmp893;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp795 = tmp791 + tmp794;
		    tmp806 = K707106781 * (tmp800 + tmp805);
		    tmp807 = tmp795 - tmp806;
		    tmp891 = tmp795 + tmp806;
		    {
			 fftw_real tmp818;
			 fftw_real tmp829;
			 fftw_real tmp1075;
			 fftw_real tmp1078;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp818 = (K923879532 * tmp812) - (K382683432 * tmp817);
			 tmp829 = (K923879532 * tmp823) + (K382683432 * tmp828);
			 tmp830 = tmp818 - tmp829;
			 tmp1074 = tmp829 + tmp818;
			 tmp1075 = K707106781 * (tmp909 + tmp908);
			 tmp1078 = tmp1076 + tmp1077;
			 tmp1079 = tmp1075 + tmp1078;
			 tmp1085 = tmp1078 - tmp1075;
		    }
		    tmp892 = (K923879532 * tmp828) - (K382683432 * tmp823);
		    tmp893 = (K382683432 * tmp812) + (K923879532 * tmp817);
		    tmp894 = tmp892 + tmp893;
		    tmp1084 = tmp892 - tmp893;
		    {
			 fftw_real tmp875;
			 fftw_real tmp899;
			 fftw_real tmp884;
			 fftw_real tmp900;
			 fftw_real tmp874;
			 fftw_real tmp883;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp874 = K707106781 * (tmp868 + tmp873);
			 tmp875 = tmp863 - tmp874;
			 tmp899 = tmp863 + tmp874;
			 tmp883 = K707106781 * (tmp881 + tmp882);
			 tmp884 = tmp880 - tmp883;
			 tmp900 = tmp880 + tmp883;
			 tmp885 = (K555570233 * tmp875) + (K831469612 * tmp884);
			 tmp888 = (K555570233 * tmp884) - (K831469612 * tmp875);
			 tmp901 = (K980785280 * tmp899) + (K195090322 * tmp900);
			 tmp904 = (K980785280 * tmp900) - (K195090322 * tmp899);
		    }
		    {
			 fftw_real tmp848;
			 fftw_real tmp896;
			 fftw_real tmp857;
			 fftw_real tmp897;
			 fftw_real tmp847;
			 fftw_real tmp856;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp847 = K707106781 * (tmp841 + tmp846);
			 tmp848 = tmp836 - tmp847;
			 tmp896 = tmp836 + tmp847;
			 tmp856 = K707106781 * (tmp854 + tmp855);
			 tmp857 = tmp853 - tmp856;
			 tmp897 = tmp853 + tmp856;
			 tmp858 = (K555570233 * tmp848) - (K831469612 * tmp857);
			 tmp889 = (K831469612 * tmp848) + (K555570233 * tmp857);
			 tmp898 = (K980785280 * tmp896) - (K195090322 * tmp897);
			 tmp905 = (K195090322 * tmp896) + (K980785280 * tmp897);
		    }
	       }
	       {
		    fftw_real tmp831;
		    fftw_real tmp886;
		    fftw_real tmp887;
		    fftw_real tmp890;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp831 = tmp807 + tmp830;
		    tmp886 = tmp858 + tmp885;
		    c_re(inout[42 * iostride]) = tmp831 - tmp886;
		    c_re(inout[10 * iostride]) = tmp831 + tmp886;
		    tmp887 = tmp807 - tmp830;
		    tmp890 = tmp888 - tmp889;
		    c_re(inout[58 * iostride]) = tmp887 - tmp890;
		    c_re(inout[26 * iostride]) = tmp887 + tmp890;
	       }
	       {
		    fftw_real tmp1083;
		    fftw_real tmp1086;
		    fftw_real tmp1087;
		    fftw_real tmp1088;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp1083 = tmp889 + tmp888;
		    tmp1086 = tmp1084 + tmp1085;
		    c_im(inout[10 * iostride]) = tmp1083 + tmp1086;
		    c_im(inout[42 * iostride]) = tmp1086 - tmp1083;
		    tmp1087 = tmp858 - tmp885;
		    tmp1088 = tmp1085 - tmp1084;
		    c_im(inout[26 * iostride]) = tmp1087 + tmp1088;
		    c_im(inout[58 * iostride]) = tmp1088 - tmp1087;
	       }
	       {
		    fftw_real tmp895;
		    fftw_real tmp902;
		    fftw_real tmp903;
		    fftw_real tmp906;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp895 = tmp891 + tmp894;
		    tmp902 = tmp898 + tmp901;
		    c_re(inout[34 * iostride]) = tmp895 - tmp902;
		    c_re(inout[2 * iostride]) = tmp895 + tmp902;
		    tmp903 = tmp891 - tmp894;
		    tmp906 = tmp904 - tmp905;
		    c_re(inout[50 * iostride]) = tmp903 - tmp906;
		    c_re(inout[18 * iostride]) = tmp903 + tmp906;
	       }
	       {
		    fftw_real tmp1073;
		    fftw_real tmp1080;
		    fftw_real tmp1081;
		    fftw_real tmp1082;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp1073 = tmp905 + tmp904;
		    tmp1080 = tmp1074 + tmp1079;
		    c_im(inout[2 * iostride]) = tmp1073 + tmp1080;
		    c_im(inout[34 * iostride]) = tmp1080 - tmp1073;
		    tmp1081 = tmp898 - tmp901;
		    tmp1082 = tmp1079 - tmp1074;
		    c_im(inout[18 * iostride]) = tmp1081 + tmp1082;
		    c_im(inout[50 * iostride]) = tmp1082 - tmp1081;
	       }
	  }
	  {
	       fftw_real tmp911;
	       fftw_real tmp939;
	       fftw_real tmp918;
	       fftw_real tmp1090;
	       fftw_real tmp1093;
	       fftw_real tmp1099;
	       fftw_real tmp942;
	       fftw_real tmp1098;
	       fftw_real tmp933;
	       fftw_real tmp936;
	       fftw_real tmp949;
	       fftw_real tmp952;
	       fftw_real tmp926;
	       fftw_real tmp937;
	       fftw_real tmp946;
	       fftw_real tmp953;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp907;
		    fftw_real tmp910;
		    fftw_real tmp940;
		    fftw_real tmp941;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp907 = tmp791 - tmp794;
		    tmp910 = K707106781 * (tmp908 - tmp909);
		    tmp911 = tmp907 - tmp910;
		    tmp939 = tmp907 + tmp910;
		    {
			 fftw_real tmp914;
			 fftw_real tmp917;
			 fftw_real tmp1091;
			 fftw_real tmp1092;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp914 = (K382683432 * tmp912) - (K923879532 * tmp913);
			 tmp917 = (K382683432 * tmp915) + (K923879532 * tmp916);
			 tmp918 = tmp914 - tmp917;
			 tmp1090 = tmp917 + tmp914;
			 tmp1091 = K707106781 * (tmp800 - tmp805);
			 tmp1092 = tmp1077 - tmp1076;
			 tmp1093 = tmp1091 + tmp1092;
			 tmp1099 = tmp1092 - tmp1091;
		    }
		    tmp940 = (K382683432 * tmp916) - (K923879532 * tmp915);
		    tmp941 = (K923879532 * tmp912) + (K382683432 * tmp913);
		    tmp942 = tmp940 + tmp941;
		    tmp1098 = tmp940 - tmp941;
		    {
			 fftw_real tmp929;
			 fftw_real tmp947;
			 fftw_real tmp932;
			 fftw_real tmp948;
			 fftw_real tmp928;
			 fftw_real tmp931;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp928 = K707106781 * (tmp841 - tmp846);
			 tmp929 = tmp927 - tmp928;
			 tmp947 = tmp927 + tmp928;
			 tmp931 = K707106781 * (tmp855 - tmp854);
			 tmp932 = tmp930 - tmp931;
			 tmp948 = tmp930 + tmp931;
			 tmp933 = (K195090322 * tmp929) + (K980785280 * tmp932);
			 tmp936 = (K195090322 * tmp932) - (K980785280 * tmp929);
			 tmp949 = (K831469612 * tmp947) + (K555570233 * tmp948);
			 tmp952 = (K831469612 * tmp948) - (K555570233 * tmp947);
		    }
		    {
			 fftw_real tmp922;
			 fftw_real tmp944;
			 fftw_real tmp925;
			 fftw_real tmp945;
			 fftw_real tmp921;
			 fftw_real tmp924;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp921 = K707106781 * (tmp868 - tmp873);
			 tmp922 = tmp920 - tmp921;
			 tmp944 = tmp920 + tmp921;
			 tmp924 = K707106781 * (tmp882 - tmp881);
			 tmp925 = tmp923 - tmp924;
			 tmp945 = tmp923 + tmp924;
			 tmp926 = (K195090322 * tmp922) - (K980785280 * tmp925);
			 tmp937 = (K980785280 * tmp922) + (K195090322 * tmp925);
			 tmp946 = (K831469612 * tmp944) - (K555570233 * tmp945);
			 tmp953 = (K555570233 * tmp944) + (K831469612 * tmp945);
		    }
	       }
	       {
		    fftw_real tmp919;
		    fftw_real tmp934;
		    fftw_real tmp935;
		    fftw_real tmp938;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp919 = tmp911 - tmp918;
		    tmp934 = tmp926 - tmp933;
		    c_re(inout[62 * iostride]) = tmp919 - tmp934;
		    c_re(inout[30 * iostride]) = tmp919 + tmp934;
		    tmp935 = tmp911 + tmp918;
		    tmp938 = tmp936 + tmp937;
		    c_re(inout[46 * iostride]) = tmp935 - tmp938;
		    c_re(inout[14 * iostride]) = tmp935 + tmp938;
	       }
	       {
		    fftw_real tmp1101;
		    fftw_real tmp1102;
		    fftw_real tmp1097;
		    fftw_real tmp1100;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp1101 = tmp936 - tmp937;
		    tmp1102 = tmp1099 - tmp1098;
		    c_im(inout[30 * iostride]) = tmp1101 + tmp1102;
		    c_im(inout[62 * iostride]) = tmp1102 - tmp1101;
		    tmp1097 = tmp933 + tmp926;
		    tmp1100 = tmp1098 + tmp1099;
		    c_im(inout[14 * iostride]) = tmp1097 + tmp1100;
		    c_im(inout[46 * iostride]) = tmp1100 - tmp1097;
	       }
	       {
		    fftw_real tmp943;
		    fftw_real tmp950;
		    fftw_real tmp951;
		    fftw_real tmp954;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp943 = tmp939 - tmp942;
		    tmp950 = tmp946 - tmp949;
		    c_re(inout[54 * iostride]) = tmp943 - tmp950;
		    c_re(inout[22 * iostride]) = tmp943 + tmp950;
		    tmp951 = tmp939 + tmp942;
		    tmp954 = tmp952 + tmp953;
		    c_re(inout[38 * iostride]) = tmp951 - tmp954;
		    c_re(inout[6 * iostride]) = tmp951 + tmp954;
	       }
	       {
		    fftw_real tmp1095;
		    fftw_real tmp1096;
		    fftw_real tmp1089;
		    fftw_real tmp1094;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp1095 = tmp952 - tmp953;
		    tmp1096 = tmp1093 - tmp1090;
		    c_im(inout[22 * iostride]) = tmp1095 + tmp1096;
		    c_im(inout[54 * iostride]) = tmp1096 - tmp1095;
		    tmp1089 = tmp949 + tmp946;
		    tmp1094 = tmp1090 + tmp1093;
		    c_im(inout[6 * iostride]) = tmp1089 + tmp1094;
		    c_im(inout[38 * iostride]) = tmp1094 - tmp1089;
	       }
	  }
	  {
	       fftw_real tmp667;
	       fftw_real tmp727;
	       fftw_real tmp1155;
	       fftw_real tmp1161;
	       fftw_real tmp682;
	       fftw_real tmp1152;
	       fftw_real tmp730;
	       fftw_real tmp1160;
	       fftw_real tmp702;
	       fftw_real tmp725;
	       fftw_real tmp734;
	       fftw_real tmp741;
	       fftw_real tmp721;
	       fftw_real tmp724;
	       fftw_real tmp737;
	       fftw_real tmp740;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp659;
		    fftw_real tmp666;
		    fftw_real tmp1153;
		    fftw_real tmp1154;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp659 = tmp655 - tmp658;
		    tmp666 = tmp662 - tmp665;
		    tmp667 = tmp659 - tmp666;
		    tmp727 = tmp659 + tmp666;
		    tmp1153 = tmp744 - tmp745;
		    tmp1154 = tmp1139 - tmp1138;
		    tmp1155 = tmp1153 + tmp1154;
		    tmp1161 = tmp1154 - tmp1153;
	       }
	       {
		    fftw_real tmp674;
		    fftw_real tmp729;
		    fftw_real tmp681;
		    fftw_real tmp728;
		    ASSERT_ALIGNED_DOUBLE;
		    {
			 fftw_real tmp670;
			 fftw_real tmp673;
			 fftw_real tmp677;
			 fftw_real tmp680;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp670 = tmp668 - tmp669;
			 tmp673 = tmp671 - tmp672;
			 tmp674 = (K195090322 * tmp670) - (K980785280 * tmp673);
			 tmp729 = (K980785280 * tmp670) + (K195090322 * tmp673);
			 tmp677 = tmp675 - tmp676;
			 tmp680 = tmp678 - tmp679;
			 tmp681 = (K195090322 * tmp677) + (K980785280 * tmp680);
			 tmp728 = (K195090322 * tmp680) - (K980785280 * tmp677);
		    }
		    tmp682 = tmp674 - tmp681;
		    tmp1152 = tmp681 + tmp674;
		    tmp730 = tmp728 + tmp729;
		    tmp1160 = tmp728 - tmp729;
	       }
	       {
		    fftw_real tmp694;
		    fftw_real tmp732;
		    fftw_real tmp701;
		    fftw_real tmp733;
		    ASSERT_ALIGNED_DOUBLE;
		    {
			 fftw_real tmp686;
			 fftw_real tmp693;
			 fftw_real tmp697;
			 fftw_real tmp700;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp686 = tmp684 - tmp685;
			 tmp693 = tmp689 - tmp692;
			 tmp694 = tmp686 - tmp693;
			 tmp732 = tmp686 + tmp693;
			 tmp697 = tmp695 - tmp696;
			 tmp700 = tmp698 - tmp699;
			 tmp701 = tmp697 - tmp700;
			 tmp733 = tmp697 + tmp700;
		    }
		    tmp702 = (K098017140 * tmp694) - (K995184726 * tmp701);
		    tmp725 = (K995184726 * tmp694) + (K098017140 * tmp701);
		    tmp734 = (K773010453 * tmp732) - (K634393284 * tmp733);
		    tmp741 = (K634393284 * tmp732) + (K773010453 * tmp733);
	       }
	       {
		    fftw_real tmp713;
		    fftw_real tmp735;
		    fftw_real tmp720;
		    fftw_real tmp736;
		    ASSERT_ALIGNED_DOUBLE;
		    {
			 fftw_real tmp705;
			 fftw_real tmp712;
			 fftw_real tmp716;
			 fftw_real tmp719;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp705 = tmp703 - tmp704;
			 tmp712 = tmp708 - tmp711;
			 tmp713 = tmp705 - tmp712;
			 tmp735 = tmp705 + tmp712;
			 tmp716 = tmp714 - tmp715;
			 tmp719 = tmp717 - tmp718;
			 tmp720 = tmp716 - tmp719;
			 tmp736 = tmp716 + tmp719;
		    }
		    tmp721 = (K098017140 * tmp713) + (K995184726 * tmp720);
		    tmp724 = (K098017140 * tmp720) - (K995184726 * tmp713);
		    tmp737 = (K773010453 * tmp735) + (K634393284 * tmp736);
		    tmp740 = (K773010453 * tmp736) - (K634393284 * tmp735);
	       }
	       {
		    fftw_real tmp683;
		    fftw_real tmp722;
		    fftw_real tmp723;
		    fftw_real tmp726;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp683 = tmp667 + tmp682;
		    tmp722 = tmp702 + tmp721;
		    c_re(inout[47 * iostride]) = tmp683 - tmp722;
		    c_re(inout[15 * iostride]) = tmp683 + tmp722;
		    tmp723 = tmp667 - tmp682;
		    tmp726 = tmp724 - tmp725;
		    c_re(inout[63 * iostride]) = tmp723 - tmp726;
		    c_re(inout[31 * iostride]) = tmp723 + tmp726;
	       }
	       {
		    fftw_real tmp1159;
		    fftw_real tmp1162;
		    fftw_real tmp1163;
		    fftw_real tmp1164;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp1159 = tmp725 + tmp724;
		    tmp1162 = tmp1160 + tmp1161;
		    c_im(inout[15 * iostride]) = tmp1159 + tmp1162;
		    c_im(inout[47 * iostride]) = tmp1162 - tmp1159;
		    tmp1163 = tmp702 - tmp721;
		    tmp1164 = tmp1161 - tmp1160;
		    c_im(inout[31 * iostride]) = tmp1163 + tmp1164;
		    c_im(inout[63 * iostride]) = tmp1164 - tmp1163;
	       }
	       {
		    fftw_real tmp731;
		    fftw_real tmp738;
		    fftw_real tmp739;
		    fftw_real tmp742;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp731 = tmp727 + tmp730;
		    tmp738 = tmp734 + tmp737;
		    c_re(inout[39 * iostride]) = tmp731 - tmp738;
		    c_re(inout[7 * iostride]) = tmp731 + tmp738;
		    tmp739 = tmp727 - tmp730;
		    tmp742 = tmp740 - tmp741;
		    c_re(inout[55 * iostride]) = tmp739 - tmp742;
		    c_re(inout[23 * iostride]) = tmp739 + tmp742;
	       }
	       {
		    fftw_real tmp1151;
		    fftw_real tmp1156;
		    fftw_real tmp1157;
		    fftw_real tmp1158;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp1151 = tmp741 + tmp740;
		    tmp1156 = tmp1152 + tmp1155;
		    c_im(inout[7 * iostride]) = tmp1151 + tmp1156;
		    c_im(inout[39 * iostride]) = tmp1156 - tmp1151;
		    tmp1157 = tmp734 - tmp737;
		    tmp1158 = tmp1155 - tmp1152;
		    c_im(inout[23 * iostride]) = tmp1157 + tmp1158;
		    c_im(inout[55 * iostride]) = tmp1158 - tmp1157;
	       }
	  }
	  {
	       fftw_real tmp747;
	       fftw_real tmp775;
	       fftw_real tmp1141;
	       fftw_real tmp1147;
	       fftw_real tmp754;
	       fftw_real tmp1136;
	       fftw_real tmp778;
	       fftw_real tmp1146;
	       fftw_real tmp762;
	       fftw_real tmp773;
	       fftw_real tmp782;
	       fftw_real tmp789;
	       fftw_real tmp769;
	       fftw_real tmp772;
	       fftw_real tmp785;
	       fftw_real tmp788;
	       ASSERT_ALIGNED_DOUBLE;
	       {
		    fftw_real tmp743;
		    fftw_real tmp746;
		    fftw_real tmp1137;
		    fftw_real tmp1140;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp743 = tmp655 + tmp658;
		    tmp746 = tmp744 + tmp745;
		    tmp747 = tmp743 - tmp746;
		    tmp775 = tmp743 + tmp746;
		    tmp1137 = tmp665 + tmp662;
		    tmp1140 = tmp1138 + tmp1139;
		    tmp1141 = tmp1137 + tmp1140;
		    tmp1147 = tmp1140 - tmp1137;
	       }
	       {
		    fftw_real tmp750;
		    fftw_real tmp777;
		    fftw_real tmp753;
		    fftw_real tmp776;
		    ASSERT_ALIGNED_DOUBLE;
		    {
			 fftw_real tmp748;
			 fftw_real tmp749;
			 fftw_real tmp751;
			 fftw_real tmp752;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp748 = tmp668 + tmp669;
			 tmp749 = tmp671 + tmp672;
			 tmp750 = (K831469612 * tmp748) - (K555570233 * tmp749);
			 tmp777 = (K555570233 * tmp748) + (K831469612 * tmp749);
			 tmp751 = tmp675 + tmp676;
			 tmp752 = tmp678 + tmp679;
			 tmp753 = (K831469612 * tmp751) + (K555570233 * tmp752);
			 tmp776 = (K831469612 * tmp752) - (K555570233 * tmp751);
		    }
		    tmp754 = tmp750 - tmp753;
		    tmp1136 = tmp753 + tmp750;
		    tmp778 = tmp776 + tmp777;
		    tmp1146 = tmp776 - tmp777;
	       }
	       {
		    fftw_real tmp758;
		    fftw_real tmp780;
		    fftw_real tmp761;
		    fftw_real tmp781;
		    ASSERT_ALIGNED_DOUBLE;
		    {
			 fftw_real tmp756;
			 fftw_real tmp757;
			 fftw_real tmp759;
			 fftw_real tmp760;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp756 = tmp714 + tmp715;
			 tmp757 = tmp711 + tmp708;
			 tmp758 = tmp756 - tmp757;
			 tmp780 = tmp756 + tmp757;
			 tmp759 = tmp703 + tmp704;
			 tmp760 = tmp717 + tmp718;
			 tmp761 = tmp759 - tmp760;
			 tmp781 = tmp759 + tmp760;
		    }
		    tmp762 = (K471396736 * tmp758) - (K881921264 * tmp761);
		    tmp773 = (K881921264 * tmp758) + (K471396736 * tmp761);
		    tmp782 = (K956940335 * tmp780) - (K290284677 * tmp781);
		    tmp789 = (K290284677 * tmp780) + (K956940335 * tmp781);
	       }
	       {
		    fftw_real tmp765;
		    fftw_real tmp783;
		    fftw_real tmp768;
		    fftw_real tmp784;
		    ASSERT_ALIGNED_DOUBLE;
		    {
			 fftw_real tmp763;
			 fftw_real tmp764;
			 fftw_real tmp766;
			 fftw_real tmp767;
			 ASSERT_ALIGNED_DOUBLE;
			 tmp763 = tmp695 + tmp696;
			 tmp764 = tmp692 + tmp689;
			 tmp765 = tmp763 - tmp764;
			 tmp783 = tmp763 + tmp764;
			 tmp766 = tmp684 + tmp685;
			 tmp767 = tmp698 + tmp699;
			 tmp768 = tmp766 - tmp767;
			 tmp784 = tmp766 + tmp767;
		    }
		    tmp769 = (K471396736 * tmp765) + (K881921264 * tmp768);
		    tmp772 = (K471396736 * tmp768) - (K881921264 * tmp765);
		    tmp785 = (K956940335 * tmp783) + (K290284677 * tmp784);
		    tmp788 = (K956940335 * tmp784) - (K290284677 * tmp783);
	       }
	       {
		    fftw_real tmp755;
		    fftw_real tmp770;
		    fftw_real tmp771;
		    fftw_real tmp774;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp755 = tmp747 - tmp754;
		    tmp770 = tmp762 - tmp769;
		    c_re(inout[59 * iostride]) = tmp755 - tmp770;
		    c_re(inout[27 * iostride]) = tmp755 + tmp770;
		    tmp771 = tmp747 + tmp754;
		    tmp774 = tmp772 + tmp773;
		    c_re(inout[43 * iostride]) = tmp771 - tmp774;
		    c_re(inout[11 * iostride]) = tmp771 + tmp774;
	       }
	       {
		    fftw_real tmp1149;
		    fftw_real tmp1150;
		    fftw_real tmp1145;
		    fftw_real tmp1148;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp1149 = tmp772 - tmp773;
		    tmp1150 = tmp1147 - tmp1146;
		    c_im(inout[27 * iostride]) = tmp1149 + tmp1150;
		    c_im(inout[59 * iostride]) = tmp1150 - tmp1149;
		    tmp1145 = tmp769 + tmp762;
		    tmp1148 = tmp1146 + tmp1147;
		    c_im(inout[11 * iostride]) = tmp1145 + tmp1148;
		    c_im(inout[43 * iostride]) = tmp1148 - tmp1145;
	       }
	       {
		    fftw_real tmp779;
		    fftw_real tmp786;
		    fftw_real tmp787;
		    fftw_real tmp790;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp779 = tmp775 - tmp778;
		    tmp786 = tmp782 - tmp785;
		    c_re(inout[51 * iostride]) = tmp779 - tmp786;
		    c_re(inout[19 * iostride]) = tmp779 + tmp786;
		    tmp787 = tmp775 + tmp778;
		    tmp790 = tmp788 + tmp789;
		    c_re(inout[35 * iostride]) = tmp787 - tmp790;
		    c_re(inout[3 * iostride]) = tmp787 + tmp790;
	       }
	       {
		    fftw_real tmp1143;
		    fftw_real tmp1144;
		    fftw_real tmp1135;
		    fftw_real tmp1142;
		    ASSERT_ALIGNED_DOUBLE;
		    tmp1143 = tmp788 - tmp789;
		    tmp1144 = tmp1141 - tmp1136;
		    c_im(inout[19 * iostride]) = tmp1143 + tmp1144;
		    c_im(inout[51 * iostride]) = tmp1144 - tmp1143;
		    tmp1135 = tmp785 + tmp782;
		    tmp1142 = tmp1136 + tmp1141;
		    c_im(inout[3 * iostride]) = tmp1135 + tmp1142;
		    c_im(inout[35 * iostride]) = tmp1142 - tmp1135;
	       }
	  }
     }
}

static const int twiddle_order[] =
{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63};
fftw_codelet_desc fftwi_twiddle_64_desc =
{
     "fftwi_twiddle_64",
     (void (*)()) fftwi_twiddle_64,
     64,
     FFTW_BACKWARD,
     FFTW_TWIDDLE,
     1419,
     63,
     twiddle_order,
};
