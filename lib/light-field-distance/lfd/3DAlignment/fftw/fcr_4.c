/*
 * Copyright (c) 1997-1999 Massachusetts Institute of Technology
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 *
 */

/* This file was automatically generated --- DO NOT EDIT */
/* Generated on Sun Nov  7 20:44:18 EST 1999 */

#include "fftw-int.h"
#include "fftw.h"

/* Generated by: ./genfft -magic-alignment-check -magic-twiddle-load-all -magic-variables 4 -magic-loopi -hc2real 4 */

/*
 * This function contains 6 FP additions, 2 FP multiplications,
 * (or, 6 additions, 2 multiplications, 0 fused multiply/add),
 * 8 stack variables, and 8 memory accesses
 */
static const fftw_real K2_000000000 = FFTW_KONST(+2.000000000000000000000000000000000000000000000);

/*
 * Generator Id's : 
 * $Id: exprdag.ml,v 1.41 1999/05/26 15:44:14 fftw Exp $
 * $Id: fft.ml,v 1.43 1999/05/17 19:44:18 fftw Exp $
 * $Id: to_c.ml,v 1.25 1999/10/26 21:41:32 stevenj Exp $
 */

void fftw_hc2real_4(const fftw_real *real_input, const fftw_real *imag_input, fftw_real *output, int real_istride, int imag_istride, int ostride)
{
     fftw_real tmp5;
     fftw_real tmp8;
     fftw_real tmp3;
     fftw_real tmp6;
     ASSERT_ALIGNED_DOUBLE;
     {
	  fftw_real tmp4;
	  fftw_real tmp7;
	  fftw_real tmp1;
	  fftw_real tmp2;
	  ASSERT_ALIGNED_DOUBLE;
	  tmp4 = real_input[real_istride];
	  tmp5 = K2_000000000 * tmp4;
	  tmp7 = imag_input[imag_istride];
	  tmp8 = K2_000000000 * tmp7;
	  tmp1 = real_input[0];
	  tmp2 = real_input[2 * real_istride];
	  tmp3 = tmp1 + tmp2;
	  tmp6 = tmp1 - tmp2;
     }
     output[2 * ostride] = tmp3 - tmp5;
     output[0] = tmp3 + tmp5;
     output[ostride] = tmp6 - tmp8;
     output[3 * ostride] = tmp6 + tmp8;
}

fftw_codelet_desc fftw_hc2real_4_desc =
{
     "fftw_hc2real_4",
     (void (*)()) fftw_hc2real_4,
     4,
     FFTW_BACKWARD,
     FFTW_HC2REAL,
     103,
     0,
     (const int *) 0,
};
