version: 1.0.{build}
image:
- Visual Studio 2017
- Visual Studio 2015
test: off
skip_branch_with_pr: true
build:
  parallel: true
platform:
- x64
- x86
environment:
  matrix:
  - PYTHON: 36
    CPP: 14
    CONFIG: Debug
  - PYTHON: 27
    CPP: 14
    CONFIG: Debug
  - CONDA: 36
    CPP: latest
    CONFIG: Release
matrix:
  exclude:
    - image: Visual Studio 2015
      platform: x86
    - image: Visual Studio 2015
      CPP: latest
    - image: Visual Studio 2017
      CPP: latest
      platform: x86
install:
- ps: |
    if ($env:PLATFORM -eq "x64") { $env:CMAKE_ARCH = "x64" }
    if ($env:APPVEYOR_JOB_NAME -like "*Visual Studio 2017*") {
      $env:CMAKE_GENERATOR = "Visual Studio 15 2017"
      $env:CMAKE_INCLUDE_PATH = "C:\Libraries\boost_1_64_0"
      $env:CXXFLAGS = "-permissive-"
    } else {
      $env:CMAKE_GENERATOR = "Visual Studio 14 2015"
    }
    if ($env:PYTH<PERSON>) {
      if ($env:PLATFORM -eq "x64") { $env:PYTHON = "$env:PYTHON-x64" }
      $env:PATH = "C:\Python$env:PYTHON\;C:\Python$env:PYTHON\Scripts\;$env:PATH"
      python -m pip install --upgrade pip wheel
      python -m pip install pytest numpy --no-warn-script-location
    } elseif ($env:CONDA) {
      if ($env:CONDA -eq "27") { $env:CONDA = "" }
      if ($env:PLATFORM -eq "x64") { $env:CONDA = "$env:CONDA-x64" }
      $env:PATH = "C:\Miniconda$env:CONDA\;C:\Miniconda$env:CONDA\Scripts\;$env:PATH"
      $env:PYTHONHOME = "C:\Miniconda$env:CONDA"
      conda update -y -n base conda
      conda install -y -q pytest numpy scipy
    }
- ps: |
    Start-FileDownload 'http://bitbucket.org/eigen/eigen/get/3.3.3.zip'
    7z x 3.3.3.zip -y > $null
    $env:CMAKE_INCLUDE_PATH = "eigen-eigen-67e894c6cd8f;$env:CMAKE_INCLUDE_PATH"
build_script:
- cmake -G "%CMAKE_GENERATOR%" -A "%CMAKE_ARCH%"
    -DPYBIND11_CPP_STANDARD=/std:c++%CPP%
    -DPYBIND11_WERROR=ON
    -DDOWNLOAD_CATCH=ON
    -DCMAKE_SUPPRESS_REGENERATION=1
- set MSBuildLogger="C:\Program Files\AppVeyor\BuildAgent\Appveyor.MSBuildLogger.dll"
- cmake --build . --config %CONFIG% --target pytest -- /m /v:m /logger:%MSBuildLogger%
- cmake --build . --config %CONFIG% --target cpptest -- /m /v:m /logger:%MSBuildLogger%
- if "%CPP%"=="latest" (cmake --build . --config %CONFIG% --target test_cmake_build -- /m /v:m /logger:%MSBuildLogger%)
on_failure: if exist "tests\test_cmake_build" type tests\test_cmake_build\*.log*
