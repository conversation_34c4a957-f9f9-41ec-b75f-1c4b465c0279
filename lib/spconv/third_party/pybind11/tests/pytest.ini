[pytest]
minversion = 3.0
norecursedirs = test_cmake_build test_embed
addopts =
    # show summary of skipped tests
    -rs
    # capture only Python print and C++ py::print, but not C output (low-level Python errors)
    --capture=sys
filterwarnings =
    # make warnings into errors but ignore certain third-party extension issues
    error
    # importing scipy submodules on some version of Python
    ignore::ImportWarning
    # bogus numpy ABI warning (see numpy/#432)
    ignore:.*numpy.dtype size changed.*:RuntimeWarning
